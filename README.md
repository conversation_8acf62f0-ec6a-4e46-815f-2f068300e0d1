# fs文件处理项目指南

## 项目概述
fs - 文件处理项目是一个文件处理服务，可对多种类型的文件进行处理，包括：
- 文档（PDF、Word、Excel、PowerPoint）
- 图像
- 音频/视频文件
- 文本文件

该服务具备以下功能：
- 文件转换（例如，将文档转换为Markdown格式）
- 文件解析与提取
- 文档批量处理
- 文件元数据提取
- 文件存储与检索

## 项目结构
本项目遵循标准的Spring Boot应用程序结构，包含以下关键包：

- `com.fxiaoke.file.process`
    - `annotation`：用于验证及其他用途的自定义注解
    - `client`：外部服务的客户端类
    - `config`：Spring Boot的配置类
    - `dao`：MongoDB的数据访问对象
    - `domain`：领域模型、实体及常量
        - `api`：API请求/响应模型
        - `constants`：常量与枚举
        - `entity`：数据库实体
        - `model`：内部领域模型
    - `exception`：自定义异常类
    - `mq`：RocketMQ的消息队列相关类
    - `service`：业务逻辑服务
        - `impl`：服务实现
        - `option`：服务选项与配置
    - `utils`：工具类
    - `validation`：自定义验证类
    - `web`：REST API的Web控制器

## 技术栈
- Java 21
- Spring Boot 3.x
- MongoDB用于数据存储
- RocketMQ用于消息传递
- Redis用于缓存
- Aspose用于文档处理（Word、PDF、Excel、PowerPoint）
- PDFBox用于PDF处理
- JAVE/FFmpeg用于音频/视频处理
- Thumbnailator用于图像处理
- 阿里云通义千问SDK用于人工智能功能

## 构建要求
本项目使用Maven进行构建管理。要构建项目，请执行：

```bash
mvn clean package
```

对于在配备M1芯片的Mac上进行开发，使用fstest - mac配置文件：

```bash
mvn clean package -Pfstest - mac
```

## 测试要求
本项目同时使用JUnit 4和JUnit 5进行测试。在实现新功能或修复错误时，务必做到：
1. 为新代码编写单元测试
2. 确保现有测试通过
3. 在提交更改前运行测试

要运行测试，请执行：

```bash
mvn test
```

## 代码风格指南
1. **Lombok使用**：本项目使用Lombok减少样板代码。lombok.config文件配置Lombok为生成的代码添加@Generated注解。
    - 对数据类使用`@Data`
    - 对日志记录使用`@Slf4j`
    - 对构造函数使用`@AllArgsConstructor`和`@NoArgsConstructor`

2. **异常处理**：使用`exception`包中的自定义异常。先捕获特定异常，再捕获通用异常。

3. **日志记录**：使用SLF4J进行日志记录，并采用适当的日志级别：
    - `log.error()`用于错误
    - `log.warn()`用于警告
    - `log.info()`用于重要信息
    - `log.debug()`用于调试信息

4. **文档说明**：为公共方法添加JavaDoc注释，解释：
    - 方法的用途
    - 参数
    - 返回值
    - 抛出的异常

5. **验证**：使用Jakarta Validation注解进行输入验证。

## 与AI-Coder协作
在本项目中与AI-Coder协作时：
1. 提供清晰的问题描述
2. 指明是否应运行测试以验证更改
3. 提及提交前是否应构建项目
4. 对于复杂更改，提供受影响组件的相关背景信息