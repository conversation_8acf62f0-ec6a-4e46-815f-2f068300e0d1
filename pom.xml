<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.fxiaoke.cloud</groupId>
    <artifactId>fxiaoke-spring-cloud-parent</artifactId>
    <version>3.0.0-SNAPSHOT</version>
    <relativePath/>
  </parent>

  <groupId>com.fxiaoke</groupId>
  <artifactId>fs-file-process</artifactId>
  <version>1.0-SNAPSHOT</version>
  <modelVersion>4.0.0</modelVersion>

  <packaging>jar</packaging>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
    <maven.compiler.source>21</maven.compiler.source>
    <maven.compiler.target>21</maven.compiler.target>
    <java.version>21</java.version>
    <jdk.version>21</jdk.version>
    <mockito.version>5.11.0</mockito.version>

    <tencentcloud-sdk.version>3.1.977</tencentcloud-sdk.version>
    <volcengine-java-sdk-ark-runtime.version>0.2.22</volcengine-java-sdk-ark-runtime.version>
    <flexmark.version>0.64.8</flexmark.version>
    <jai.version>1.4.0</jai.version>
    <jbig2-imageio.version>3.0.4</jbig2-imageio.version>
    <thumbnailator.version>0.4.20</thumbnailator.version>
    <jsoup.version>1.18.3</jsoup.version>
    <fs-paas-ai-api.version>1.0.2-SNAPSHOT</fs-paas-ai-api.version>
    <fs-s3-support.version>1.3-SNAPSHOT</fs-s3-support.version>
    <aspose.version>25.5</aspose.version>
    <jedis.version>3.8.0</jedis.version>
    <pdfbox.version>3.0.5</pdfbox.version>
    <junit.version>4.13.2</junit.version>
    <hutool-http.version>5.8.32</hutool-http.version>
    <fs-enterpriserelation-rest-api2.version>2.0.4-SNAPSHOT
    </fs-enterpriserelation-rest-api2.version>
    <jave2.version>3.5.0</jave2.version>
    <fsi-proxy.version>4.2.0-SNAPSHOT</fsi-proxy.version>
    <fs-stone-sdk.version>1.1-SNAPSHOT</fs-stone-sdk.version>
    <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
    <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
    <apache.poi.version>5.2.3</apache.poi.version>
    <apache.poi-ooxml-schemas.version>4.1.2</apache.poi-ooxml-schemas.version>
  </properties>

  <dependencies>
    <!-- SpringBoot核心依赖 必须 start-->
    <dependency>
      <artifactId>spring-boot-starter-web</artifactId>
      <groupId>org.springframework.boot</groupId>
    </dependency>
    <dependency>
      <artifactId>actuator-ext-spring-boot-starter</artifactId>
      <groupId>com.fxiaoke.boot</groupId>
    </dependency>
    <dependency>
      <artifactId>spring-boot-starter-aop</artifactId>
      <groupId>org.springframework.boot</groupId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>
    <!--SpringBoot核心依赖 必须 end-->

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <!--组件-SpringBoot 配置中心-->
    <dependency>
      <groupId>com.fxiaoke.cloud</groupId>
      <artifactId>cms-spring-cloud-starter</artifactId>
    </dependency>
    <!-- 日志自动上报 -->
    <dependency>
      <groupId>com.fxiaoke.boot</groupId>
      <artifactId>metrics-spring-boot-starter</artifactId>
    </dependency>
    <!-- 链路追踪过滤器 -->
    <dependency>
      <groupId>com.fxiaoke.boot</groupId>
      <artifactId>core-filter-spring-boot-starter</artifactId>
    </dependency>

    <!--组件-SpringBoot MongoDB-->
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>mongo-spring-support</artifactId>
    </dependency>
    <!--组件 Redis-->
    <dependency>
      <groupId>redis.clients</groupId>
      <artifactId>jedis</artifactId>
      <version>${jedis.version}</version>
    </dependency>

    <!--组件-Spring RocketMQ-->
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-rocketmq-support</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.apache.tomcat</groupId>
          <artifactId>annotations-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.j2objc</groupId>
          <artifactId>j2objc-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.luben</groupId>
          <artifactId>zstd-jni</artifactId>
        </exclusion>
        <exclusion>
          <groupId>commons-logging</groupId>
          <artifactId>commons-logging</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-client</artifactId>
    </dependency>

    <!-- FileSystem Client -->
    <dependency>
      <artifactId>fs-fsi-proxy</artifactId>
      <groupId>com.facishare</groupId>
      <version>${fsi-proxy.version}</version>
      <exclusions>
        <exclusion>
          <groupId>commons-logging</groupId>
          <artifactId>commons-logging</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-stone-sdk</artifactId>
      <version>${fs-stone-sdk.version}</version>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-stone-commons-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-s3-support</artifactId>
      <version>${fs-s3-support.version}</version>
      <exclusions>
        <exclusion>
          <groupId>commons-logging</groupId>
          <artifactId>commons-logging</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!--企业账号、ID转换 -->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-enterprise-id-account-converter</artifactId>
    </dependency>
    <!--算粒上报-->
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-paas-ai-api</artifactId>
      <version>${fs-paas-ai-api.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.jetbrains</groupId>
          <artifactId>annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.mongodb</groupId>
          <artifactId>mongo-java-driver</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <!--公司组件-end-->

    <!--第三方组件-start-->

    <dependency>
      <groupId>com.volcengine</groupId>
      <artifactId>volcengine-java-sdk-ark-runtime</artifactId>
      <version>${volcengine-java-sdk-ark-runtime.version}</version>
    </dependency>

    <dependency>
      <groupId>com.tencentcloudapi</groupId>
      <artifactId>tencentcloud-sdk-java-asr</artifactId>
      <version>${tencentcloud-sdk.version}</version>
      <exclusions>
        <exclusion>
          <groupId>commons-logging</groupId>
          <artifactId>commons-logging</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <!--Aspose 文档控件 start-->
    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-words</artifactId>
      <version>${aspose.version}</version>
      <classifier>jdk17</classifier>
    </dependency>
    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-words</artifactId>
      <version>${aspose.version}</version>
      <classifier>javadoc</classifier>
    </dependency>

    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-slides</artifactId>
      <version>${aspose.version}</version>
      <classifier>jdk16</classifier>
    </dependency>
    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-slides</artifactId>
      <version>${aspose.version}</version>
      <classifier>javadoc</classifier>
    </dependency>

    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-pdf</artifactId>
      <version>${aspose.version}</version>
      <classifier>jdk17</classifier>
    </dependency>
    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-pdf</artifactId>
      <version>${aspose.version}</version>
      <classifier>javadoc</classifier>
    </dependency>

    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-cells</artifactId>
      <version>${aspose.version}</version>
    </dependency>
    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-cells</artifactId>
      <version>${aspose.version}</version>
      <classifier>javadoc</classifier>
    </dependency>

    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-html</artifactId>
      <version>${aspose.version}</version>
      <classifier>jdk17</classifier>
    </dependency>

    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-html</artifactId>
      <version>${aspose.version}</version>
      <classifier>javadoc</classifier>
    </dependency>
    <!--Aspose 文档控件 end-->

    <!--HTML DOM解析 start-->
    <dependency>
      <groupId>org.jsoup</groupId>
      <artifactId>jsoup</artifactId>
      <version>${jsoup.version}</version>
    </dependency>

    <dependency>
      <groupId>com.vladsch.flexmark</groupId>
      <artifactId>flexmark</artifactId>
      <version>${flexmark.version}</version>
    </dependency>
    <dependency>
      <groupId>com.vladsch.flexmark</groupId>
      <artifactId>flexmark-html2md-converter</artifactId>
      <version>${flexmark.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.jsoup</groupId>
          <artifactId>jsoup</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!--HTML DOM解析 end-->

    <!--Pdfbox 文档控件 start-->
    <dependency>
      <groupId>org.apache.pdfbox</groupId>
      <artifactId>pdfbox</artifactId>
      <version>${pdfbox.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.pdfbox</groupId>
      <artifactId>pdfbox-io</artifactId>
      <version>${pdfbox.version}</version>
    </dependency>
    <!--Pdfbox 文档控件 end-->

    <!--Jai 高级图像转换 start-->
    <dependency>
      <artifactId>jai-imageio-core</artifactId>
      <groupId>com.github.jai-imageio</groupId>
      <version>${jai.version}</version>
    </dependency>
    <dependency>
      <artifactId>jai-imageio-jpeg2000</artifactId>
      <groupId>com.github.jai-imageio</groupId>
      <version>${jai.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.pdfbox</groupId>
      <artifactId>jbig2-imageio</artifactId>
      <version>${jbig2-imageio.version}</version>
    </dependency>
    <!--Jai 高级图像转换 end-->

    <!--common 通用工具类 start-->
    <dependency>
      <artifactId>commons-io</artifactId>
      <groupId>commons-io</groupId>
    </dependency>
    <!--common 通用工具类 end-->

    <!--通用工具类-->
    <!-- MapStruct 依赖 -->
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
      <version>${org.mapstruct.version}</version>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>${junit.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>RELEASE</version>
      <scope>test</scope>
    </dependency>
    <!--第三方组件 按需引入 非必须 end-->

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>${mockito.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <version>${mockito.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-inline</artifactId>
      <version>5.2.0</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <!--添加打包插件以便支持jar模式运行-->
  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven-compiler-plugin.version}</version>
        <configuration>
          <source>${maven.compiler.source}</source>
          <target>${maven.compiler.target}</target>
          <annotationProcessorPaths>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok-mapstruct-binding</artifactId>
              <version>${lombok-mapstruct-binding.version}</version>
            </path>
            <path>
              <groupId>org.mapstruct</groupId>
              <artifactId>mapstruct-processor</artifactId>
              <version>${org.mapstruct.version}</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>