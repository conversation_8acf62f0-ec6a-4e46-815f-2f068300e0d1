package com.fxiaoke.file.process;

import com.alibaba.fastjson.JSON;
import com.facishare.uc.api.util.MD5Util;
import com.tencentcloudapi.asr.v20190614.AsrClient;
import com.tencentcloudapi.asr.v20190614.models.CreateRecTaskRequest;
import com.tencentcloudapi.asr.v20190614.models.CreateRecTaskResponse;
import com.tencentcloudapi.asr.v20190614.models.DescribeTaskStatusRequest;
import com.tencentcloudapi.asr.v20190614.models.DescribeTaskStatusResponse;
import com.tencentcloudapi.asr.v20190614.models.Task;
import com.tencentcloudapi.asr.v20190614.models.TaskStatus;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import java.net.URI;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;

@Slf4j
public class AsrService {

  // Set 音频数据来源 0：音频URL； 1：音频数据（post body）
  private static final Long SOURCE_TYPE_URL = 0L;
  private static final Long SOURCE_TYPE_DATA = 1L;
  private static final String defaultRecEngine = "16k_zh";
  public static final Integer CHANNEL_NUM_ONE = 1;
  public static final Integer SpeakerDiarization = 0;
  public static final Integer SpeakerNumber = 0;
  public static final Integer ResTextFormat = 3;

  private final String secretId = "AKID2P8EGNTbcValvy8IezHTqbYUPExbFvgV";
  private final String secretKey = "xz8LSekEhSJfyRtNwXRIHMpbxFVjvTsH";
  private final String endpoint = "asr.tencentcloudapi.com";
  private final String proxy = "************:30002";
  private final String region = "ap-beijing";
  private final String url = "https://fs-file-process.oss-cn-beijing.aliyuncs.com/asr/24%E5%B9%B4%E7%89%88AI-PaaS%E5%8A%A9%E5%8A%9B%E6%89%93%E9%80%A0%E4%BC%81%E4%B8%9A%E7%BA%A7AI%E4%B8%93%E5%B1%9E%E5%BA%94%E7%94%A8.mp3?OSSAccessKeyId=LTAI5tD5TDYMYJXRik2ywYZ4&Expires=1753349200&Signature=mAr62EyEmszOxOUg0aGb%2FGge%2BRw%3D";

  public static URI NetUtilsUri(String address) {
    // http://xxxx:port
    if (!address.contains("//")) {
      // 补充前面的协议，这样才能正常解析成一个URI
      address = "http://" + address;
    }
    return URI.create(address.trim());
  }


  public AsrClient createAsrClient() {
    Credential cred = new Credential(secretId, secretKey);
    ClientProfile clientProfile = new ClientProfile();
    if (StringUtils.isNotBlank(proxy)) {
      HttpProfile httpProfile = new HttpProfile();
      URI uri = NetUtilsUri(proxy);
      httpProfile.setEndpoint(endpoint);
      httpProfile.setProxyHost(uri.getHost());
      httpProfile.setProxyPort(uri.getPort());
      clientProfile.setHttpProfile(httpProfile);
    }
    return new AsrClient(cred, region, clientProfile);
  }

  @Test
  public void createAsrRecTask() {

    AsrClient client = createAsrClient();

    CreateRecTaskRequest req = new CreateRecTaskRequest();
    req.setSourceType(SOURCE_TYPE_URL);
    req.setUrl(url);
    req.setEngineModelType(defaultRecEngine);

    req.setChannelNum(Long.valueOf(CHANNEL_NUM_ONE));
    // 双声道时这几个只能用默认值，否则腾讯会报400错误
    // 是否开启说话人分离 0：不开启；
    req.setSpeakerDiarization(Long.valueOf(SpeakerDiarization));
    // 说话人分离人数, 需配合开启说话人分离使用，不开启无效，取值范围：0-10，0：自动分离（最多分离出20个人）
    req.setSpeakerNumber(Long.valueOf(SpeakerNumber));

    // 识别结果返回样式 0：基础识别结果（仅包含有效人声时间戳，无词粒度的详细识别结果）
    req.setResTextFormat(Long.valueOf(ResTextFormat));
    // 生成随机jobId
    String jobId = String.valueOf(System.currentTimeMillis());
    // 对jobid进行签名作为sign
    String sign = MD5Util.getMD5(jobId);
    req.setCallbackUrl(
        "https://img.ceshi112.com/fs-file-process/FileProcess/tencent/asr/callback?jobId=" + jobId +
            "&sign=" + sign);

    try {
      CreateRecTaskResponse rsp = client.CreateRecTask(req);
      String requestId = rsp.getRequestId();
      Task task = rsp.getData();
      Long taskId = task.getTaskId();
      System.out.println("RequestId: " + requestId);
      System.out.println("TaskId: " + taskId);
    } catch (TencentCloudSDKException e) {
      throw new RuntimeException(e);
    }
  }

  @Test
  public void queryAsrRecTask() {
    DescribeTaskStatusRequest req = new DescribeTaskStatusRequest();
    req.setTaskId(12672235845L); // 替换为实际的任务ID
    AsrClient client = createAsrClient();
    try {
      DescribeTaskStatusResponse response = client.DescribeTaskStatus(req);
      TaskStatus taskStatus = response.getData();
      System.out.println("TaskStatus: " + JSON.toJSONString(response));
    } catch (TencentCloudSDKException e) {
      throw new RuntimeException(e);
    }
  }
}
