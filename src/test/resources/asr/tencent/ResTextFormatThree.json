{"appid": 1251989944, "audioTime": 232.199563, "audioUrl": "https://fs-file-process.oss-cn-beijing.aliyuncs.com/asr/%E8%BF%88%E8%BF%9B%E6%96%B0%E6%97%B6%E4%BB%A3.mp3?OSSAccessKeyId=LTAI5tD5TDYMYJXRik2ywYZ4&Expires=1754148021&Signature=D830Vp64P9CG4dPdxU9FmcskrPQ%3D", "code": 0, "errorDescription": "成功", "message": "", "projectid": 0, "requestId": 12704918660, "resultDetail": [{"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 19020, "finalSentence": "我们想改变什么？", "silenceTime": 0, "sliceSentence": "我们 想 改变 什么 ？", "speakerId": 0, "speechSpeed": 0.6, "startMs": 7540, "words": [{"offsetEndMs": 10635, "offsetStartMs": 10300, "word": "我们"}, {"offsetEndMs": 10800, "offsetStartMs": 10635, "word": "想"}, {"offsetEndMs": 11100, "offsetStartMs": 10800, "word": "改变"}, {"offsetEndMs": 11480, "offsetStartMs": 11100, "word": "什么"}, {"offsetEndMs": 11480, "offsetStartMs": 11100, "word": "？"}], "wordsNum": 5}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 21445, "finalSentence": "我们的愿景是什么样？", "silenceTime": 1040, "sliceSentence": "我们 的 愿景 是 什么样 ？", "speakerId": 0, "speechSpeed": 6.5, "startMs": 20060, "words": [{"offsetEndMs": 335, "offsetStartMs": 0, "word": "我们"}, {"offsetEndMs": 455, "offsetStartMs": 335, "word": "的"}, {"offsetEndMs": 740, "offsetStartMs": 455, "word": "愿景"}, {"offsetEndMs": 875, "offsetStartMs": 740, "word": "是"}, {"offsetEndMs": 1385, "offsetStartMs": 875, "word": "什么样"}, {"offsetEndMs": 1385, "offsetStartMs": 875, "word": "？"}], "wordsNum": 6}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 23250, "finalSentence": "是什么驱使我们做这件事情，", "silenceTime": 0, "sliceSentence": "是 什么 驱使 我们 做 这件 事情 ，", "speakerId": 0, "speechSpeed": 6.6, "startMs": 21445, "words": [{"offsetEndMs": 285, "offsetStartMs": 0, "word": "是"}, {"offsetEndMs": 570, "offsetStartMs": 285, "word": "什么"}, {"offsetEndMs": 885, "offsetStartMs": 570, "word": "驱使"}, {"offsetEndMs": 1080, "offsetStartMs": 885, "word": "我们"}, {"offsetEndMs": 1200, "offsetStartMs": 1080, "word": "做"}, {"offsetEndMs": 1395, "offsetStartMs": 1200, "word": "这件"}, {"offsetEndMs": 1805, "offsetStartMs": 1395, "word": "事情"}, {"offsetEndMs": 1805, "offsetStartMs": 1395, "word": "，"}], "wordsNum": 8}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 31950, "finalSentence": "一定要去思考我们为用户，", "silenceTime": 6140, "sliceSentence": "一定 要 去思考 我们 为 用户 ，", "speakerId": 0, "speechSpeed": 4.3, "startMs": 29390, "words": [{"offsetEndMs": 395, "offsetStartMs": 0, "word": "一定"}, {"offsetEndMs": 515, "offsetStartMs": 395, "word": "要"}, {"offsetEndMs": 1040, "offsetStartMs": 515, "word": "去思考"}, {"offsetEndMs": 1400, "offsetStartMs": 1040, "word": "我们"}, {"offsetEndMs": 1720, "offsetStartMs": 1400, "word": "为"}, {"offsetEndMs": 2560, "offsetStartMs": 1860, "word": "用户"}, {"offsetEndMs": 2560, "offsetStartMs": 1860, "word": "，"}], "wordsNum": 7}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 33390, "finalSentence": "我们为这个时代，", "silenceTime": 200, "sliceSentence": "我们 为 这个时代 ，", "speakerId": 0, "speechSpeed": 5.6, "startMs": 32150, "words": [{"offsetEndMs": 365, "offsetStartMs": 0, "word": "我们"}, {"offsetEndMs": 515, "offsetStartMs": 365, "word": "为"}, {"offsetEndMs": 1240, "offsetStartMs": 515, "word": "这个时代"}, {"offsetEndMs": 1240, "offsetStartMs": 515, "word": "，"}], "wordsNum": 4}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 34980, "finalSentence": "为这个商业和社会，", "silenceTime": 110, "sliceSentence": "为 这个 商业 和 社会 ，", "speakerId": 0, "speechSpeed": 5.4, "startMs": 33500, "words": [{"offsetEndMs": 260, "offsetStartMs": 0, "word": "为"}, {"offsetEndMs": 485, "offsetStartMs": 260, "word": "这个"}, {"offsetEndMs": 860, "offsetStartMs": 485, "word": "商业"}, {"offsetEndMs": 1040, "offsetStartMs": 860, "word": "和"}, {"offsetEndMs": 1480, "offsetStartMs": 1040, "word": "社会"}, {"offsetEndMs": 1480, "offsetStartMs": 1040, "word": "，"}], "wordsNum": 6}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 37260, "finalSentence": "真真正正创造了什么价值。", "silenceTime": 170, "sliceSentence": "真真正正 创造 了 什么 价值 。", "speakerId": 0, "speechSpeed": 5.2, "startMs": 35150, "words": [{"offsetEndMs": 860, "offsetStartMs": 0, "word": "真真正正"}, {"offsetEndMs": 1220, "offsetStartMs": 860, "word": "创造"}, {"offsetEndMs": 1325, "offsetStartMs": 1220, "word": "了"}, {"offsetEndMs": 1565, "offsetStartMs": 1325, "word": "什么"}, {"offsetEndMs": 2110, "offsetStartMs": 1565, "word": "价值"}, {"offsetEndMs": 2110, "offsetStartMs": 1565, "word": "。"}], "wordsNum": 6}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 53760, "finalSentence": "商业环境存在着巨大的不确定性，", "silenceTime": 9700, "sliceSentence": "商业 环境 存在着 巨大 的 不确定 性 ，", "speakerId": 0, "speechSpeed": 2.1, "startMs": 46960, "words": [{"offsetEndMs": 4320, "offsetStartMs": 3850, "word": "商业"}, {"offsetEndMs": 4820, "offsetStartMs": 4320, "word": "环境"}, {"offsetEndMs": 5625, "offsetStartMs": 5050, "word": "存在着"}, {"offsetEndMs": 5895, "offsetStartMs": 5625, "word": "巨大"}, {"offsetEndMs": 6015, "offsetStartMs": 5895, "word": "的"}, {"offsetEndMs": 6510, "offsetStartMs": 6015, "word": "不确定"}, {"offsetEndMs": 6800, "offsetStartMs": 6510, "word": "性"}, {"offsetEndMs": 6800, "offsetStartMs": 6510, "word": "，"}], "wordsNum": 8}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 56700, "finalSentence": "尤其进入到移动互联时代，", "silenceTime": 920, "sliceSentence": "尤其 进入 到 移动互联 时代 ，", "speakerId": 0, "speechSpeed": 5.4, "startMs": 54680, "words": [{"offsetEndMs": 410, "offsetStartMs": 0, "word": "尤其"}, {"offsetEndMs": 725, "offsetStartMs": 410, "word": "进入"}, {"offsetEndMs": 920, "offsetStartMs": 725, "word": "到"}, {"offsetEndMs": 1580, "offsetStartMs": 920, "word": "移动互联"}, {"offsetEndMs": 2020, "offsetStartMs": 1580, "word": "时代"}, {"offsetEndMs": 2020, "offsetStartMs": 1580, "word": "，"}], "wordsNum": 6}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 59950, "finalSentence": "连接催动着一切进行加速。", "silenceTime": 1010, "sliceSentence": "连接 催动 着 一切 进行 加速 。", "speakerId": 0, "speechSpeed": 4.9, "startMs": 57710, "words": [{"offsetEndMs": 500, "offsetStartMs": 0, "word": "连接"}, {"offsetEndMs": 800, "offsetStartMs": 500, "word": "催动"}, {"offsetEndMs": 935, "offsetStartMs": 800, "word": "着"}, {"offsetEndMs": 1450, "offsetStartMs": 935, "word": "一切"}, {"offsetEndMs": 1910, "offsetStartMs": 1470, "word": "进行"}, {"offsetEndMs": 2240, "offsetStartMs": 1910, "word": "加速"}, {"offsetEndMs": 2240, "offsetStartMs": 1910, "word": "。"}], "wordsNum": 7}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 64290, "finalSentence": "No又初出茅庐的新兴企业快速崛起，", "silenceTime": 0, "sliceSentence": "No 又 初出茅庐 的 新兴 企业 快速 崛起 ，", "speakerId": 0, "speechSpeed": 3.7, "startMs": 59950, "words": [{"offsetEndMs": 230, "offsetStartMs": 0, "word": "No"}, {"offsetEndMs": 1425, "offsetStartMs": 1120, "word": "又"}, {"offsetEndMs": 2115, "offsetStartMs": 1425, "word": "初出茅庐"}, {"offsetEndMs": 2235, "offsetStartMs": 2115, "word": "的"}, {"offsetEndMs": 2610, "offsetStartMs": 2235, "word": "新兴"}, {"offsetEndMs": 3110, "offsetStartMs": 2610, "word": "企业"}, {"offsetEndMs": 3810, "offsetStartMs": 3310, "word": "快速"}, {"offsetEndMs": 4340, "offsetStartMs": 3810, "word": "崛起"}, {"offsetEndMs": 4340, "offsetStartMs": 3810, "word": "，"}], "wordsNum": 9}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 68340, "finalSentence": "也有经历百年的公司瞬势轰然倒塌。", "silenceTime": 530, "sliceSentence": "也有 经历 百年 的 公司 瞬 势 轰然 倒塌 。", "speakerId": 0, "speechSpeed": 4.3, "startMs": 64820, "words": [{"offsetEndMs": 440, "offsetStartMs": 0, "word": "也有"}, {"offsetEndMs": 800, "offsetStartMs": 440, "word": "经历"}, {"offsetEndMs": 1130, "offsetStartMs": 800, "word": "百年"}, {"offsetEndMs": 1265, "offsetStartMs": 1130, "word": "的"}, {"offsetEndMs": 1655, "offsetStartMs": 1265, "word": "公司"}, {"offsetEndMs": 1865, "offsetStartMs": 1655, "word": "瞬"}, {"offsetEndMs": 2170, "offsetStartMs": 1865, "word": "势"}, {"offsetEndMs": 3020, "offsetStartMs": 2550, "word": "轰然"}, {"offsetEndMs": 3520, "offsetStartMs": 3020, "word": "倒塌"}, {"offsetEndMs": 3520, "offsetStartMs": 3020, "word": "。"}], "wordsNum": 10}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 75180, "finalSentence": "现在企业的经营管理已经发生了质的变化，", "silenceTime": 3620, "sliceSentence": "现在 企业 的 经营 管理 已经 发生了 质 的 变化 ，", "speakerId": 0, "speechSpeed": 5.6, "startMs": 71960, "words": [{"offsetEndMs": 500, "offsetStartMs": 0, "word": "现在"}, {"offsetEndMs": 755, "offsetStartMs": 500, "word": "企业"}, {"offsetEndMs": 875, "offsetStartMs": 755, "word": "的"}, {"offsetEndMs": 1160, "offsetStartMs": 875, "word": "经营"}, {"offsetEndMs": 1550, "offsetStartMs": 1160, "word": "管理"}, {"offsetEndMs": 1895, "offsetStartMs": 1550, "word": "已经"}, {"offsetEndMs": 2345, "offsetStartMs": 1895, "word": "发生了"}, {"offsetEndMs": 2510, "offsetStartMs": 2345, "word": "质"}, {"offsetEndMs": 2675, "offsetStartMs": 2510, "word": "的"}, {"offsetEndMs": 3220, "offsetStartMs": 2675, "word": "变化"}, {"offsetEndMs": 3220, "offsetStartMs": 2675, "word": "，"}], "wordsNum": 11}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 80010, "finalSentence": "从原来以产品为中心转变为以用户为中心，", "silenceTime": 890, "sliceSentence": "从 原来 以 产品 为 中心 转变为 以 用户 为 中心 ，", "speakerId": 0, "speechSpeed": 4.6, "startMs": 76070, "words": [{"offsetEndMs": 275, "offsetStartMs": 0, "word": "从"}, {"offsetEndMs": 590, "offsetStartMs": 275, "word": "原来"}, {"offsetEndMs": 845, "offsetStartMs": 590, "word": "以"}, {"offsetEndMs": 1250, "offsetStartMs": 845, "word": "产品"}, {"offsetEndMs": 1385, "offsetStartMs": 1250, "word": "为"}, {"offsetEndMs": 1840, "offsetStartMs": 1385, "word": "中心"}, {"offsetEndMs": 2570, "offsetStartMs": 1980, "word": "转变为"}, {"offsetEndMs": 2855, "offsetStartMs": 2570, "word": "以"}, {"offsetEndMs": 3320, "offsetStartMs": 2855, "word": "用户"}, {"offsetEndMs": 3470, "offsetStartMs": 3320, "word": "为"}, {"offsetEndMs": 3940, "offsetStartMs": 3470, "word": "中心"}, {"offsetEndMs": 3940, "offsetStartMs": 3470, "word": "，"}], "wordsNum": 12}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 84060, "finalSentence": "这不仅仅是基于飞享逍客这家企业经营的本身，", "silenceTime": 710, "sliceSentence": "这不 仅仅 是 基于 飞 享 逍客 这家 企业 经营 的 本身 ，", "speakerId": 0, "speechSpeed": 6.0, "startMs": 80720, "words": [{"offsetEndMs": 395, "offsetStartMs": 0, "word": "这不"}, {"offsetEndMs": 635, "offsetStartMs": 395, "word": "仅仅"}, {"offsetEndMs": 800, "offsetStartMs": 635, "word": "是"}, {"offsetEndMs": 1085, "offsetStartMs": 800, "word": "基于"}, {"offsetEndMs": 1220, "offsetStartMs": 1085, "word": "飞"}, {"offsetEndMs": 1355, "offsetStartMs": 1220, "word": "享"}, {"offsetEndMs": 1640, "offsetStartMs": 1355, "word": "逍客"}, {"offsetEndMs": 2000, "offsetStartMs": 1640, "word": "这家"}, {"offsetEndMs": 2405, "offsetStartMs": 2000, "word": "企业"}, {"offsetEndMs": 2675, "offsetStartMs": 2405, "word": "经营"}, {"offsetEndMs": 2810, "offsetStartMs": 2675, "word": "的"}, {"offsetEndMs": 3340, "offsetStartMs": 2810, "word": "本身"}, {"offsetEndMs": 3340, "offsetStartMs": 2810, "word": "，"}], "wordsNum": 13}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 87000, "finalSentence": "还有飞享销客60万家企业用户，", "silenceTime": 500, "sliceSentence": "还有 飞 享 销 客 60 万家 企业 用户 ，", "speakerId": 0, "speechSpeed": 5.7, "startMs": 84560, "words": [{"offsetEndMs": 395, "offsetStartMs": 0, "word": "还有"}, {"offsetEndMs": 530, "offsetStartMs": 395, "word": "飞"}, {"offsetEndMs": 665, "offsetStartMs": 530, "word": "享"}, {"offsetEndMs": 830, "offsetStartMs": 665, "word": "销"}, {"offsetEndMs": 1025, "offsetStartMs": 830, "word": "客"}, {"offsetEndMs": 1325, "offsetStartMs": 1025, "word": "60"}, {"offsetEndMs": 1655, "offsetStartMs": 1325, "word": "万家"}, {"offsetEndMs": 1955, "offsetStartMs": 1655, "word": "企业"}, {"offsetEndMs": 2440, "offsetStartMs": 1955, "word": "用户"}, {"offsetEndMs": 2440, "offsetStartMs": 1955, "word": "，"}], "wordsNum": 10}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 90990, "finalSentence": "如何借助飞享销客实现企业数字化转型，", "silenceTime": 440, "sliceSentence": "如何 借助 飞 享 销 客 实现 企业 数字化 转型 ，", "speakerId": 0, "speechSpeed": 4.8, "startMs": 87440, "words": [{"offsetEndMs": 440, "offsetStartMs": 0, "word": "如何"}, {"offsetEndMs": 875, "offsetStartMs": 440, "word": "借助"}, {"offsetEndMs": 1070, "offsetStartMs": 875, "word": "飞"}, {"offsetEndMs": 1205, "offsetStartMs": 1070, "word": "享"}, {"offsetEndMs": 1370, "offsetStartMs": 1205, "word": "销"}, {"offsetEndMs": 1660, "offsetStartMs": 1370, "word": "客"}, {"offsetEndMs": 2240, "offsetStartMs": 1740, "word": "实现"}, {"offsetEndMs": 2570, "offsetStartMs": 2240, "word": "企业"}, {"offsetEndMs": 3020, "offsetStartMs": 2570, "word": "数字化"}, {"offsetEndMs": 3550, "offsetStartMs": 3020, "word": "转型"}, {"offsetEndMs": 3550, "offsetStartMs": 3020, "word": "，"}], "wordsNum": 11}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 93990, "finalSentence": "这是我一直思考与探索的问题。", "silenceTime": 440, "sliceSentence": "这是 我 一直 思考 与 探索 的 问题 。", "speakerId": 0, "speechSpeed": 5.1, "startMs": 91430, "words": [{"offsetEndMs": 410, "offsetStartMs": 0, "word": "这是"}, {"offsetEndMs": 560, "offsetStartMs": 410, "word": "我"}, {"offsetEndMs": 905, "offsetStartMs": 560, "word": "一直"}, {"offsetEndMs": 1340, "offsetStartMs": 905, "word": "思考"}, {"offsetEndMs": 1610, "offsetStartMs": 1340, "word": "与"}, {"offsetEndMs": 1985, "offsetStartMs": 1610, "word": "探索"}, {"offsetEndMs": 2105, "offsetStartMs": 1985, "word": "的"}, {"offsetEndMs": 2560, "offsetStartMs": 2105, "word": "问题"}, {"offsetEndMs": 2560, "offsetStartMs": 2105, "word": "。"}], "wordsNum": 9}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 99660, "finalSentence": "移动互联网时代未来的企业什么样的企业？", "silenceTime": 2990, "sliceSentence": "移动互联 网 时代 未来 的 企业 什么样 的 企业 ？", "speakerId": 0, "speechSpeed": 6.7, "startMs": 96980, "words": [{"offsetEndMs": 665, "offsetStartMs": 0, "word": "移动互联"}, {"offsetEndMs": 785, "offsetStartMs": 665, "word": "网"}, {"offsetEndMs": 1210, "offsetStartMs": 785, "word": "时代"}, {"offsetEndMs": 1655, "offsetStartMs": 1320, "word": "未来"}, {"offsetEndMs": 1790, "offsetStartMs": 1655, "word": "的"}, {"offsetEndMs": 2030, "offsetStartMs": 1790, "word": "企业"}, {"offsetEndMs": 2255, "offsetStartMs": 2030, "word": "什么样"}, {"offsetEndMs": 2330, "offsetStartMs": 2255, "word": "的"}, {"offsetEndMs": 2680, "offsetStartMs": 2330, "word": "企业"}, {"offsetEndMs": 2680, "offsetStartMs": 2330, "word": "？"}], "wordsNum": 10}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 101515, "finalSentence": "一定是一些连接型的企业，", "silenceTime": 50, "sliceSentence": "一定 是 一些 连接 型 的 企业 ，", "speakerId": 0, "speechSpeed": 6.1, "startMs": 99710, "words": [{"offsetEndMs": 410, "offsetStartMs": 0, "word": "一定"}, {"offsetEndMs": 515, "offsetStartMs": 410, "word": "是"}, {"offsetEndMs": 830, "offsetStartMs": 515, "word": "一些"}, {"offsetEndMs": 1190, "offsetStartMs": 830, "word": "连接"}, {"offsetEndMs": 1295, "offsetStartMs": 1190, "word": "型"}, {"offsetEndMs": 1430, "offsetStartMs": 1295, "word": "的"}, {"offsetEndMs": 1805, "offsetStartMs": 1430, "word": "企业"}, {"offsetEndMs": 1805, "offsetStartMs": 1430, "word": "，"}], "wordsNum": 8}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 103440, "finalSentence": "敏捷型的企业和数字型的企业。", "silenceTime": 0, "sliceSentence": "敏捷 型 的 企业 和数 字型 的 企业 。", "speakerId": 0, "speechSpeed": 6.8, "startMs": 101515, "words": [{"offsetEndMs": 375, "offsetStartMs": 0, "word": "敏捷"}, {"offsetEndMs": 480, "offsetStartMs": 375, "word": "型"}, {"offsetEndMs": 600, "offsetStartMs": 480, "word": "的"}, {"offsetEndMs": 870, "offsetStartMs": 600, "word": "企业"}, {"offsetEndMs": 1170, "offsetStartMs": 870, "word": "和数"}, {"offsetEndMs": 1395, "offsetStartMs": 1170, "word": "字型"}, {"offsetEndMs": 1530, "offsetStartMs": 1395, "word": "的"}, {"offsetEndMs": 1925, "offsetStartMs": 1530, "word": "企业"}, {"offsetEndMs": 1925, "offsetStartMs": 1530, "word": "。"}], "wordsNum": 9}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 105940, "finalSentence": "我们说以后的管理是要去结构化的，", "silenceTime": 260, "sliceSentence": "我们 说 以后 的 管理 是 要 去 结构化 的 ，", "speakerId": 0, "speechSpeed": 6.7, "startMs": 103700, "words": [{"offsetEndMs": 335, "offsetStartMs": 0, "word": "我们"}, {"offsetEndMs": 530, "offsetStartMs": 335, "word": "说"}, {"offsetEndMs": 815, "offsetStartMs": 530, "word": "以后"}, {"offsetEndMs": 935, "offsetStartMs": 815, "word": "的"}, {"offsetEndMs": 1190, "offsetStartMs": 935, "word": "管理"}, {"offsetEndMs": 1295, "offsetStartMs": 1190, "word": "是"}, {"offsetEndMs": 1415, "offsetStartMs": 1295, "word": "要"}, {"offsetEndMs": 1580, "offsetStartMs": 1415, "word": "去"}, {"offsetEndMs": 2045, "offsetStartMs": 1580, "word": "结构化"}, {"offsetEndMs": 2240, "offsetStartMs": 2045, "word": "的"}, {"offsetEndMs": 2240, "offsetStartMs": 2045, "word": "，"}], "wordsNum": 11}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 106825, "finalSentence": "去边界化的，", "silenceTime": 0, "sliceSentence": "去 边界 化 的 ，", "speakerId": 0, "speechSpeed": 5.6, "startMs": 105940, "words": [{"offsetEndMs": 240, "offsetStartMs": 0, "word": "去"}, {"offsetEndMs": 570, "offsetStartMs": 240, "word": "边界"}, {"offsetEndMs": 705, "offsetStartMs": 570, "word": "化"}, {"offsetEndMs": 885, "offsetStartMs": 705, "word": "的"}, {"offsetEndMs": 885, "offsetStartMs": 705, "word": "，"}], "wordsNum": 5}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 107410, "finalSentence": "要去管理。", "silenceTime": 0, "sliceSentence": "要 去 管理 。", "speakerId": 0, "speechSpeed": 6.8, "startMs": 106825, "words": [{"offsetEndMs": 210, "offsetStartMs": 0, "word": "要"}, {"offsetEndMs": 375, "offsetStartMs": 210, "word": "去"}, {"offsetEndMs": 585, "offsetStartMs": 375, "word": "管理"}, {"offsetEndMs": 585, "offsetStartMs": 375, "word": "。"}], "wordsNum": 4}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 107820, "finalSentence": "的话，", "silenceTime": 30, "sliceSentence": "的话 ，", "speakerId": 0, "speechSpeed": 5.3, "startMs": 107440, "words": [{"offsetEndMs": 380, "offsetStartMs": 0, "word": "的话"}, {"offsetEndMs": 380, "offsetStartMs": 0, "word": "，"}], "wordsNum": 2}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 109500, "finalSentence": "让主动柔性起来，", "silenceTime": 260, "sliceSentence": "让 主动 柔性 起来 ，", "speakerId": 0, "speechSpeed": 4.9, "startMs": 108080, "words": [{"offsetEndMs": 290, "offsetStartMs": 0, "word": "让"}, {"offsetEndMs": 605, "offsetStartMs": 290, "word": "主动"}, {"offsetEndMs": 995, "offsetStartMs": 605, "word": "柔性"}, {"offsetEndMs": 1420, "offsetStartMs": 995, "word": "起来"}, {"offsetEndMs": 1420, "offsetStartMs": 995, "word": "，"}], "wordsNum": 5}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 115020, "finalSentence": "去驱动整个商业的主织效率提升和整个的管理效率提升。", "silenceTime": 260, "sliceSentence": "去 驱动 整个 商业 的 主 织 效率 提升 和 整个 的 管理 效率 提升 。", "speakerId": 0, "speechSpeed": 4.6, "startMs": 109760, "words": [{"offsetEndMs": 290, "offsetStartMs": 0, "word": "去"}, {"offsetEndMs": 605, "offsetStartMs": 290, "word": "驱动"}, {"offsetEndMs": 860, "offsetStartMs": 605, "word": "整个"}, {"offsetEndMs": 1115, "offsetStartMs": 860, "word": "商业"}, {"offsetEndMs": 1325, "offsetStartMs": 1115, "word": "的"}, {"offsetEndMs": 1565, "offsetStartMs": 1325, "word": "主"}, {"offsetEndMs": 1775, "offsetStartMs": 1565, "word": "织"}, {"offsetEndMs": 2165, "offsetStartMs": 1775, "word": "效率"}, {"offsetEndMs": 2680, "offsetStartMs": 2165, "word": "提升"}, {"offsetEndMs": 3310, "offsetStartMs": 2910, "word": "和"}, {"offsetEndMs": 4085, "offsetStartMs": 3660, "word": "整个"}, {"offsetEndMs": 4265, "offsetStartMs": 4085, "word": "的"}, {"offsetEndMs": 4595, "offsetStartMs": 4265, "word": "管理"}, {"offsetEndMs": 4850, "offsetStartMs": 4595, "word": "效率"}, {"offsetEndMs": 5260, "offsetStartMs": 4850, "word": "提升"}, {"offsetEndMs": 5260, "offsetStartMs": 4850, "word": "。"}], "wordsNum": 16}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 119460, "finalSentence": "我觉得这件事情就是我们看到做培产社会的真正的有利利益和价值，", "silenceTime": 230, "sliceSentence": "我 觉得 这件 事情 就是 我们 看到 做 培 产 社会 的 真正 的 有利 利益 和 价值 ，", "speakerId": 0, "speechSpeed": 6.9, "startMs": 115250, "words": [{"offsetEndMs": 260, "offsetStartMs": 0, "word": "我"}, {"offsetEndMs": 410, "offsetStartMs": 260, "word": "觉得"}, {"offsetEndMs": 635, "offsetStartMs": 410, "word": "这件"}, {"offsetEndMs": 1030, "offsetStartMs": 635, "word": "事情"}, {"offsetEndMs": 1490, "offsetStartMs": 1140, "word": "就是"}, {"offsetEndMs": 1685, "offsetStartMs": 1490, "word": "我们"}, {"offsetEndMs": 1910, "offsetStartMs": 1685, "word": "看到"}, {"offsetEndMs": 2060, "offsetStartMs": 1910, "word": "做"}, {"offsetEndMs": 2195, "offsetStartMs": 2060, "word": "培"}, {"offsetEndMs": 2300, "offsetStartMs": 2195, "word": "产"}, {"offsetEndMs": 2525, "offsetStartMs": 2300, "word": "社会"}, {"offsetEndMs": 2645, "offsetStartMs": 2525, "word": "的"}, {"offsetEndMs": 2885, "offsetStartMs": 2645, "word": "真正"}, {"offsetEndMs": 2990, "offsetStartMs": 2885, "word": "的"}, {"offsetEndMs": 3350, "offsetStartMs": 2990, "word": "有利"}, {"offsetEndMs": 3575, "offsetStartMs": 3350, "word": "利益"}, {"offsetEndMs": 3740, "offsetStartMs": 3575, "word": "和"}, {"offsetEndMs": 4210, "offsetStartMs": 3740, "word": "价值"}, {"offsetEndMs": 4210, "offsetStartMs": 3740, "word": "，"}], "wordsNum": 19}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 122580, "finalSentence": "以互联互通为着力点，", "silenceTime": 1250, "sliceSentence": "以 互联互通 为着 力点 ，", "speakerId": 0, "speechSpeed": 4.8, "startMs": 120710, "words": [{"offsetEndMs": 290, "offsetStartMs": 0, "word": "以"}, {"offsetEndMs": 1085, "offsetStartMs": 290, "word": "互联互通"}, {"offsetEndMs": 1445, "offsetStartMs": 1085, "word": "为着"}, {"offsetEndMs": 1870, "offsetStartMs": 1445, "word": "力点"}, {"offsetEndMs": 1870, "offsetStartMs": 1445, "word": "，"}], "wordsNum": 5}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 126180, "finalSentence": "促进企业内外部实现智能的组织互联，", "silenceTime": 350, "sliceSentence": "促进 企业 内外 部 实现 智能 的 组织 互联 ，", "speakerId": 0, "speechSpeed": 4.9, "startMs": 122930, "words": [{"offsetEndMs": 470, "offsetStartMs": 0, "word": "促进"}, {"offsetEndMs": 785, "offsetStartMs": 470, "word": "企业"}, {"offsetEndMs": 1100, "offsetStartMs": 785, "word": "内外"}, {"offsetEndMs": 1390, "offsetStartMs": 1100, "word": "部"}, {"offsetEndMs": 1925, "offsetStartMs": 1410, "word": "实现"}, {"offsetEndMs": 2300, "offsetStartMs": 1925, "word": "智能"}, {"offsetEndMs": 2465, "offsetStartMs": 2300, "word": "的"}, {"offsetEndMs": 2810, "offsetStartMs": 2465, "word": "组织"}, {"offsetEndMs": 3250, "offsetStartMs": 2810, "word": "互联"}, {"offsetEndMs": 3250, "offsetStartMs": 2810, "word": "，"}], "wordsNum": 10}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 128010, "finalSentence": "通过立体化、", "silenceTime": 710, "sliceSentence": "通过 立体化 、", "speakerId": 0, "speechSpeed": 4.5, "startMs": 126890, "words": [{"offsetEndMs": 440, "offsetStartMs": 0, "word": "通过"}, {"offsetEndMs": 1120, "offsetStartMs": 440, "word": "立体化"}, {"offsetEndMs": 1120, "offsetStartMs": 440, "word": "、"}], "wordsNum": 3}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 129540, "finalSentence": "多层次的服务，", "silenceTime": 320, "sliceSentence": "多层次 的 服务 ，", "speakerId": 0, "speechSpeed": 5.0, "startMs": 128330, "words": [{"offsetEndMs": 605, "offsetStartMs": 0, "word": "多层次"}, {"offsetEndMs": 740, "offsetStartMs": 605, "word": "的"}, {"offsetEndMs": 1210, "offsetStartMs": 740, "word": "服务"}, {"offsetEndMs": 1210, "offsetStartMs": 740, "word": "，"}], "wordsNum": 4}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 133500, "finalSentence": "让销客把成功探索no带给了企业，", "silenceTime": 710, "sliceSentence": "让 销 客 把 成功 探索 no 带给 了 企业 ，", "speakerId": 0, "speechSpeed": 4.6, "startMs": 130250, "words": [{"offsetEndMs": 290, "offsetStartMs": 0, "word": "让"}, {"offsetEndMs": 470, "offsetStartMs": 290, "word": "销"}, {"offsetEndMs": 695, "offsetStartMs": 470, "word": "客"}, {"offsetEndMs": 950, "offsetStartMs": 695, "word": "把"}, {"offsetEndMs": 1445, "offsetStartMs": 950, "word": "成功"}, {"offsetEndMs": 1895, "offsetStartMs": 1445, "word": "探索"}, {"offsetEndMs": 2170, "offsetStartMs": 1895, "word": "no"}, {"offsetEndMs": 2675, "offsetStartMs": 2250, "word": "带给"}, {"offsetEndMs": 2825, "offsetStartMs": 2675, "word": "了"}, {"offsetEndMs": 3250, "offsetStartMs": 2825, "word": "企业"}, {"offsetEndMs": 3250, "offsetStartMs": 2825, "word": "，"}], "wordsNum": 11}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 137310, "finalSentence": "以客户的成功来定义成功，", "silenceTime": 1430, "sliceSentence": "以 客户 的 成功 来定 义成 功 ，", "speakerId": 0, "speechSpeed": 4.6, "startMs": 134930, "words": [{"offsetEndMs": 290, "offsetStartMs": 0, "word": "以"}, {"offsetEndMs": 545, "offsetStartMs": 290, "word": "客户"}, {"offsetEndMs": 665, "offsetStartMs": 545, "word": "的"}, {"offsetEndMs": 1150, "offsetStartMs": 665, "word": "成功"}, {"offsetEndMs": 1745, "offsetStartMs": 1260, "word": "来定"}, {"offsetEndMs": 2090, "offsetStartMs": 1745, "word": "义成"}, {"offsetEndMs": 2380, "offsetStartMs": 2090, "word": "功"}, {"offsetEndMs": 2380, "offsetStartMs": 2090, "word": "，"}], "wordsNum": 8}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 140850, "finalSentence": "我们提供的不仅仅是一套SS软件，", "silenceTime": 770, "sliceSentence": "我们 提供 的 不仅仅 是 一套 SS 软件 ，", "speakerId": 0, "speechSpeed": 5.4, "startMs": 138080, "words": [{"offsetEndMs": 425, "offsetStartMs": 0, "word": "我们"}, {"offsetEndMs": 770, "offsetStartMs": 425, "word": "提供"}, {"offsetEndMs": 965, "offsetStartMs": 770, "word": "的"}, {"offsetEndMs": 1460, "offsetStartMs": 965, "word": "不仅仅"}, {"offsetEndMs": 1610, "offsetStartMs": 1460, "word": "是"}, {"offsetEndMs": 1970, "offsetStartMs": 1610, "word": "一套"}, {"offsetEndMs": 2315, "offsetStartMs": 1970, "word": "SS"}, {"offsetEndMs": 2770, "offsetStartMs": 2315, "word": "软件"}, {"offsetEndMs": 2770, "offsetStartMs": 2315, "word": "，"}], "wordsNum": 9}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 143940, "finalSentence": "而是一种全新的经营管理智慧，", "silenceTime": 500, "sliceSentence": "而是 一种 全新 的 经营 管理 智慧 ，", "speakerId": 0, "speechSpeed": 5.0, "startMs": 141350, "words": [{"offsetEndMs": 365, "offsetStartMs": 0, "word": "而是"}, {"offsetEndMs": 650, "offsetStartMs": 365, "word": "一种"}, {"offsetEndMs": 1115, "offsetStartMs": 650, "word": "全新"}, {"offsetEndMs": 1390, "offsetStartMs": 1115, "word": "的"}, {"offsetEndMs": 1820, "offsetStartMs": 1410, "word": "经营"}, {"offsetEndMs": 2135, "offsetStartMs": 1820, "word": "管理"}, {"offsetEndMs": 2590, "offsetStartMs": 2135, "word": "智慧"}, {"offsetEndMs": 2590, "offsetStartMs": 2135, "word": "，"}], "wordsNum": 8}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 154380, "finalSentence": "帮助企业实现客户组织伙伴it新版图的数据化经营转型，", "silenceTime": 2510, "sliceSentence": "帮助 企业 实现 客户 组织 伙伴 it 新版 图 的 数据 化 经营 转型 ，", "speakerId": 0, "speechSpeed": 3.2, "startMs": 146450, "words": [{"offsetEndMs": 500, "offsetStartMs": 0, "word": "帮助"}, {"offsetEndMs": 785, "offsetStartMs": 500, "word": "企业"}, {"offsetEndMs": 1270, "offsetStartMs": 785, "word": "实现"}, {"offsetEndMs": 2110, "offsetStartMs": 1470, "word": "客户"}, {"offsetEndMs": 3430, "offsetStartMs": 2760, "word": "组织"}, {"offsetEndMs": 4720, "offsetStartMs": 4050, "word": "伙伴"}, {"offsetEndMs": 5630, "offsetStartMs": 5250, "word": "it"}, {"offsetEndMs": 6155, "offsetStartMs": 5630, "word": "新版"}, {"offsetEndMs": 6335, "offsetStartMs": 6155, "word": "图"}, {"offsetEndMs": 6575, "offsetStartMs": 6335, "word": "的"}, {"offsetEndMs": 6965, "offsetStartMs": 6575, "word": "数据"}, {"offsetEndMs": 7115, "offsetStartMs": 6965, "word": "化"}, {"offsetEndMs": 7415, "offsetStartMs": 7115, "word": "经营"}, {"offsetEndMs": 7930, "offsetStartMs": 7415, "word": "转型"}, {"offsetEndMs": 7930, "offsetStartMs": 7415, "word": "，"}], "wordsNum": 15}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 159690, "finalSentence": "成为企业在信息化建设道路上携手共进的战略合作伙伴。", "silenceTime": 470, "sliceSentence": "成为 企业 在 信息化 建设 道路 上 携手 共进 的 战略合作 伙伴 。", "speakerId": 0, "speechSpeed": 5.0, "startMs": 154850, "words": [{"offsetEndMs": 485, "offsetStartMs": 0, "word": "成为"}, {"offsetEndMs": 860, "offsetStartMs": 485, "word": "企业"}, {"offsetEndMs": 1055, "offsetStartMs": 860, "word": "在"}, {"offsetEndMs": 1535, "offsetStartMs": 1055, "word": "信息化"}, {"offsetEndMs": 1835, "offsetStartMs": 1535, "word": "建设"}, {"offsetEndMs": 2135, "offsetStartMs": 1835, "word": "道路"}, {"offsetEndMs": 2410, "offsetStartMs": 2135, "word": "上"}, {"offsetEndMs": 3170, "offsetStartMs": 2640, "word": "携手"}, {"offsetEndMs": 3590, "offsetStartMs": 3170, "word": "共进"}, {"offsetEndMs": 3770, "offsetStartMs": 3590, "word": "的"}, {"offsetEndMs": 4385, "offsetStartMs": 3770, "word": "战略合作"}, {"offsetEndMs": 4840, "offsetStartMs": 4385, "word": "伙伴"}, {"offsetEndMs": 4840, "offsetStartMs": 4385, "word": "。"}], "wordsNum": 13}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 166450, "finalSentence": "7年多来，", "silenceTime": 1850, "sliceSentence": "7年 多来 ，", "speakerId": 0, "speechSpeed": 0.8, "startMs": 161540, "words": [{"offsetEndMs": 4470, "offsetStartMs": 4030, "word": "7年"}, {"offsetEndMs": 4910, "offsetStartMs": 4470, "word": "多来"}, {"offsetEndMs": 4910, "offsetStartMs": 4470, "word": "，"}], "wordsNum": 3}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 170140, "finalSentence": "不然逍客以连接的名义重新定义CM，", "silenceTime": 470, "sliceSentence": "不然 逍客 以 连接 的 名义 重新 定义 CM ，", "speakerId": 0, "speechSpeed": 5.0, "startMs": 166920, "words": [{"offsetEndMs": 335, "offsetStartMs": 0, "word": "不然"}, {"offsetEndMs": 635, "offsetStartMs": 335, "word": "逍客"}, {"offsetEndMs": 815, "offsetStartMs": 635, "word": "以"}, {"offsetEndMs": 1160, "offsetStartMs": 815, "word": "连接"}, {"offsetEndMs": 1295, "offsetStartMs": 1160, "word": "的"}, {"offsetEndMs": 1750, "offsetStartMs": 1295, "word": "名义"}, {"offsetEndMs": 2450, "offsetStartMs": 1950, "word": "重新"}, {"offsetEndMs": 2795, "offsetStartMs": 2450, "word": "定义"}, {"offsetEndMs": 3220, "offsetStartMs": 2795, "word": "CM"}, {"offsetEndMs": 3220, "offsetStartMs": 2795, "word": "，"}], "wordsNum": 10}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 176230, "finalSentence": "已经帮助60万家企业实现了企业信息化建设与数字化经营转型，", "silenceTime": 380, "sliceSentence": "已经 帮助 60 万家 企业 实现 了 企业 信息化 建设 与 数字化 经营 转型 ，", "speakerId": 0, "speechSpeed": 4.9, "startMs": 170520, "words": [{"offsetEndMs": 455, "offsetStartMs": 0, "word": "已经"}, {"offsetEndMs": 935, "offsetStartMs": 455, "word": "帮助"}, {"offsetEndMs": 1295, "offsetStartMs": 935, "word": "60"}, {"offsetEndMs": 1610, "offsetStartMs": 1295, "word": "万家"}, {"offsetEndMs": 2080, "offsetStartMs": 1610, "word": "企业"}, {"offsetEndMs": 2780, "offsetStartMs": 2340, "word": "实现"}, {"offsetEndMs": 2945, "offsetStartMs": 2780, "word": "了"}, {"offsetEndMs": 3245, "offsetStartMs": 2945, "word": "企业"}, {"offsetEndMs": 3665, "offsetStartMs": 3245, "word": "信息化"}, {"offsetEndMs": 4055, "offsetStartMs": 3665, "word": "建设"}, {"offsetEndMs": 4280, "offsetStartMs": 4055, "word": "与"}, {"offsetEndMs": 4880, "offsetStartMs": 4280, "word": "数字化"}, {"offsetEndMs": 5210, "offsetStartMs": 4880, "word": "经营"}, {"offsetEndMs": 5710, "offsetStartMs": 5210, "word": "转型"}, {"offsetEndMs": 5710, "offsetStartMs": 5210, "word": "，"}], "wordsNum": 15}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 184390, "finalSentence": "泛养逍客的企业朋友圈越来越快以客户为中心，", "silenceTime": 380, "sliceSentence": "泛 养 逍客 的 企业 朋友圈 越来越 快 以 客户 为 中心 ，", "speakerId": 0, "speechSpeed": 2.6, "startMs": 176610, "words": [{"offsetEndMs": 260, "offsetStartMs": 0, "word": "泛"}, {"offsetEndMs": 395, "offsetStartMs": 260, "word": "养"}, {"offsetEndMs": 710, "offsetStartMs": 395, "word": "逍客"}, {"offsetEndMs": 970, "offsetStartMs": 710, "word": "的"}, {"offsetEndMs": 1385, "offsetStartMs": 990, "word": "企业"}, {"offsetEndMs": 1990, "offsetStartMs": 1385, "word": "朋友圈"}, {"offsetEndMs": 2735, "offsetStartMs": 2190, "word": "越来越"}, {"offsetEndMs": 3010, "offsetStartMs": 2735, "word": "快"}, {"offsetEndMs": 6890, "offsetStartMs": 6600, "word": "以"}, {"offsetEndMs": 7160, "offsetStartMs": 6890, "word": "客户"}, {"offsetEndMs": 7265, "offsetStartMs": 7160, "word": "为"}, {"offsetEndMs": 7780, "offsetStartMs": 7265, "word": "中心"}, {"offsetEndMs": 7780, "offsetStartMs": 7265, "word": "，"}], "wordsNum": 13}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 186250, "finalSentence": "连接企业一切，", "silenceTime": 530, "sliceSentence": "连接 企业 一切 ，", "speakerId": 0, "speechSpeed": 4.5, "startMs": 184920, "words": [{"offsetEndMs": 515, "offsetStartMs": 0, "word": "连接"}, {"offsetEndMs": 815, "offsetStartMs": 515, "word": "企业"}, {"offsetEndMs": 1330, "offsetStartMs": 815, "word": "一切"}, {"offsetEndMs": 1330, "offsetStartMs": 815, "word": "，"}], "wordsNum": 4}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 188020, "finalSentence": "连接一切企业，", "silenceTime": 170, "sliceSentence": "连接 一切 企业 ，", "speakerId": 0, "speechSpeed": 3.8, "startMs": 186420, "words": [{"offsetEndMs": 635, "offsetStartMs": 0, "word": "连接"}, {"offsetEndMs": 1115, "offsetStartMs": 635, "word": "一切"}, {"offsetEndMs": 1600, "offsetStartMs": 1115, "word": "企业"}, {"offsetEndMs": 1600, "offsetStartMs": 1115, "word": "，"}], "wordsNum": 4}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 191080, "finalSentence": "让企业拥抱瞬息万变的时代，", "silenceTime": 200, "sliceSentence": "让 企业 拥抱 瞬息万变 的 时代 ，", "speakerId": 0, "speechSpeed": 4.2, "startMs": 188220, "words": [{"offsetEndMs": 350, "offsetStartMs": 0, "word": "让"}, {"offsetEndMs": 770, "offsetStartMs": 350, "word": "企业"}, {"offsetEndMs": 1330, "offsetStartMs": 770, "word": "拥抱"}, {"offsetEndMs": 2285, "offsetStartMs": 1380, "word": "瞬息万变"}, {"offsetEndMs": 2420, "offsetStartMs": 2285, "word": "的"}, {"offsetEndMs": 2860, "offsetStartMs": 2420, "word": "时代"}, {"offsetEndMs": 2860, "offsetStartMs": 2420, "word": "，"}], "wordsNum": 7}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 194200, "finalSentence": "构建价值命运共辉者共创、", "silenceTime": 170, "sliceSentence": "构建 价值 命运 共 辉 者 共创 、", "speakerId": 0, "speechSpeed": 3.7, "startMs": 191250, "words": [{"offsetEndMs": 545, "offsetStartMs": 0, "word": "构建"}, {"offsetEndMs": 995, "offsetStartMs": 545, "word": "价值"}, {"offsetEndMs": 1340, "offsetStartMs": 995, "word": "命运"}, {"offsetEndMs": 1505, "offsetStartMs": 1340, "word": "共"}, {"offsetEndMs": 1670, "offsetStartMs": 1505, "word": "辉"}, {"offsetEndMs": 1960, "offsetStartMs": 1670, "word": "者"}, {"offsetEndMs": 2950, "offsetStartMs": 2370, "word": "共创"}, {"offsetEndMs": 2950, "offsetStartMs": 2370, "word": "、"}], "wordsNum": 8}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 195160, "finalSentence": "共建、", "silenceTime": 350, "sliceSentence": "共建 、", "speakerId": 0, "speechSpeed": 3.3, "startMs": 194550, "words": [{"offsetEndMs": 610, "offsetStartMs": 0, "word": "共建"}, {"offsetEndMs": 610, "offsetStartMs": 0, "word": "、"}], "wordsNum": 2}, {"emotionType": ["disabled"], "emotionalEnergy": 0.0, "endMs": 196240, "finalSentence": "共赢。", "silenceTime": 530, "sliceSentence": "共赢 。", "speakerId": 0, "speechSpeed": 3.6, "startMs": 195690, "words": [{"offsetEndMs": 550, "offsetStartMs": 0, "word": "共赢"}, {"offsetEndMs": 550, "offsetStartMs": 0, "word": "。"}], "wordsNum": 2}], "success": true, "text": "我们想改变什么？我们的愿景是什么样？是什么驱使我们做这件事情，一定要去思考我们为用户，我们为这个时代，为这个商业和社会，真真正正创造了什么价值。商业环境存在着巨大的不确定性，尤其进入到移动互联时代，连接催动着一切进行加速。No又初出茅庐的新兴企业快速崛起，也有经历百年的公司瞬势轰然倒塌。现在企业的经营管理已经发生了质的变化，从原来以产品为中心转变为以用户为中心，这不仅仅是基于飞享逍客这家企业经营的本身，还有飞享销客60万家企业用户，如何借助飞享销客实现企业数字化转型，这是我一直思考与探索的问题。移动互联网时代未来的企业什么样的企业？一定是一些连接型的企业，敏捷型的企业和数字型的企业。我们说以后的管理是要去结构化的，去边界化的，要去管理。的话，让主动柔性起来，去驱动整个商业的主织效率提升和整个的管理效率提升。我觉得这件事情就是我们看到做培产社会的真正的有利利益和价值，以互联互通为着力点，促进企业内外部实现智能的组织互联，通过立体化、多层次的服务，让销客把成功探索no带给了企业，以客户的成功来定义成功，我们提供的不仅仅是一套SS软件，而是一种全新的经营管理智慧，帮助企业实现客户组织伙伴it新版图的数据化经营转型，成为企业在信息化建设道路上携手共进的战略合作伙伴。7年多来，不然逍客以连接的名义重新定义CM，已经帮助60万家企业实现了企业信息化建设与数字化经营转型，泛养逍客的企业朋友圈越来越快以客户为中心，连接企业一切，连接一切企业，让企业拥抱瞬息万变的时代，构建价值命运共辉者共创、共建、共赢。"}