请注意：请求体的数据格式为文本，内容为在线文件的URL链接（支持http以及https协议）。在线文件大小不超过 500M，长宽比小于2的图片宽高需在20～20000像素范围内，其他图片的宽高需在20～10000像素范围内。为了快速验证接入，这里为您提供了示例文件URL。

URL参数说明
以下是文档解析API的URL参数，URL参数指以 参数名=参数值 形式拼接到 URL 上的键值对。它以 ? 开头，不同参数之间使用 & 连接，形如 ?p1=v1&p2=v2。URL参数会影响文档的解析结果和JSON输出内容，您可按需进行设置。
pdf_pwd：当pdf为加密文档时，需要提供密码。
备注：对前端封装该接口参数时，需要自行对密码进行安全防护。
page_start：当上传的是pdf时，表示从第几页开始解析，取值范围从1开始，不传该参数时默认从首页开始。
page_count：当上传的是pdf时，表示要进行解析的pdf页数。总页数不得超过1000页，默认为1000页。
parse_mode：pdf文档的解析模式，默认为scan模式。图片不用设置，均默认按scan模式处理。
auto 综合文字识别和解析模式：对pdf电子档解析，会直接提取pdf中的文字
scan 仅按文字识别模式：将pdf当成图片处理
dpi：pdf文档的坐标基准，默认144 dpi。 与parse_mode参数联动
当parse_mode=auto时，默认动态，支持72，144，216；
当parse_mode=scan时，默认144，支持72，144，216。
apply_document_tree：markdown中是否生成标题层级，默认为1，生成标题。
0 不生成标题：同时也不会返回catalog字段
1 生成标题
table_flavor：markdown里的表格格式，默认为html，按html语法输出表格。
md 按md语法输出表格
html 按html语法输出表格
none 不进行表格识别，把表格图像当成普通文字段落来识别。
get_image：获取markdown里的图片，默认为none，不返回任何图像。
none 不返回任何图像
page 返回每一页的整页图像：即pdf页的完整页图片
objects 返回页面内的子图像：即pdf页内的各个子图片
both 返回整页图像和图像对象
image_output_type：指定返回的图片对象输出类型，默认返回子图片url和页图片id。
base64str 指定所有图片对象为base64字符串，适用于没有云存储的用户，但是引擎返回结果体积会很大。
default 指定子图片对象为图片url，页图片对象为图片id
apply_image_analysis：图像分析参数。利用大模型对文档中的子图片进行分析，分析结果以markdown格式输出，并替换掉子图片的文本识别内容。默认为0，不进行图像分析。
0 不进行图像分析
1 进行图像分析
paratext_mode：markdown中非正文文本内容展示模式。默认为annotation。非正文内容包括页眉页脚、子图中的文本。
none 不展示
annotation 以注释格式插入到markdown中。页眉页脚中的图片只保留文本，图片base64或url不保留
body 以正文格式插入到markdown中
formula_level：公式识别等级，默认为0，全识别。
0 行间公式和行内公式都识别
1 仅识别行间公式，行内公式不识别
2 不识别公式
apply_merge：是否进行段落合并和表格合并。默认为1，合并段落和表格。
0 不合并
1 合并
markdown_details：是否返回结果中的detail字段。默认为1，返回detail字段，保存markdown各类型元素的详细信息。
0 不返回
1 返回
page_details：是否返回结果中的pages字段。默认为1，返回pages字段，保存每一页更加详细的解析结果。
0 不返回
1 返回
raw_ocr：是否返回全部文字识别结果(包含字符坐标信息)，结果字段为raw_ocr。默认为0，不返回。与page_details参数联动，当page_details为0或false时不返回。
0 不返回
1 返回
char_details：是否返回结果中的char_pos字段（保存每个字符的位置信息）和raw_ocr中的char_相关字段。默认为0，不返回。
0 不返回
1 返回
catalog_details：是否返回结果中的catalog字段，保存目录相关信息。与apply_document_tree参数联动，当apply_document_tree为0时不返回。
0 不返回
1 返回
get_excel：是否返回excel的base64结果，结果字段为excel_base64，可以根据该字段进行后处理保存excel文件。默认为0，不返回。
0 不返回
1 返回
crop_image（切边矫正）：是否进行切边矫正预处理，默认为0，不进行切边矫正。
0 不进行切边矫正
1 进行切边矫正
remove_watermark（去水印）：是否进行去水印预处理，默认为0，不去水印。
0 不去水印
1 去水印
apply_chart（图表识别）：是否开启图表识别，开启图表识别会将识别到的图表以表格形式输出。默认为0，不进行图表识别。
0 不开启图表识别
1 开启图表识别

返回JSON结构说明
当您使用文档解析API解析文档时，解析后的数据将按照以下结构的JSON格式返回。
```json
{
  "code": 200, // 响应状态码，200表示成功
  "result": {
    "markdown": "# 劳动人事争议仲裁申请书\n\n致:广东省劳动人事争议调解仲裁院\n\n[完整表格内容...]", // 生成的markdown格式文档内容正文字符串
    "success_count": 5, // 成功处理的页面数量
    "pages": [
      {
        "angle": 0, // 页面旋转角度，0表示无旋转
        "page_id": 1, // 页面ID
        "content": [
          {
            "pos": [276, 243, 970, 243, 970, 291, 276, 291], // 文本行四个角点坐标
            "id": 0, // 内容块ID
            "score": 1, // 识别置信度分数
            "type": "line", // 内容类型，line表示文本行
            "text": "劳动人事争议仲裁申请书" // 识别的文本内容
          },
          {
            "pos": [181, 400, 560, 400, 560, 424, 181, 424], // 文本行四个角点坐标
            "id": 1, // 内容块ID
            "score": 1, // 识别置信度分数
            "type": "line", // 内容类型，line表示文本行
            "text": "致:广东省劳动人事争议调解仲裁院" // 识别的文本内容
          }
          // [更多content条目...]
        ],
        "status": "success", // 页面处理状态，success表示成功
        "height": 1684, // 页面高度
        "structured": [
          {
            "pos": [278, 244, 967, 242, 967, 293, 278, 295], // 结构化内容的位置坐标
            "type": "textblock", // 结构化内容类型，textblock表示文本块
            "id": 0, // 结构化内容ID
            "content": [0], // 关联的content数组索引
            "text": "劳动人事争议仲裁申请书", // 结构化文本内容
            "outline_level": 0, // 大纲级别，0表示顶级标题
            "sub_type": "text_title" // 子类型，text_title表示文本标题
          }
          // [更多structured条目...]
        ],
        "durations": 459.98861694336, // 页面处理耗时（毫秒）
        "image_id": "", // 页面图像ID
        "width": 1191 // 页面宽度
      }
      // [更多pages条目...]
    ],
    "valid_page_number": 5, // 有效页面数量
    "total_page_number": 5, // 总页面数量
    "total_count": 5, // 总处理数量
    "detail": [
      {
        "paragraph_id": 0, // 段落ID
        "page_id": 1, // 所属页面ID
        "tags": [], // 标签数组
        "outline_level": 0, // 大纲级别
        "text": "劳动人事争议仲裁申请书", // 段落文本内容
        "type": "paragraph", // 内容类型，paragraph表示段落
        "position": [278, 244, 967, 242, 967, 293, 278, 295], // 段落位置坐标
        "content": 0, // 关联的content索引
        "sub_type": "text_title" // 子类型，text_title表示文本标题
      }
      // [更多detail条目...]
    ]
  },
  "x_request_id": "f36effda6a0141ed0583bea0d596f597", // 请求唯一标识符
  "metrics": [
    {
      "angle": 0, // 页面旋转角度
      "status": "success", // 处理状态
      "dpi": 144, // 图像DPI值
      "image_id": "", // 图像ID
      "page_id": 1, // 页面ID
      "duration": 464.10571289062, // 处理耗时（毫秒）
      "page_image_width": 1191, // 页面图像宽度
      "page_image_height": 1684 // 页面图像高度
    }
    // [更多metrics条目...]
  ],
  "duration": 1459, // 引擎耗时（毫秒）
  "message": "success", // 响应消息
  "version": "3.17.12" // 引擎版本号
}
```
常规字段说明
x_request_id：该请求的唯一标识。
code：错误码，200表示成功。详情见快速启动-错误码说明。
message：错误信息，成功时为”success”。
version：引擎版本号，例如”3.18.9”。
duration：引擎耗时(毫秒)，例如”999”。

主要结果说明：result对象
文档解析API会在返回结果的result对象中包含以下关键信息。

markdown：正文字符串
markdown：解析结果 markdown 的正文字符串。

detail：markdown 各类型元素详细信息
detail包含markdown中不同类型元素的详细信息。受URL参数markdown_details影响，默认返回detail字段，详情见快速启动-URL参数说明。
detail：markdown各类型元素详细信息
page_id：当前元素所在页码，例如”1”。
paragraph_id：当前元素id。
outline_level：标题级别(最多支持5级标题) -1表示正文，0表示一级标题，1表示二级标题 …
text：文本，例如”hello markdown”。
position：以长度为8的整型数组表示四边形，8个数两两一组为一个点的横纵坐标，分别是左上，右上，右下，左下。 当输入是PDF时, 此坐标是基于72dpi的；当输入是图片时，此坐标是原图里的坐标。 单位：像素。例如[217, 390, 1336, 390, 1336, 460, 217, 460]
origin_position：受URL参数切边矫正和去水印影响，详情见快速启动-URL参数说明。仅当打开切边或去水印时返回，表示该段落在原图中的坐标。格式同position。
content：表示元素是否为正文。0 正文(段落、图片、表格)；1 非正文(页眉、页脚、侧边栏)
type：元素的类型。
paragraph（段落类型，包括正文、标题、公式等文字信息）
image（图片类型）
table（表格类型）
sub_type：元素子类型，受type影响。
当type为paragraph时，取值范围为catalog(目录),header(页眉),footer(页脚),sidebar(侧边栏),text(正文普通文本),text_title(文本标题),image_title(图片标题),table_title(表格标题)；
当type是image时，取值范围为stamp(印章),chart(图表),qrcode(二维码),barcode(条形码)；
当type为table时，取值范围为bordered(有线表), borderless(无线表)
image_url：图片链接，仅在type为image时返回。受URL参数get_image和image_output_type影响，详情见快速启动-URL参数说明。
当get_image = objects, image_output_type = default时，返回图片的url，图片默认保存30天；如需长久保存，请在有效期内下载图片并自行保存，可参考获取图片并持久化；
或者使用image_output_type = base64str，图片以base64的方式返回。
tags：表示段落内是否存在特殊文本，类型包括公式formula和手写体handwritten，仅在type为paragraph时返回。
caption_id：表格或图片的标题id，仅在type为image或table时返回。
page_id：标题所在页码。
paragraph_id：标题所在段落id。
cells：单元格数组，仅在type为table时返回。
row：单元格行号。
col：单元格列号。
row_span：单元格行跨度,默认为1。
col_span：单元格列跨度,默认为1。
position：单元格的四个角点坐标，依次为左上，右上，右下，左下。例如[10, 10, 100, 10, 100, 50, 10, 50]
origin_position：受URL参数切边矫正或去水印影响，详情见快速启动-URL参数说明。仅当打开切边或去水印时返回，表示该单元格在原图中的坐标。格式同position。
text：单元格文本内容。
type：类型，固定为cell，表示单元格。

pages：每一页的详细信息
文档按页为单位展开时， 存储每一页的详情和状态（适用于PDF），部分信息与metrics字段重复。受URL参数page_details影响，默认返回pages，详情见快速启动-URL参数说明。
pages：每一页的详细信息
page_id：当前页码 (若为流式文件, 页码置为0)，例如”0”。
status：表示当前页的引擎输出状态，或者error_message，例如”success”。
durations：当前页总耗时(毫秒)，例如”612.5”。
width：文档页宽度
height：文档页高度
angle：图像的角度（可选值0, 90, 180, 270)
image_id：当前页图片id 。受URL参数get_image和image_output_type影响，当URL参数image_output_type=default且get_image=page/both时返回，详情见快速启动-URL参数说明。
下载方式：https://api.textin.com/ocr_image/download?image_id=xxx ,需要在headers里添加appid和key
origin_image_id：切边或去水印前的原始页图片。受URL参数切边矫正或去水印影响，详情见快速启动-URL参数说明。仅当开启切边或去水印，image_output_type=default且get_image=page/both时返回。
下载方式同image_id
base64：当前页图片的base64字符串，受URL参数image_output_type影响，详情见快速启动-URL参数说明。当输入参数image_output_type=base64str且get_image=page/both时返回。
origin_base64：切边或去水印前的原始页图片base64字符串。受URL参数切边矫正或去水印影响，详情见快速启动-URL参数说明。仅当开启切边或去水印，image_output_type=base64str且get_image=page/both时返回。
raw_ocr：全部文字识别结果，只包含文字结果。受URL参数raw_ocr影响，默认不返回，详情见快速启动-URL参数说明。
text：识别内容字符串，例如”这是一个例子。”
score：识别置信度（0 <= x <= 1），例如”0.99”。
type：文本类型，用于表示文字的形态。 当前版本下，文本类型包括：
text(文本)
formula(公式)
position：文本行的四个角点坐标，依次为左上，右上，右下，左下。例如[10, 10, 100, 10, 100, 50, 10, 50]
angle：文本行的角度（可选值0, 90, 180, 270)
direction：文字阅读方向。
-1: 其他
0: 单字
1: 横向阅读
2: 纵向阅读
handwritten：文字是否手写所得。
-1: 未知
0: 非手写文字, 一般为印刷文字
1: 文字手写, 一般具备明显的书写特征
char_scores：字符置信度，值域范围0-1。 受URL参数char_details影响，详情见快速启动-URL参数说明。设置char_details=1时输出。
char_centers：字符中心点坐标。受URL参数char_details影响，详情见快速启动-URL参数说明。设置char_details=1时输出。
char_positions：字符四边形点坐标，以顺时针构成闭合区域。 受URL参数char_details影响，详情见快速启动-URL参数说明。设置char_details=1时输出。
char_candidates：候选字数组，表示每一个字符的候选，与候选置信度配套使用。受URL参数char_details影响，详情见快速启动-URL参数说明。 设置char_details=1时输出。
char_candidates_score：候选字置信度数组，表示每一个候选字符的置信度，与候选字符配套使用。 受URL参数char_details影响，详情见快速启动-URL参数说明。 设置char_details=1时输出。
content：基础数据，为文字行、图像中的其中一种。
textline：文字行
id：数据id(页内唯一)
type：数据类型,line
text：文本行文字内容, 当sub_type=stamp时， text为印章上的文字。
angle：文本行文字方向, 默认为0(angle为0时， json中可能不包含angle属性)。
pos：文本行四个角点坐标。
origin_position：表示文本行在原图中的坐标。受URL参数切边矫正或去水印影响，详情见快速启动-URL参数说明。仅当打开切边或去水印时返回，格式同pos。
sub_type：子类型。
当type为line时，取值范围有handwriting, formula;
当type为image时，取值范围有stamp(印章),chart(图表),qrcode(二维码),barcode(条形码)
direction：文字方向, 默认为0。
0:横向文本;
1:竖向文本;
2:横向右往左文本（如阿拉伯语）
score：文本行内每个字符的置信度(仅当输入图像做ocr时)
char_pos：文本行内每个字符的坐标,每个item是一个由八个整数组成的数组，分别表示，左上，右上，右下，左下四个点的(x,y)坐标。受URL参数char_details影响，详情见快速启动-URL参数说明。设置char_details=1时输出。
image：图像
id：数据id
type：数据类型, image
pos：图像四个角点坐标
sub_type：子类型, 包括stamp, chart, qrcode, barcode
size：图像大小[width, height]
data：图像内容
base64：图像文件(jpg, png)的base64字符串
region：图像在页图像中的区域（四边形4个点坐标）
path：图像文件路径（如在zip包中的路径）
structured：结构化数据，为段落块、图像块、表格块、页脚块、页眉块中的一种。
textblock：段落块
type：段落块类型， 固定为 textblock
pos：文本行四个角点坐标
origin_position：表示该段落在原图中的坐标。受URL参数切边矫正或去水印影响，详情见快速启动-URL参数说明。仅当打开切边或去水印时返回，格式同pos。
content：段落块内文本行id数据组
sub_type：段落块子类型，包括catalog(目录),text(正文普通文本),text_title(文本标题),image_title(图片标题),table_title(表格标题)
continue：段落块连续属性，用于判断完整的段落块是否被页面或栏分割，为true表示该段落块和下一个段落块连续（即两个段落块可合成一个逻辑段落块）。
next_page_id：当且仅当continue为true时有值。表示下一个段落块的page_id。
next_para_id：当且仅当continue为true时有值。表示下一个段落块的paragraph_id。
text：段落块文本内容
outline_level：标题级别: (最多支持5级标题)
-1：正文
0：一级标题
1：二级标题
…
imageblock：图像块
type：图像块类型， 值为 image
pos：文本行四个角点坐标
origin_position：表示该子图在原图中的坐标。受URL参数切边矫正或去水印影响，详情见快速启动-URL参数说明。仅当打开切边或去水印时返回，格式同pos。
lines：图像包含的文本行id
content：图像资源数据id数组
caption_id：图片的标题id
page_id：标题所在页码
paragraph_id：标题所在段落id
text：子图片识别得到的文本内容。受URL参数apply_image_analysis影响，详情见快速启动-URL参数说明。开启图像分析后，该字段内容会替换为大模型对子图片的分析结果。
table：表格块
type：表格块类型, 固定为table
sub_type：表格子属性，取值为bordered(有线表)或borderless(无线表)，默认为bordered(即json中无该字段时，默认值为bordered)
pos：文本行四个角点坐标
origin_position：表示该表格在原图中的坐标。受URL参数切边矫正或去水印影响，详情见快速启动-URL参数说明。仅当打开切边或去水印时返回，格式同pos。
rows：表格行数
cols：表格列数
columns_width：表格列宽度列表
rows_height：表格行高度列表
text：表格文本内容，以html或md格式展示
continue：当前表格与后一表格连续，用来判断一个表格是否被页面分割（如果 continue为true 且该表格位于本页结尾，该表格可与下一页开头表格组合为一个表格）
caption_id：表格的标题id
page_id：标题所在页码
paragraph_id：标题所在段落id
cells：单元格数组
row：单元格行号
col：单元格列号
row_span：单元格行跨度,默认为1
col_span：单元格列跨度,默认为1
pos：单元格的四个角点坐标，依次为左上，右上，右下，左下。
content：单元格内容
footer：页脚块
type：页脚块类型，固定为 footer
pos：文本行四个角点坐标
blocks：footer段落内容，为textblock, imageblock, table中其中的一种
header：页眉块
type：页眉块类型，固定为 header
pos：文本行四个角点坐标
blocks：header段落内容，为textblock, imageblock, table中的其中一种

catalog：描述目录树的结构
catalog：目录树结构。受URL参数catalog_details和apply_document_tree影响，详情见快速启动-URL参数说明。
toc：返回的table of contents
sub_type：标题类型 text_title、 image_title、 table_title
hierarchy：标题层级， 1 是 一级标题, 2 是 二级标题，依次类推
title：标题内容
page_id：标题所在页码（最小页码为 1)
paragraph_id：标题所在段落id
pos：该目录区域的四个角点坐标，依次为左上，右上，右下，左下。
pos_list：发生标题合并时，为合并前多个标题的坐标；未发生标题合并时，即为该标题的坐标。

其他result字段
total_page_number：输入PDF时， 返回文档的总页数。
valid_page_number：记录本次解析成功的总页数。
excel_base64：excel的base64结果，受URL参数get_excel影响，仅当get_excel=1时返回。详情见快速启动-URL参数说明。

metrics：每一页的信息
部分信息跟pages字段重复，当URL参数page_details设置为不返回pages字段时，可以在metrics字段获取每一页的信息。
metrics：每一页信息
page_image_width：当前段落所在页的图片宽或者pdf转成的图片宽，例如”1024”。
page_image_height：当前段落所在页的图片高或者pdf转成的图片高，例如”768”。
dpi：当前pdf页转成图片所用的dpi，例如”72”。
durations：当前页总耗时(毫秒)
status：当前页状态
page_id：当前页码
angle：图像角度， 定义0度为人类阅读文字的图像方向，称为正置图像， 本字段表示输入图像是正置图像进行顺时针若干角度的旋转所得。
0: ▲
90: ▶
180: ▼
270: ◀
image_id：当前页图片id。
下载方式：https://api.textin.com/ocr_image/download?image_id=xxx, 需要在headers里添加appid和key, 有效期30天

将结果保存为JSON或markdown文件
参考快速启动中的使用示例，您可以将API返回的结果保存为JSON文件，也可以解析JSON响应以提取并保存markdown文件。以下示例代码在快速启动中已提供，您可以直接使用。
```python
with open("result.json", "w", encoding="utf-8") as f:
f.write(response)

        # 解析JSON响应以提取markdown内容
        json_response = json.loads(response)
        if "result" in json_response and "markdown" in json_response["result"]:
            markdown_content = json_response["result"]["markdown"]
            with open("result.md", "w", encoding="utf-8") as f:
                f.write(markdown_content)
```
错误码说明
错误码	描述
40101	x-ti-app-id 或 x-ti-secret-code 为空
40102	x-ti-app-id 或 x-ti-secret-code 无效，验证失败
40103	客户端IP不在白名单
40003	余额不足，请充值后再使用
40004	参数错误，请查看技术文档，检查传参
40007	机器人不存在或未发布
40008	机器人未开通，请至市场开通后重试
40301	图片类型不支持
40302	上传文件大小不符，文件大小不超过 500M
40303	文件类型不支持，接口会返回实际检测到的文件类型，如“当前文件类型为.gif”
40304	图片尺寸不符，长宽比小于2的图片宽高需在20～20000像素范围内，其他图片的宽高需在20～10000像素范围内
40305	识别文件未上传
40422	文件损坏（The file is corrupted.）
40423	PDF密码错误（Password required or incorrect password.）
40424	页数设置超出文件范围（Page number out of range.）
40425	文件格式不支持（The input file format is not supported.）
40427	DPI参数不在支持列表中（Input DPI is not in the allowed DPIs list(72,144,216).）
40428	word和ppt转pdf失败或者超时（Process office file failed.）
50207	部分页面解析失败（Partial failed）
40400	无效的请求链接，请检查链接是否正确
30203	基础服务故障，请稍后重试
500	服务器内部错误