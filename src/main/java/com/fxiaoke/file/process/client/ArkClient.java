package com.fxiaoke.file.process.client;

import com.fxiaoke.file.process.config.ArkConfig;
import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.domain.model.Usages;
import com.fxiaoke.file.process.domain.model.VisCompReq;
import com.fxiaoke.file.process.domain.model.VisCompRes;
import com.fxiaoke.file.process.utils.MarkDownUtils;
import com.volcengine.ark.runtime.model.Usage;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionContentPart;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionContentPart.ChatCompletionContentPartImageURL;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionResult;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.service.ArkService;
import com.fxiaoke.file.process.manager.ThreadManager;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

@Service
@Slf4j(topic = "ArkClient")
@RefreshScope
public class ArkClient {

  private final ArkConfig config;
  private final ArkService arkService;
  private final ThreadManager threadManager;

  public ArkClient(ArkConfig arkConfig,ArkService arkService,ThreadManager threadManager) {
    this.config = arkConfig;
    this.arkService = arkService;
    this.threadManager = threadManager;
  }

  public <T> VisCompRes<T> visComp(VisCompReq<T> visCompReq) {

    List<ChatMessage> chatMessages = toChatMessages(visCompReq);

    ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
        .model(visCompReq.getModel())
        .messages(chatMessages)
        .temperature(Constants.ARK_TEMPERATURE)
        .topP(Constants.ARK_TOP_P)
        .maxTokens(Constants.ARK_MAX_TOKENS)
        .frequencyPenalty(Constants.ARK_FREQUENCY_PENALTY)
        .build();

    ChatCompletionResult chatCompletion = arkService.createChatCompletion(chatCompletionRequest);

    return toVisCompRes(visCompReq, chatCompletion);
  }

  /**
   * 并发处理多个视觉理解请求。
   * 参考 FileServiceImpl.localMdFileToS3PreSignUrl 的批处理机制，
   * 将请求分割成每5个请求为一批进行并发处理，确保某个请求失败不会影响其他请求的结果。
   * @param visCompReqs 视觉理解请求列表
   * @return 视觉理解结果列表，只包含成功的请求结果
   */
  public <T> List<VisCompRes<T>> visComps(List<VisCompReq<T>> visCompReqs) {
    // 使用虚拟线程并发来处理多个视觉理解请求
    if (visCompReqs == null || visCompReqs.isEmpty()) {
      return new ArrayList<>();
    }

    int visCompCount = visCompReqs.size();
    log.info("Vision completion batch processing start, request count: {}", visCompCount);

    // 根据批次大小将任务分割成多个批次
    List<List<VisCompReq<T>>> taskList = splitIntoBatches(visCompReqs, config.getBatchSize());

    // 为每个批次创建一个 CompletableFuture 列表
    List<List<CompletableFuture<Optional<VisCompRes<T>>>>> futures = new ArrayList<>();
    for (List<VisCompReq<T>> batch : taskList) {
      List<CompletableFuture<Optional<VisCompRes<T>>>> batchTask = createVisCompAsyncTasks(batch);
      futures.add(batchTask);
    }

    // 分批处理所有任务
    for (List<CompletableFuture<Optional<VisCompRes<T>>>> batchFutures : futures) {
      // 等待所有任务完成，无论成功失败
      for (CompletableFuture<Optional<VisCompRes<T>>> future : batchFutures) {
        try {
          future.get(config.getBatchTimeout(), TimeUnit.SECONDS);
        } catch (Exception e) {
          // 不取消其他任务，让它们继续执行
          log.warn("Vision completion batch process, request fail error", e);
        }
      }
    }

    // 安全收集所有批次的成功结果
    List<VisCompRes<T>> results = new ArrayList<>();
    for (List<CompletableFuture<Optional<VisCompRes<T>>>> batchFutures : futures) {
      for (CompletableFuture<Optional<VisCompRes<T>>> future : batchFutures) {
        try {
          Optional<VisCompRes<T>> result = future.get();
          result.ifPresent(results::add);
        } catch (Exception e) {
          log.warn("Vision completion batch process, get future result fail: {}", e.getMessage());
        }
      }
    }

    log.info("Vision completion batch process end, request count: {}, success count: {}", 
        visCompCount, results.size());
    return results;
  }

  /**
   * 为单个视觉理解请求创建异步任务。
   * @param visCompReq 视觉理解请求
   * @return CompletableFuture 包含视觉理解结果的 Optional，失败时返回 empty
   */
  private <T>CompletableFuture<Optional<VisCompRes<T>>> createVisCompAsyncTask(VisCompReq<T> visCompReq) {
    return CompletableFuture.supplyAsync(() -> {
      try {
        VisCompRes<T> result = visComp(visCompReq);
        return Optional.of(result);
      } catch (Exception e) {
        log.warn("Vision completion request failed for request: {}, error: {}", visCompReq, e.getMessage());
        return Optional.empty();
      }
    }, threadManager.getExecutor());
  }

  /**
   * 为视觉理解请求列表创建异步任务列表。
   * @param visCompReqs 视觉理解请求列表
   * @return CompletableFuture 列表，每个元素包含 Optional<VisCompRes<T>>
   */
  private <T> List<CompletableFuture<Optional<VisCompRes<T>>>> createVisCompAsyncTasks(List<VisCompReq<T>> visCompReqs) {
    List<CompletableFuture<Optional<VisCompRes<T>>>> futures = new ArrayList<>(visCompReqs.size());
    for (VisCompReq<T> visCompReq : visCompReqs) {
      CompletableFuture<Optional<VisCompRes<T>>> future = createVisCompAsyncTask(visCompReq);
      futures.add(future);
    }
    return futures;
  }

  /**
   * 将视觉理解请求列表分割成多个批次。
   * @param visCompReqs 视觉理解请求列表
   * @param batchSize 批次大小
   * @return 分割后的批次列表
   */
  private <T> List<List<VisCompReq<T>>> splitIntoBatches(
      List<VisCompReq<T>> visCompReqs, int batchSize) {
    int taskCount = visCompReqs.size();
    List<List<VisCompReq<T>>> batches = new ArrayList<>();
    for (int i = 0; i < taskCount; i += batchSize) {
      int end = Math.min(i + batchSize, taskCount);
      batches.add(visCompReqs.subList(i, end));
    }
    return batches;
  }

  /**
   * 将 VisCompReq 转换为 ChatMessage 列表。
   * @param visCompReq 视觉理解请求
   * @return ChatMessage 列表
   */
  private <T> List<ChatMessage>  toChatMessages(VisCompReq<T> visCompReq) {
    // 用户请求
    List<ChatMessage> chatMessages = new ArrayList<>();

    ChatMessage systemMsg = ChatMessage
        .builder()
        .role(ChatMessageRole.SYSTEM)
        .content(visCompReq.getSystemPrompt())
        .build();
    chatMessages.add(systemMsg);

    // 多模态内容部分
    List<ChatCompletionContentPart> multiParts = new ArrayList<>();

    // 用户提示词
    ChatCompletionContentPart textContent = ChatCompletionContentPart
        .builder()
        .type("text")
        .text(visCompReq.getUserPrompt())
        .build();
    multiParts.add(textContent);

    // 图片URL或图片Base64编码
    ChatCompletionContentPartImageURL imageContentUrl = new ChatCompletionContentPartImageURL(
        visCompReq.getImageUrl(), visCompReq.getDetail().getValue());
    ChatCompletionContentPart imageContent = ChatCompletionContentPart
        .builder()
        .type("image_url")
        .imageUrl(imageContentUrl)
        .build();
    multiParts.add(imageContent);

    // 用户消息
    ChatMessage userMsg = ChatMessage
        .builder()
        .role(ChatMessageRole.USER)
        .multiContent(multiParts)
        .build();
    chatMessages.add(userMsg);

    return  chatMessages;
  }

  /**
   * 将 ChatCompletionResult 转换为 VisCompRes。
   * @param visCompReq 模型名称
   * @param chatCompletion ChatCompletionResult 对象
   * @return VisCompRes 对象
   */
  private <T>VisCompRes<T> toVisCompRes(VisCompReq<T> visCompReq ,ChatCompletionResult chatCompletion) {
    VisCompRes<T> visCompRes = new VisCompRes<>();
    // 透传数据
    visCompRes.setData(visCompReq.getData());
    Usages usages = new Usages(visCompReq.getModel());
    Usage usage = chatCompletion.getUsage();
    usages.addCount();
    usages.addInputTokens(usage.getPromptTokens());
    usages.addOutputTokens(usage.getCompletionTokens());
    usages.addTotalTokens(usage.getTotalTokens());
    visCompRes.setUsages(usages);

    StringBuilder contentBuilder = new StringBuilder();
    chatCompletion.getChoices().forEach(choice ->
        contentBuilder.append(choice.getMessage().getContent())
    );

    if (contentBuilder.isEmpty() || Constants.INVALID_CONTENT.contentEquals(contentBuilder)) {
      visCompRes.setContent("");
      return visCompRes; // 返回空结果
    }

    // 清理识别中AI臆造的图片标签链接
    String content = MarkDownUtils.cleanVlIllusionStr(contentBuilder.toString());
    visCompRes.setContent(content);
    return visCompRes;
  }

}
