package com.fxiaoke.file.process.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fxiaoke.file.process.config.TextInConfig;
import com.fxiaoke.file.process.domain.constants.FileType;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.utils.FilenameUtil;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Component
@RefreshScope
@Slf4j(topic = "TextInClient")
public class TextInClient {

  private static final String MODULE = "TextInClient";
  private final TextInConfig config;
  private static final ObjectMapper objectMapper = new ObjectMapper();

  // 构造函数
  public TextInClient(TextInConfig textInConfig) {
    this.config = textInConfig;
  }

  /**
   * 执行 TextIn OCR 请求，将文件内容转换为 Markdown
   *
   * @param fileContent 文件内容字节数组
   * @param options     请求参数选项
   * @param mdFilePath  输出的 Markdown 文件路径
   * @throws IOException 当网络请求或文件操作失败时抛出
   */
  public void performTextInOcr(byte[] fileContent, HashMap<String, Object> options, Path mdFilePath) throws IOException {
    // 构建查询参数
    StringBuilder queryParams = new StringBuilder();
    for (Map.Entry<String, Object> entry : options.entrySet()) {
      if (!queryParams.isEmpty()) {
        queryParams.append("&");
      }
      queryParams.append(URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8))
          .append("=")
          .append(URLEncoder.encode(entry.getValue().toString(), StandardCharsets.UTF_8));
    }

    // 创建完整的 URL
    String fullUrl = config.getBaseUrl() + (!queryParams.isEmpty() ? "?" + queryParams : "");
    URI uri = URI.create(fullUrl);

    // 创建并配置 HTTP 连接
    HttpURLConnection connection = (HttpURLConnection) uri.toURL().openConnection();
    connection.setRequestMethod("POST");

    // 设置请求头
    connection.setRequestProperty("x-ti-app-id", config.getAppid());
    connection.setRequestProperty("x-ti-secret-code", config.getSecretCode());
    connection.setRequestProperty("Content-Type", "application/octet-stream");

    // 启用输出并发送文件内容
    connection.setDoOutput(true);
    try (OutputStream os = connection.getOutputStream()) {
      os.write(fileContent);
      os.flush();
    }

    // 读取响应
    int responseCode = connection.getResponseCode();
    if (responseCode != HttpURLConnection.HTTP_OK) {
      String errorMessage = "TextIn OCR failed with HTTP code: " + responseCode;
      log.error(errorMessage);
      throw new BaseException(500, errorMessage, MODULE + ".performTextInOcr", mdFilePath);
    }

    // 保存 JSON 响应到文件
    Path jsonFilePath = FilenameUtil.replaceExtension(mdFilePath, FileType.JSON.getFileType());
    try (InputStream inputStream = connection.getInputStream();
         OutputStream outputStream = Files.newOutputStream(jsonFilePath)) {
      byte[] buffer = new byte[8192]; // 8KB 缓冲区
      int bytesRead;
      while ((bytesRead = inputStream.read(buffer)) != -1) {
        outputStream.write(buffer, 0, bytesRead);
      }
      outputStream.flush();
      log.info("TextIn OCR Success, JSON FilePath: {}", jsonFilePath);
    }

    // 将响应 JSON 文件转换为 Markdown 文件
    processTextInResponse(jsonFilePath, mdFilePath);
  }

  /**
   * 处理 TextIn 响应 JSON 文件并转换为 Markdown
   *
   * @param jsonFilePath JSON 文件路径
   * @param mdFilePath   Markdown 文件路径
   */
  public static void processTextInResponse(Path jsonFilePath, Path mdFilePath) {
    try {
      // 读取 JSON 响应
      String jsonContent = Files.readString(jsonFilePath);
      JsonNode jsonNode = objectMapper.readTree(jsonContent);

      // 检查响应状态
      if (!jsonNode.has("code") || jsonNode.get("code").asInt() != 200) {
        String errorMessage = "TextIn API returned error: " +
            (jsonNode.has("message") ? jsonNode.get("message").asText() : "Unknown error");
        throw new BaseException(500, errorMessage, MODULE + ".processTextInResponse", jsonFilePath);
      }

      // 提取 markdown 内容
      if (jsonNode.has("result") && jsonNode.get("result").has("markdown")) {
        String markdown = jsonNode.get("result").get("markdown").asText();

        // 写入 Markdown 文件
        try (BufferedWriter writer = Files.newBufferedWriter(mdFilePath)) {
          writer.write(markdown);
          writer.flush();
        }

        log.info("TextIn response processed successfully, Markdown FilePath: {}", mdFilePath);
      } else {
        throw new BaseException(500, "No markdown content found in TextIn response",
            MODULE + ".processTextInResponse", jsonFilePath);
      }
    } catch (IOException e) {
      throw new BaseException(e, 500, MODULE + ".processTextInResponse", jsonFilePath, mdFilePath);
    }
  }

  /**
   * 创建默认的 TextIn OCR 请求参数
   *
   * @return 默认参数 Map
   */
  public static HashMap<String, Object> createDefaultOptions() {
    HashMap<String, Object> options = new HashMap<>();

    // 文档结构相关参数
    options.put("apply_document_tree", 1);      // 生成标题层级
    options.put("apply_merge", 1);              // 段落和表格合并
    options.put("catalog_details", 1);          // 返回目录信息

    // 图像处理相关参数
    options.put("apply_image_analysis", 1);     // 图像分析
    options.put("get_image", "objects");        // 返回页面内的子图像

    // 表格和公式处理
    options.put("table_flavor", "md");          // 表格使用 Markdown 格式
    options.put("formula_level", 1);            // 仅识别行间公式

    // 输出详细信息
    options.put("markdown_details", 1);         // 返回 detail 字段
    options.put("page_details", 1);             // 返回 pages 字段
    options.put("raw_ocr", 1);                  // 返回全部文字识别结果

    // 文档解析参数
    options.put("parse_mode", "scan");          // 按文字识别模式处理
    options.put("dpi", 216);                    // 高质量 DPI
    options.put("paratext_mode", "annotation"); // 非正文内容以注释格式插入

    return options;
  }

  /**
   * 创建自定义的 TextIn OCR 请求参数
   *
   * @param pageStart     起始页码（从1开始）
   * @param pageCount     处理页数
   * @param pdfPassword   PDF 密码（可选）
   * @param parseMode     解析模式：auto（综合模式）或 scan（识别模式）
   * @return 自定义参数 Map
   */
  public static HashMap<String, Object> createCustomOptions(Integer pageStart, Integer pageCount,
      String pdfPassword, String parseMode) {
    HashMap<String, Object> options = createDefaultOptions();

    if (pageStart != null && pageStart > 0) {
      options.put("page_start", pageStart);
    }

    if (pageCount != null && pageCount > 0) {
      options.put("page_count", pageCount);
    }

    if (pdfPassword != null && !pdfPassword.trim().isEmpty()) {
      options.put("pdf_pwd", pdfPassword);
    }

    if (("auto".equals(parseMode) || "scan".equals(parseMode))) {
      options.put("parse_mode", parseMode);
    }

    return options;
  }
}
