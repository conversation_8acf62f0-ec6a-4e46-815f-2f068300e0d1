package com.fxiaoke.file.process.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fxiaoke.file.process.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.domain.api.remote.mistral.Image;
import com.fxiaoke.file.process.domain.api.remote.mistral.MistralDoc;
import com.fxiaoke.file.process.domain.api.remote.mistral.MistralOcrRequest;
import com.fxiaoke.file.process.domain.api.remote.mistral.Page;
import com.fxiaoke.file.process.domain.constants.FileType;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.utils.FilenameUtil;
import com.fxiaoke.file.process.utils.MarkdownFormatUtil;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Base64;
import java.util.List;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.OkHttpClient.Builder;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Component
@RefreshScope
@Slf4j(topic = "MistralClient")
public class MistralClient {

  private static final String MODULE = "MistralOcrClient";
  private final OkHttpClient client;
  private final CmsPropertiesConfig config;
  private static final ObjectMapper objectMapper = new ObjectMapper();
  private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

  // 构造函数
  public MistralClient(CmsPropertiesConfig cmsPropertiesConfig) {
    this.config = cmsPropertiesConfig;
    Builder builder = new OkHttpClient().newBuilder();
    builder.connectTimeout(60, TimeUnit.SECONDS)
        .readTimeout(300, TimeUnit.SECONDS);
    String proxyHost = config.getMistralProxyHost();
    Integer proxyPort = config.getMistralProxyPort();
    if (proxyHost != null && proxyPort != null && proxyPort > 0) {
      builder.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort)));
    }
    this.client = builder.build();
  }

  // 执行 OCR 请求
  public void performMistralOcr(MistralOcrRequest mistralOcrRequest) throws IOException {
    // 将对象转换为 JSON
    String jsonBody = objectMapper.writeValueAsString(mistralOcrRequest);

    // 创建请求体
    RequestBody body = RequestBody.create(jsonBody, JSON);

    // 构建请求
    Request request = new Request.Builder()
        .url(config.getMistralEndpoint())
        .addHeader("Content-Type", "application/json")
        .addHeader("Authorization", "Bearer " + config.getMistralApiKey())
        .post(body)
        .build();

    // 执行请求并保存响应
    try (Response response = client.newCall(request).execute()) {

      if (!response.isSuccessful() || response.body() == null) {
        log.error("Mistral OCR failed: {}", response.message());
        throw new BaseException(500, response.message(), MODULE + ".performMistralOcr",
            mistralOcrRequest);
      }

      Path jsonFilePath = FilenameUtil.replaceExtension(mistralOcrRequest.getMdFilePath(),
          FileType.JSON.getFileType());

      // 使用流式处理保存响应内容到文件
      try (InputStream inputStream = response.body().byteStream();
           OutputStream outputStream = Files.newOutputStream(jsonFilePath)) {
          byte[] buffer = new byte[8192]; // 8KB 缓冲区
          int bytesRead;
          while ((bytesRead = inputStream.read(buffer)) != -1) {
              outputStream.write(buffer, 0, bytesRead);
          }
          outputStream.flush();
          log.info("Mistral OCR Vl Success, FilePath: {}", jsonFilePath);
      }

      // 将响应json文件转换为markdown文件
      processDocument(jsonFilePath, mistralOcrRequest.getMdFilePath());
    }
  }

  /**
   * 处理JSON文档并转换为Markdown
   *
   * @param jsonFilePath JSON文件路径
   */
  public static void processDocument(Path jsonFilePath, Path mdFilePath) {
    try {
      MistralDoc document = readDocument(jsonFilePath);
      processImages(document, jsonFilePath.getParent().toFile());
      generateMarkdown(document, mdFilePath);
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".processDocument", jsonFilePath);
    }
  }

  private static MistralDoc readDocument(Path mdFilePath) throws IOException {
    return objectMapper.readValue(mdFilePath.toFile(), MistralDoc.class);
  }

  private static void processImages(MistralDoc document, File outputDir) {
    document.getPages().stream()
        .filter(page -> page.getImages() != null)
        .flatMap(page -> page.getImages().stream())
        .filter(image -> image.getImageBase64() != null && !image.getImageBase64().trim().isEmpty()) // 只处理有base64数据的图片
        .forEach(image -> {
          try {
            saveImage(image, outputDir);
          } catch (IOException e) {
            throw new BaseException(500, e.getMessage(), MODULE + ".processImages", image);
          }
        });
  }

  private static void generateMarkdown(MistralDoc document, Path mdFilePath)
      throws IOException {
    try (BufferedWriter mdFile = Files.newBufferedWriter(mdFilePath)) {
      List<Page> pages = document.getPages();
      for (int i = 0; i < pages.size(); i++) {
        mdFile.write(pages.get(i).getMarkdown());
        if (i < pages.size() - 1) {
          mdFile.newLine();
          mdFile.write(MarkdownFormatUtil.horizontalRule());
          mdFile.newLine();
        }
      }
    }
  }

  private static void saveImage(Image image, File outputDir) throws IOException {
    String base64Data = image.getImageBase64().split(",")[1];
    byte[] imageBytes = Base64.getDecoder().decode(base64Data);
    File imageFile = new File(outputDir, image.getId());
    try (FileOutputStream fos = new FileOutputStream(imageFile)) {
      fos.write(imageBytes);
    }
  }
}
