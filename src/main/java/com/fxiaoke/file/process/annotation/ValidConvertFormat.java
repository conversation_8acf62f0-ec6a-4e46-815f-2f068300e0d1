package com.fxiaoke.file.process.annotation;


import com.fxiaoke.file.process.validation.ValidConvertFormatValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Constraint(validatedBy = ValidConvertFormatValidator.class)
@Target({ ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidConvertFormat {
  String message() default "Mismatch between source and target formats";
  Class<?>[] groups() default {};
  Class<? extends Payload>[] payload() default {};
}
