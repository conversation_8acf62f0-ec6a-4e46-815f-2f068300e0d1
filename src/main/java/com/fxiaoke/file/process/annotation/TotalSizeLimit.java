package com.fxiaoke.file.process.annotation;

import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.validation.TotalSizeLimitValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Constraint(validatedBy = TotalSizeLimitValidator.class)
@Target({ ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
public @interface TotalSizeLimit {
  String message() default "File total size exceeds limit or contains invalid sizes";
  int max() default Constants.BATCH_TO_MD_MAX_SIZE;
  Class<?>[] groups() default {};
  Class<? extends Payload>[] payload() default {};
}
