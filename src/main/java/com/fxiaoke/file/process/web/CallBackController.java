package com.fxiaoke.file.process.web;

import com.fxiaoke.file.process.domain.api.request.TencentCallbackReq;
import com.fxiaoke.file.process.domain.api.response.TencentCallbackRes;
import com.fxiaoke.file.process.service.CallBackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/FileProcess")
public class CallBackController {

  private final CallBackService callBackService;

  public CallBackController(CallBackService callBackService) {
    this.callBackService = callBackService;
  }

  /**
   * 处理腾讯云录音识别回调
   */
  @PostMapping(value = "/tencent/asr/callback",
      consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public TencentCallbackRes tencentAsrCallback(TencentCallbackReq callbackReq) {
    callBackService.tencentAsrCallback(callbackReq);
    return TencentCallbackRes.ok();
  }

}
