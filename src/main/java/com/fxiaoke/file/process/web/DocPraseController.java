package com.fxiaoke.file.process.web;

import com.fxiaoke.file.process.domain.api.R;
import com.fxiaoke.file.process.domain.api.request.BatchDocToMdArg;
import com.fxiaoke.file.process.domain.api.request.DocRanksStreamArg;
import com.fxiaoke.file.process.domain.api.request.JobGetContent;
import com.fxiaoke.file.process.domain.api.request.JobQueryArg;
import com.fxiaoke.file.process.domain.api.request.JobSubmitArg;
import com.fxiaoke.file.process.domain.api.response.BatchDocToMdResult;
import com.fxiaoke.file.process.domain.api.response.DocToMdJobSubmitResult;
import com.fxiaoke.file.process.domain.api.response.JobQueryResult;
import com.fxiaoke.file.process.domain.mapper.ArgMapper;
import com.fxiaoke.file.process.domain.mapper.CommonMapper;
import com.fxiaoke.file.process.service.ParseService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 文档解析控制器 处理文档解析相关的请求<br> 如doc、docx、ppt、pptx、xls、xlsx、pdf等文件的解析并转换为markdown格式
 */
@RestController
@RequestMapping("/FileProcess/Doc")
public class DocPraseController extends ParseController {


  public DocPraseController(ArgMapper argMapper,
      CommonMapper commonMapper,
      ParseService docParseService) {
    super(argMapper, commonMapper, docParseService);
  }

  @PostMapping("/SubmitJob")
  public R<DocToMdJobSubmitResult> submitJob(@Validated @RequestBody JobSubmitArg arg) {
    return toR(arg);
  }

  @PostMapping("/QueryJob")
  public R<JobQueryResult> queryJob(@Validated @RequestBody JobQueryArg arg) {
    return toR(arg);
  }

  @PostMapping("/GetContent")
  public void getContent(@Validated @RequestBody JobGetContent arg,
      HttpServletResponse response) {
    toR(arg, response);
  }

  @PostMapping("/RanksStream")
  public void getRanks(@Validated @RequestBody DocRanksStreamArg arg,
      HttpServletResponse response) {
    toR(arg, response);
  }

  @PostMapping("/BatchDocToMarkdown")
  public R<BatchDocToMdResult> batchDocToMarkdown(@Validated @RequestBody BatchDocToMdArg arg) {
    return toR(arg);
  }
}
