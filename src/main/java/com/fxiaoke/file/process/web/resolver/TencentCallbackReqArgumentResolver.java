package com.fxiaoke.file.process.web.resolver;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.file.process.domain.api.request.TencentCallbackReq;
import com.fxiaoke.file.process.domain.api.request.model.SentenceDetail;
import com.fxiaoke.file.process.domain.constants.ErrorCodes;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.utils.SecureCryptUtils;
import com.fxiaoke.file.process.utils.SignatureUtil;
import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * TencentCallbackReq 自定义参数解析器
 * <p>
 * 用于处理腾讯云录音识别回调请求参数的解析，特别是将 resultDetail 字符串参数 解析为 SentenceDetail 对象
 * </p>
 */
@Slf4j
@Component
public class TencentCallbackReqArgumentResolver implements HandlerMethodArgumentResolver {

  private static final String MODULE_NAME = "TencentCallbackResolver";

  @Override
  public boolean supportsParameter(MethodParameter parameter) {
    return TencentCallbackReq.class.equals(parameter.getParameterType());
  }

  @Override
  public Object resolveArgument(@NotNull MethodParameter parameter,
      ModelAndViewContainer mavContainer, NativeWebRequest webRequest,
      WebDataBinderFactory binderFactory) {

    HttpServletRequest request = webRequest.getNativeRequest(HttpServletRequest.class);
    if (request == null) {
      throw new BaseException(ErrorCodes.HTTP_BAD_REQUEST, "无法获取 HttpServletRequest",
          MODULE_NAME);
    }

    TencentCallbackReq callbackReq = new TencentCallbackReq();

    String jobId = request.getParameter("jobId");
    String sign = request.getParameter("sign");
    if (!StringUtils.hasText(jobId) || !StringUtils.hasText(sign)) {
      throw new BaseException(ErrorCodes.HTTP_BAD_REQUEST, "缺少必要的参数 jobId 或 sign",
          MODULE_NAME);
    }
    boolean valid = SignatureUtil.validateSignature(jobId, sign);
    if (!valid) {
      throw new BaseException(ErrorCodes.HTTP_BAD_REQUEST, "签名验证失败", MODULE_NAME, jobId,
          sign);
    }

    try {

      callbackReq.setJobId(jobId);
      callbackReq.setSign(sign);

      // 解析 code
      String codeStr = request.getParameter("code");
      if (StringUtils.hasText(codeStr)) {
        callbackReq.setCode(Integer.parseInt(codeStr));
      }

      callbackReq.setMessage(request.getParameter("message"));

      String requestIdStr = request.getParameter("requestId");
      if (StringUtils.hasText(requestIdStr)) {
        callbackReq.setRequestId(Long.parseLong(requestIdStr));
      }

      String appidStr = request.getParameter("appid");
      if (StringUtils.hasText(appidStr)) {
        callbackReq.setAppid(Long.parseLong(appidStr));
      }

      String projectidStr = request.getParameter("projectid");
      if (StringUtils.hasText(projectidStr)) {
        callbackReq.setProjectid(Integer.parseInt(projectidStr));
      }

      callbackReq.setAudioUrl(request.getParameter("audioUrl"));

      callbackReq.setText(request.getParameter("text"));

      String audioTimeStr = request.getParameter("audioTime");
      if (StringUtils.hasText(audioTimeStr)) {
        callbackReq.setAudioTime(Double.parseDouble(audioTimeStr));
      }

      String resultDetailStr = request.getParameter("resultDetail");
      if (StringUtils.hasText(resultDetailStr)) {
        List<SentenceDetail> sentenceDetails = JSON.parseArray(resultDetailStr,
            SentenceDetail.class);
        callbackReq.setResultDetail(sentenceDetails);
      }

      return callbackReq;
    } catch (Exception e) {
      throw new BaseException(e, ErrorCodes.HTTP_INTERNAL_SERVER_ERROR, MODULE_NAME, "参数解析失败");
    }
  }

}