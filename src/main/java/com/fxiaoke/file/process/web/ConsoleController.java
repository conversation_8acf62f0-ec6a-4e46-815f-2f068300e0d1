package com.fxiaoke.file.process.web;

import com.fxiaoke.file.process.dao.DocParseJobDao;
import com.fxiaoke.file.process.domain.api.R;
import com.fxiaoke.file.process.service.GridFsStore;
import com.fxiaoke.file.process.utils.StrUtils;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/FileProcess/Console")
@Slf4j(topic = "ConsoleController")
public class ConsoleController {

  private final GridFsStore gridFsStore;

  private final DocParseJobDao docParseJobDao;

  public ConsoleController(GridFsStore gridFsStore, DocParseJobDao docParseJobDao) {
    this.gridFsStore = gridFsStore;
    this.docParseJobDao = docParseJobDao;
  }

  @DeleteMapping("/clearAllCache")
  public R<String> deleteFilesBeforeDays(String pwd) {
    if (StrUtils.notBlank(pwd)&&pwd.equals("clearAllCache@2025")) {
      int metaCount = docParseJobDao.clearAllJobs();
      int fileCount = gridFsStore.deleteFilesBeforeDate(new Date());
      return R.ok("删除成功, metaCount: " + metaCount + ", fileCount: " + fileCount);
    }
    return R.ok("pwd 无效");
  }

  @DeleteMapping("/deleteJob")
  public R<String> deleteJob(String jobId) {
    if (StrUtils.notBlank(jobId)) {
      int count = docParseJobDao.deleteJob(jobId);
      return R.ok("删除成功, count: " + count);
    }
    return R.ok("jobId 无效");
  }

}
