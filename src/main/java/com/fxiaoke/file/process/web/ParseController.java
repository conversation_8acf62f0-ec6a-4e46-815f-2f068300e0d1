package com.fxiaoke.file.process.web;

import com.fxiaoke.file.process.domain.api.R;
import com.fxiaoke.file.process.domain.api.request.BatchDocToMdArg;
import com.fxiaoke.file.process.domain.api.request.DocRanksStreamArg;
import com.fxiaoke.file.process.domain.api.request.JobGetContent;
import com.fxiaoke.file.process.domain.api.request.JobQueryArg;
import com.fxiaoke.file.process.domain.api.request.JobSubmitArg;
import com.fxiaoke.file.process.domain.api.response.BatchDocToMdResult;
import com.fxiaoke.file.process.domain.api.response.DocToMdJobSubmitResult;
import com.fxiaoke.file.process.domain.api.response.JobQueryResult;
import com.fxiaoke.file.process.domain.entity.DocParseJob;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.mapper.ArgMapper;
import com.fxiaoke.file.process.domain.mapper.CommonMapper;
import com.fxiaoke.file.process.service.ParseService;
import jakarta.servlet.http.HttpServletResponse;
import java.io.BufferedWriter;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;

@Slf4j(topic = "ParseController")
public class ParseController {

  private static final String MODULE = "ParseController";

  private final ArgMapper argMapper;
  private final CommonMapper commonMapper;
  private final ParseService docParseService;

  public ParseController(ArgMapper argMapper, CommonMapper commonMapper,
      ParseService docParseService) {
    this.argMapper = argMapper;
    this.commonMapper = commonMapper;
    this.docParseService = docParseService;
  }

  protected R<DocToMdJobSubmitResult> toR(JobSubmitArg arg) {
    try {
      logger("toR JobSubmitArg start, arg:{}", arg);
      String jobId = docParseService.submitJob(argMapper.toJobParseArg(arg));
      logger("toR JobSubmitArg end, arg:{}, jobId:{}", arg, jobId);
      return R.ok(DocToMdJobSubmitResult.of(jobId));
    } catch (BaseException e) {
      throw e;
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".submitJob", arg);
    }
  }

  protected R<JobQueryResult> toR(JobQueryArg arg) {
    logger("toR JobQueryArg start, arg:{}", arg);
    try {
      Optional<DocParseJob> docParseJobOpt = docParseService.queryJob(arg.getJobId());
      if (docParseJobOpt.isPresent()) {
        DocParseJob docParseJob = docParseJobOpt.get();
        JobQueryResult jobQueryResult = commonMapper.toJobQueryResult(docParseJob);
        logger("toR JobQueryArg end, result:{}", arg);
        return R.ok(jobQueryResult);
      }
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".queryJob", arg);
    }
    throw new BaseException(400, "jobId not found", MODULE, arg);
  }

  protected void toR(JobGetContent arg, HttpServletResponse response) {
    logger("toR JobGetContent start, arg:{}", arg);
    try (InputStream stream = docParseService.getContent(arg)) {
      IOUtils.copy(stream, response.getOutputStream());
    } catch (BaseException e) {
      throw e;
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".getContent", arg);
    }
    logger("toR JobGetContent end, arg:{}", arg);
  }

  protected void toR(DocRanksStreamArg arg, HttpServletResponse response) {
    logger("toR DocRanksStreamArg start, arg:{}", arg);
    try (BufferedWriter writer = new BufferedWriter(
        new OutputStreamWriter(response.getOutputStream(), StandardCharsets.UTF_8))) {
      docParseService.ranksStream(arg, writer);
      writer.flush();
    } catch (BaseException e) {
      throw e;
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".ranksStream", arg);
    }
    logger("toR DocRanksStreamArg end, arg:{}", arg);
  }

  protected R<BatchDocToMdResult> toR(BatchDocToMdArg arg) {
    try {
      logger("toR BatchDocToMdArg start, arg:{}", arg);
      BatchDocToMdResult batchDocToMdResult = docParseService.batchDocToMd(arg);
      logger("toR BatchDocToMdArg end, arg:{}, result:{}", arg, batchDocToMdResult);
      return R.ok(batchDocToMdResult);
    } catch (BaseException e) {
      throw e;
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".batchDocToMd", arg);
    }
  }

  private void logger(String template, Object arg) {
    log.info(template, arg);
  }

  private void logger(String template, Object arg, Object arg2) {
    log.info(template, arg, arg2);
  }
}
