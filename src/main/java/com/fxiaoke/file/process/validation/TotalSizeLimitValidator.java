package com.fxiaoke.file.process.validation;

import com.fxiaoke.file.process.annotation.TotalSizeLimit;
import com.fxiaoke.file.process.domain.api.request.model.DocInfo;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.List;

public class TotalSizeLimitValidator implements ConstraintValidator<TotalSizeLimit, List<DocInfo>> {

  private int maxSize;

  @Override
  public void initialize(TotalSizeLimit constraintAnnotation) {
    this.maxSize = constraintAnnotation.max();
  }

  @Override
  public boolean isValid(List<DocInfo> docInfos, ConstraintValidatorContext context) {

    if (docInfos == null) {
      return false;
    }

    int totalSize = 0;
    for (DocInfo docInfo : docInfos) {
      Integer size = docInfo.getSize();
      if (size == null || size == 0) {
        return false;
      }
      totalSize += size;
    }

    return totalSize <= maxSize;
  }
}
