package com.fxiaoke.file.process.validation;

import com.fxiaoke.file.process.annotation.ValidConvertFormat;
import com.fxiaoke.file.process.domain.api.request.JobSubmitArg;
import com.fxiaoke.file.process.domain.exception.BusException;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * 文档转换格式验证器 用于验证文档转换参数的合法性
 */
public class ValidConvertFormatValidator implements
    ConstraintValidator<ValidConvertFormat, JobSubmitArg> {

  private static final String MODULE = "ValidConvertFormatValidator";

  // 文档类型
  private static final Set<String> DOCUMENT_TYPES = Set.of("pdf", "doc", "docx", "html");

  // 演示文稿类型
  private static final Set<String> PRESENTATION_TYPES = Set.of("ppt", "pptx");

  // Excel文件类型
  private static final Set<String> EXCEL_TYPES = Set.of("xls", "xlsx");

  // 图片类型
  private static final Set<String> IMAGE_TYPES = Set.of("jpg", "jpeg", "png", "gif", "webp", "bmp");

  // 音频类型
  private static final Set<String> AUDIO_TYPES = Set.of("wav","mp3","m4a","wma","amr","aac","opus","flac");

  // 视频类型
  private static final Set<String> VIDEO_TYPES = Set.of("flv","mp4","3gp");

  // 全部的支持的文件类型
  private static final Set<String> SUPPORTED_SOURCE_TYPES;

  static {
    Set<String> allTypes = new HashSet<>();
    allTypes.addAll(DOCUMENT_TYPES);
    allTypes.addAll(PRESENTATION_TYPES);
    allTypes.addAll(EXCEL_TYPES);
    allTypes.addAll(IMAGE_TYPES);
    allTypes.addAll(AUDIO_TYPES);
    allTypes.addAll(VIDEO_TYPES);
    SUPPORTED_SOURCE_TYPES = Collections.unmodifiableSet(allTypes);
  }

  private static final Set<String> SUPPORTED_TARGET_TYPES = Set.of("md");

  /**
   * 验证文档转换参数是否有效
   *
   * @param arg     文档转换参数
   * @param context 验证上下文
   * @return 是否有效
   */
  @Override
  public boolean isValid(JobSubmitArg arg, ConstraintValidatorContext context) {
    if (!isValidBasicCheck(arg, context)) {
      return false;
    }

    // 验证 employeeId
    if (!isValidEmployeeId(arg.getEmployeeId(), context)) {
      return false;
    }

    String sourceType = arg.getSourceType().toLowerCase();
    String targetType = arg.getTargetType().toLowerCase();

    // 验证文件类型是否支持
    if (!isValidFileTypes(sourceType, targetType, context)) {
      return false;
    }

    // 图片类型解析,OCR 必须开启
    if (IMAGE_TYPES.contains(sourceType) && !arg.isOcr()) {
      throw BusException.ocrRequired(MODULE + ".isValid", arg);
    }

    // 验证特殊转换规则
    return isValidConversionRule(sourceType, targetType, context);
  }

  private boolean isValidEmployeeId(Integer employeeId, ConstraintValidatorContext context) {
    if (employeeId == null || (employeeId != -10000 && employeeId <= 0)) {
      addConstraintViolation(context, "employeeId must be -10000 or greater than 0");
      return false;
    }
    return true;
  }

  /**
   * 基础检查：参数是否为空
   */
  private boolean isValidBasicCheck(JobSubmitArg arg, ConstraintValidatorContext context) {
    if (arg == null) {
      addConstraintViolation(context, "The parameter cannot be null");
      return false;
    }

    if (arg.getSourceType() == null || arg.getTargetType() == null) {
      addConstraintViolation(context, "The source type and target type cannot be empty");
      return false;
    }

    return true;
  }

  /**
   * 验证文件类型是否在支持范围内
   */
  private boolean isValidFileTypes(String sourceType, String targetType,
      ConstraintValidatorContext context) {
    if (!SUPPORTED_SOURCE_TYPES.contains(sourceType)) {
      addConstraintViolation(context,
          String.format("Unsupported source file type: %s", sourceType));
      return false;
    }

    if (!SUPPORTED_TARGET_TYPES.contains(targetType)) {
      addConstraintViolation(context,
          String.format("Unsupported destination file type: %s", targetType));
      return false;
    }

    return true;
  }

  /**
   * 验证特殊转换规则 目前规则：只有Excel文件才能转换为JSON格式
   */
  private boolean isValidConversionRule(String sourceType, String targetType,
      ConstraintValidatorContext context) {
    if ("json".equals(targetType) && !EXCEL_TYPES.contains(sourceType)) {
      addConstraintViolation(context,
          "Only Excel files (xls/xlsx) can be converted to JSON format");
      return false;
    }
    return true;
  }

  /**
   * 添加约束违反信息
   */
  private void addConstraintViolation(ConstraintValidatorContext context, String message) {
    context.disableDefaultConstraintViolation();
    context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
  }
}
