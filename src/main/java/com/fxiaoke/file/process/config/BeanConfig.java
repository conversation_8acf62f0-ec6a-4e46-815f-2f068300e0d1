package com.fxiaoke.file.process.config;

import com.facishare.fsi.proxy.FsiServiceProxyFactory;
import com.facishare.fsi.proxy.FsiServiceProxyFactoryBean;
import com.facishare.fsi.proxy.service.AFileStorageService;
import com.facishare.fsi.proxy.service.GFileStorageService;
import com.facishare.restful.client.FRestApiProxyFactoryBean;
import com.facishare.stone.sdk.StoneProxyApi;
import com.fxiaoke.common.http.spring.HttpSupportFactoryBean;
import com.fxiaoke.file.process.client.MistralClient;
import com.github.mongo.support.MongoDataStoreFactoryBean;
import com.tencentcloudapi.asr.v20190614.AsrClient;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.volcengine.ark.runtime.service.ArkService;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.Proxy.Type;
import java.net.URI;
import java.time.Duration;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j(topic = "BeanConfig")
public class BeanConfig {

  @Bean
  public HttpSupportFactoryBean httpClientSupport() {
    HttpSupportFactoryBean factoryBean = new HttpSupportFactoryBean();
    factoryBean.init();
    return factoryBean;
  }

  @Bean
  public MongoDataStoreFactoryBean dpsDataStore(CmsPropertiesConfig config){
    MongoDataStoreFactoryBean mongoDataStoreFactoryBean = new MongoDataStoreFactoryBean();
    mongoDataStoreFactoryBean.setConfigName(config.getMongoConfigName());
    return mongoDataStoreFactoryBean;
  }

  @Bean
  public FRestApiProxyFactoryBean<StoneProxyApi> stoneProxyApi() {
    FRestApiProxyFactoryBean<StoneProxyApi> factoryBean = new FRestApiProxyFactoryBean<>();
    factoryBean.setType(StoneProxyApi.class);
    return factoryBean;
  }

  @Bean
  public FsiServiceProxyFactory fsiServiceProxyFactory(CmsPropertiesConfig config) {
    FsiServiceProxyFactory factory = new FsiServiceProxyFactory();
    factory.setConfigKey(config.getFsiProxyConfigName());
    factory.init();
    return factory;
  }

  @Bean
  public FsiServiceProxyFactoryBean<GFileStorageService> gFileStorageService(FsiServiceProxyFactory fsiServiceProxyFactory) {
    FsiServiceProxyFactoryBean<GFileStorageService> factoryBean = new FsiServiceProxyFactoryBean<>();
    factoryBean.setType(GFileStorageService.class);
    factoryBean.setFactory(fsiServiceProxyFactory);
    return factoryBean;
  }

  @Bean
  public FsiServiceProxyFactoryBean<AFileStorageService> aFileStorageService(FsiServiceProxyFactory fsiServiceProxyFactory) {
    FsiServiceProxyFactoryBean<AFileStorageService> factoryBean = new FsiServiceProxyFactoryBean<>();
    factoryBean.setType(AFileStorageService.class);
    factoryBean.setFactory(fsiServiceProxyFactory);
    return factoryBean;
  }

  @Bean
  @RefreshScope
  public ArkService arkService(ArkConfig arkConfig){
    // 最多 72 个空闲连接，30 秒后回收
    ConnectionPool connectionPool = new ConnectionPool(arkConfig.getMaxIdleConnections(), arkConfig.getKeepAliveDuration(), TimeUnit.SECONDS);

    // 设置最大请求数和每个主机的最大请求数
    Dispatcher dispatcher = new Dispatcher();
    dispatcher.setMaxRequests(arkConfig.getMaxRequests());
    dispatcher.setMaxRequestsPerHost(arkConfig.getMaxRequestsPerHost());

    String proxyHost = arkConfig.getProxyHost();
    Integer proxyPort = arkConfig.getProxyPort();
    Duration connectionTimeout = Duration.ofSeconds(arkConfig.getConnectionTimeout());
    Duration readTimeout = Duration.ofSeconds(arkConfig.getReadTimeout());
    Duration callTimeout = Duration.ofSeconds(arkConfig.getCallTimeout());
    Integer arkMaxRetries = arkConfig.getMaxRetries();
    ArkService.Builder arkServiceBuilder = ArkService.builder()
        .dispatcher(dispatcher)
        .connectionPool(connectionPool)
        .baseUrl(arkConfig.getEndpoint())
        .region(arkConfig.getRegion())
        .apiKey(arkConfig.getApiKey())
        .connectTimeout(connectionTimeout)
        .timeout(readTimeout)
        .callTimeout(callTimeout)
        .retryTimes(arkMaxRetries);

    if (proxyHost != null && proxyPort != null && proxyPort > 0) {
      Proxy proxy = new Proxy(Type.HTTP, new InetSocketAddress(proxyHost, proxyPort));
      arkServiceBuilder.proxy(proxy);
      log.info("Ark Client using proxy: {}:{}", proxyHost, proxyPort);
    }

    log.info("Ark Client initialized with config: {}", arkConfig);
    // 创建 ArkService 实例
    return arkServiceBuilder.build();
  }

  @Bean
  @RefreshScope
  public AsrClient asrClient(AsrConfig asrConfig){
    Credential cred = new Credential(asrConfig.getSecretId(), asrConfig.getSecretKey());
    ClientProfile clientProfile = new ClientProfile();
    String proxyHost = asrConfig.getProxyHost();
    Integer proxyPort = asrConfig.getProxyPort();
    HttpProfile httpProfile = new HttpProfile();
    httpProfile.setEndpoint(asrConfig.getEndpoint());
    if (proxyHost != null && proxyPort != null && proxyPort > 0) {
      httpProfile.setProxyHost(proxyHost);
      httpProfile.setProxyPort(proxyPort);
    }
    clientProfile.setHttpProfile(httpProfile);
    return new AsrClient(cred, asrConfig.getRegion(), clientProfile);
  }
}
