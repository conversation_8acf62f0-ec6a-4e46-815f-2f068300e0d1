package com.fxiaoke.file.process.config;

import com.fxiaoke.file.process.web.resolver.TencentCallbackReqArgumentResolver;
import com.github.filter.CoreFilter;
import java.util.List;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {

  private final TencentCallbackReqArgumentResolver tencentCallbackReqArgumentResolver;

  public WebConfig(TencentCallbackReqArgumentResolver tencentCallbackReqArgumentResolver) {
    this.tencentCallbackReqArgumentResolver = tencentCallbackReqArgumentResolver;
  }

  @Bean
  public FilterRegistrationBean<CoreFilter> coreFilter(){
    CoreFilter coreFilter=new CoreFilter();
    FilterRegistrationBean<CoreFilter> registrationBean = new FilterRegistrationBean<>();
    registrationBean.setFilter(coreFilter);
    registrationBean.setOrder(0);
    registrationBean.addUrlPatterns("/FileProcess/*");
    return registrationBean;
  }

  @Override
  public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
    // 添加 TencentCallbackReq 自定义参数解析器
    resolvers.add(tencentCallbackReqArgumentResolver);
  }
}
