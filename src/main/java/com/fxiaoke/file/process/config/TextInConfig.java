package com.fxiaoke.file.process.config;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@ToString(exclude = {"secretCode"})
@Configuration
@ConfigurationProperties(prefix = "cms.file.process.textin")
public class TextInConfig {

  private String appid;
  private String secretCode;
  private String baseUrl;
}
