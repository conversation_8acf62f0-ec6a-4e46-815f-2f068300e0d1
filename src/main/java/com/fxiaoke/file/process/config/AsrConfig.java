package com.fxiaoke.file.process.config;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@ToString(exclude = {"secretKey"})
@Configuration
@ConfigurationProperties(prefix = "cms.file.process.asr")
public class AsrConfig {

  private String secretId;
  private String secretKey;

  private String endpoint;
  private String region;
  private String proxyHost;
  private Integer proxyPort;

  private String callBackUrlTemplate;
}
