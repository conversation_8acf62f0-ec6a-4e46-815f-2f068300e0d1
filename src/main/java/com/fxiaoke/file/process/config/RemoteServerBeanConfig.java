package com.fxiaoke.file.process.config;

import com.alibaba.dubbo.config.ApplicationConfig;
import com.alibaba.dubbo.config.RegistryConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;
import org.springframework.core.env.Environment;

@Configuration
@ImportResource(locations = {
    "classpath*:spring/ei-ea-converter.xml",
    "classpath*:META-INF/fs-paas-ai-client.xml",
})
public class RemoteServerBeanConfig {

  @Bean
  public ApplicationConfig applicationConfig(Environment env) {
    ApplicationConfig applicationConfig = new ApplicationConfig();
    applicationConfig.setName(env.getProperty("spring.application.name"));
    return applicationConfig;
  }

  @Bean
  public RegistryConfig registryConfig(CmsPropertiesConfig config) {
    RegistryConfig registryConfig = new RegistryConfig();
    registryConfig.setAddress(config.getZookeeper());
    return registryConfig;
  }
}
