package com.fxiaoke.file.process.config;

import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.domain.constants.VisCompPrompt;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "cms.file.process")
public class CmsPropertiesConfig {

  // ====== 服务基础配置 ======

  // zookeeper 地址
  private String zookeeper;
  // MongoDB配置文件名
  private String mongoConfigName;
  // fsi sdk 配置文件
  private String fsiProxyConfigName;

  // MQ配置文件名
  private String mqConfigName;
  // MQ 配置块儿-解析任务
  private String mqConvertSection;
  // MQ 配置块儿-token上报、幂等、异步任务唤醒
  private String mqAssistSection;

  // === 文件缓存清理任务 相关配置 ===
  // 本地缓存目录
  private String localCacheDirPath;

  // === 大附件服务 ====
  // http://${variables_endpoint.svc_big_file}/fs-big-file-manager-biz/api/getFileMetaByFilePath?ea=%s&filePath=%s
  private String findBigFileMetaUrl;
  //
  private String bigFileS3User;

  // ====== S3 相关配置 ======
  private String s3User;
  private String s3InsideUser;
  private int s3PreSignUrlExpires;

  private Integer s3BatchSize = 2;
  private Integer s3TimeoutSeconds = 30;

  // ====== Mistral OCR 相关配置 ======
  private String mistralApiKey;
  private String mistralEndpoint;
  private String mistralProxyHost;
  private Integer mistralProxyPort;

  // === 视觉模型配置 ===

  // 文件识别默认模型配置
  private String vlModel = Constants.VL_DEFAULT_MODEL;

  // 单图片识别模型配置
  private String imageSystemPrompt = VisCompPrompt.ARK_IMAGE_SYSTEM_PROMPT;
  private String imageUserPrompt = VisCompPrompt.ARK_IMAGE_USER_PROMPT;

  // 文档全页识别模型配置
  private String fullSystemPrompt = VisCompPrompt.COMMON_IMAGE_SYSTEM_PROMPT;
  private String fullUserPrompt = VisCompPrompt.FULL_PAGE_IMAGE_USER_PROMPT;

  // 文档配图识别模型配置
  private String illustrationSystemPrompt = VisCompPrompt.COMMON_IMAGE_SYSTEM_PROMPT;
  private String illustrationUserPrompt = VisCompPrompt.ILLUSTRATION_USER_PROMPT;
}
