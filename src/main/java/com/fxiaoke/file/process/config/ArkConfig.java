package com.fxiaoke.file.process.config;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@ToString(exclude = {"apiKey"})
@Configuration
@ConfigurationProperties(prefix = "cms.file.process.ark")
public class ArkConfig {

  // === 火山引擎 Ark 视觉模型配置 ===
  private String apiKey;

  private String endpoint;
  private String region;

  // 连接超时：建立TCP连接的最长时间 (网络不通或服务器不响应连接请求)
  private Integer connectionTimeout = 10; // 连接超时，单位秒
  // 读取超时：等待服务器响应数据的最长时间 (连接建立成功，但服务器处理时间过长)
  private Integer readTimeout = 30; // 读取超时，单位秒
  // 调用超时：整个请求-响应周期的最长时间 (整个请求过程超过总时间限制)
  private Integer callTimeout = 40; // 调用超时，单位秒
  // 最大重试次数：在请求失败时，最多重试的次数
  private Integer maxRetries = 2; // 最大重试次数

  private Integer maxRequests = 64; // 最大请求数：每个连接允许的最大请求数量
  private Integer maxRequestsPerHost = 32; // 每个主机的最大请求数

  private Integer maxIdleConnections = 5; // 最大空闲连接数
  private Integer keepAliveDuration = 300; // 空闲连接保持时间，单位秒

  // 客户端-代理配置
  private String proxyHost;
  private Integer proxyPort;

  // 并发请求数：同时处理的最大请求数量
  private Integer batchSize = 2; // 最大批处理大小
  // 批处理超时：批处理操作的最长等待时间 (在批处理模式下，等待新请求的最长时间)
  private Integer batchTimeout = 70; // 批处理超时，单位秒
}
