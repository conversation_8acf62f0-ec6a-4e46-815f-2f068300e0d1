package com.fxiaoke.file.process.config;

import com.fxiaoke.file.process.domain.model.ModelPrice;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "model.pricing")
public class ModelPricingPropertiesConfig {
  // === 模型价格配置 ===
  private Map<String, ModelPrice> models = new HashMap<>();

  public ModelPrice getModelPrice(String modelName) {
    return models.get(modelName);
  }
}
