package com.fxiaoke.file.process.utils;

public class FileServerUtil {

  private FileServerUtil() {
  }

  public static boolean isNFile(String path) {
    return path.startsWith("N_") || path.startsWith("TN_");
  }

  public static boolean isCFile(String path) {
    return path.startsWith("C_") || path.startsWith("TC_");
  }

  public static boolean isAFile(String path) {
    return path.startsWith("A_") || path.startsWith("TA_");
  }

  public static boolean isGFile(String path) {
    return path.startsWith("G_");
  }

  public static boolean isBigFile(String path) {
    return path.startsWith("ALIOSS_");
  }
}
