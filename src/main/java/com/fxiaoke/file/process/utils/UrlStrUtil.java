package com.fxiaoke.file.process.utils;

import java.util.Optional;

public class UrlStrUtil {

  private static final String HTTP = "http://";
  private static final String HTTPS = "https://";

  private static final String GET_BY_PATH = "/FSC/EM/File/GetByPath";

  private static final String IMAGE = "/image/";
  private static final String IMAGE_I = "/image/i/";
  private static final String IMAGE_O = "/image/o/";
  private static final String IMAGE_S = "/image/s/";

  private static final String PATH_MARK = "path=";
  private static final String PARAM_MARK = "&";

  public static Optional<String> parseUrl(String src) {
    if (!isValidUrl(src)) {
      return Optional.empty();
    }

    try {
      String result = null;

      if (src.contains(GET_BY_PATH)) {
        result = extractPattern(src, PATH_MARK, PARAM_MARK);
      } else if (src.contains(IMAGE)) {
        result = extractImagePattern(src);
      }

      return isValidResult(result) ? Optional.of(result) : Optional.empty();

    } catch (Exception e) {
      return Optional.empty();
    }
  }

  private static boolean isValidUrl(String url) {
    return url != null && !url.trim().isEmpty() && (url.startsWith(HTTP) || url.startsWith(HTTPS));
  }

  private static String extractPattern(String src, String startMark, String endMark) {
    try {
      int startIndex = src.indexOf(startMark);
      if (startIndex == -1) {
        return null;
      }

      String result = src.substring(startIndex + startMark.length());

      if (endMark != null) {
        int endIndex = result.indexOf(endMark);
        if (endIndex != -1) {
          result = result.substring(0, endIndex);
        }
      }

      return result;
    } catch (Exception e) {
      return null;
    }
  }

  private static String extractImagePattern(String src) {
    try {
      // 处理三种image模式: i, o, s
      String pattern;
      if (src.contains(IMAGE_I)) {
        pattern = IMAGE_I;
      } else if (src.contains(IMAGE_O)) {
        pattern = IMAGE_O;
      } else if (src.contains(IMAGE_S)) {
        pattern = IMAGE_S;
      } else {
        return null;
      }

      String result = extractPattern(src, pattern, "/");

      if (result == null) {
        return null;
      }

      // 特殊处理image/i/模式下的文件扩展名
      if (pattern.equals(IMAGE_I)) {
        int dotIndex = result.indexOf(".");
        if (dotIndex != -1) {
          result = result.substring(0, dotIndex);
        }
      }

      return result;
    } catch (Exception e) {
      return null;
    }
  }

  private static boolean isValidResult(String result) {
    return result != null && !result.trim().isEmpty();
  }
}
