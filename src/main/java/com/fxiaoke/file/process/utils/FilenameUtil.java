package com.fxiaoke.file.process.utils;

import java.nio.file.Path;
import org.apache.commons.io.FilenameUtils;

public class FilenameUtil {

  private FilenameUtil() {
  }

  /**
   * 获取文件名
   *
   * @param imagePath 文件名或路径
   * @return 文件名.扩展名
   */
  public static String getName(String imagePath) {
    return FilenameUtils.getName(imagePath);
  }

  /**
   * 获取文件名不带扩展名
   *
   * @param imagePath 文件名或路径
   * @return 文件名
   */
  public static String getBaseName(String imagePath) {
    return FilenameUtils.getBaseName(imagePath);
  }

  /**
   * 获取文件扩展名
   *
   * @param filename 文件名或路径
   * @return 文件扩展名
   */
  public static String getFileExtension(Path filename) {
    return FilenameUtils.getExtension(filename.toString());
  }

  /**
   * 替换文件扩展名
   *
   * @param filePath 源文件路径
   * @param ext      替换的扩展名(无需包含".")
   * @return 替换扩展名后的新路径
   * @throws IllegalArgumentException 如果参数为null或扩展名为空
   */
  public static Path replaceExtension(Path filePath, String ext) {

    if (filePath == null || ext == null || ext.isEmpty()) {
      throw new IllegalArgumentException("filePath 和 ext 不能为空");
    }

    String fileName = filePath.getFileName().toString();
    StringBuilder newFileName = new StringBuilder(FilenameUtils.removeExtension(fileName))
        .append('.')
        .append(ext.startsWith(".") ? ext.substring(1) : ext);

    return filePath.resolveSibling(newFileName.toString());
  }

  /**
   * 根据文件路径与新文件名在同一目录下生成一个新的文件路径
   *
   * @param filePath 文件路径
   * @return 文件名
   */
  public static String resolveSibling(Path filePath, String newFileName, String extension) {
    return filePath.resolveSibling(newFileName + "." + extension).toString();
  }

  /**
   * 根据文件路径在文件名后追加后缀
   * @param filePath 文件路径
   * @return 文件名
   */
  public static Path fileNameAppendAFSuffix(Path filePath) {
    String fileName = filePath.getFileName().toString();
    String baseName = FilenameUtils.getBaseName(fileName);
    String extension = FilenameUtils.getExtension(fileName);
    String newFileName = baseName + "AF." + extension;
    return filePath.resolveSibling(newFileName);
  }

}
