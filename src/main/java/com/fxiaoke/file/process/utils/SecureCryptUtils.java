package com.fxiaoke.file.process.utils;

import com.fxiaoke.common.Guard;
import com.fxiaoke.stone.commons.domain.constant.Constants;
import com.google.common.base.Joiner;

public class SecureCryptUtils {

  private SecureCryptUtils() {
  }

  private static final Guard GUARD = new Guard("sxqGay8eNsLWHnge");


  public static String generatorRaw(Object... args) {
    return Joiner.on(Constants.DELIMITER).join(args);
  }

  /**
   * 多次加密生成相同的密文
   * @param raw 原始字符串
   * @return 加密后的字符串
   */
  public static String encry(String raw) {
    return GUARD.encode2(raw);
  }

  public static String decry(String ciphertext) {
    return GUARD.decode(ciphertext);
  }

}