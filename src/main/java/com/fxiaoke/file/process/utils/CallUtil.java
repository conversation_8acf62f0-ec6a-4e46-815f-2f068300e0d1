package com.fxiaoke.file.process.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.file.process.domain.api.R;
import com.fxiaoke.file.process.domain.exception.BaseException;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class CallUtil {

  private static final String MODULE = "CallUtil";

  public static String post(OkHttpSupport client, String serverUrl, RequestBody body) {
    Request request = new Request.Builder().url(serverUrl).post(body).build();
    try {
      return (String) client.syncExecute(request, new SyncCallback() {
        @Override
        public Object response(Response response) throws Exception {
          if (response.body() == null || response.body().string().isEmpty()) {
            throw new BaseException(500, "Call service Response body is null or empty",
                MODULE + ".post", serverUrl, body);
          }
          return response.body().string();
        }
      });
    } catch (Exception e) {
      throw new BaseException(e, 500, "Call service fail", serverUrl, body);
    }
  }

  public static String get(OkHttpSupport client, String serverUrl) {
    Request request = new Request.Builder().url(serverUrl).get().build();
    try {
      return (String) client.syncExecute(request, new SyncCallback() {
        @Override
        public Object response(Response response) throws Exception {
          if (response.body() == null) {
            throw new BaseException(500, "Call service Response body is null", MODULE + ".get",
                serverUrl);
          }
          return response.body().string();
        }
      });
    } catch (Exception e) {
      throw new BaseException(e, 500, "Call service fail", serverUrl);
    }
  }

  public static <T> R<T> formJson(String json, Class<T> clazz) {
    try {
      return JSON.parseObject(json, new TypeReference<>(clazz) {
      });
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".formJson", json, clazz);
    }
  }


}
