package com.fxiaoke.file.process.utils;

import com.fxiaoke.common.Guard;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

@Slf4j(topic = "GuardUtil")
public class GuardUtil {

  private static final Guard guard = new Guard("1111111111111111");

  public static Optional<String> decry(String sToken) {
    try {
      return Optional.of(guard.decode(sToken));
    } catch (Exception e) {
      // 如果解密失败，返回空的Optional
      log.warn("GuardUtil decry fail,sToken: {}", sToken, e);
      return Optional.empty();
    }
  }
}
