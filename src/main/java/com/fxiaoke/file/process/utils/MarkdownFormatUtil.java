package com.fxiaoke.file.process.utils;

/**
 * Markdown 格式化工具类
 */
public class MarkdownFormatUtil {

  private MarkdownFormatUtil() {
  }

  public static final String LINE_SEPARATOR = "\n";

  public static final String HTML_LINE_SEPARATOR = "<br>";

  // 标题相关
  public static String h1(String text) {
    return String.format("# %s", text);
  }

  public static String h2(String text) {
    return String.format("## %s", text);
  }

  public static String h3(String text) {
    return String.format("### %s", text);
  }

  public static String h4(String text) {
    return String.format("#### %s", text);
  }

  public static String h5(String text) {
    return String.format("##### %s", text);
  }

  public static String h6(String text) {
    return String.format("###### %s", text);
  }

  // 文本样式
  public static String bold(String text) {
    return String.format("**%s**", text);
  }

  public static String italic(String text) {
    return String.format("*%s*", text);
  }

  public static String strikethrough(String text) {
    return String.format("~~%s~~", text);
  }

  public static String code(String text) {
    return String.format("`%s`", text);
  }

  public static String codeBlock(String text) {
    return String.format("```%n%s%n```", text);
  }

  public static String codeBlock(String language, String text) {
    return String.format("```%s%n%s%n```", language, text);
  }

  // 链接和图片
  public static String link(String text, String url) {
    return String.format("[%s](%s)", text, url);
  }

  public static String image(String altText, String url) {
    return String.format("![%s](%s)", altText, url);
  }

  // 列表
  public static String unorderedListItem(String text) {
    return String.format("- %s", text);
  }

  public static String orderedListItem(int number, String text) {
    return String.format("%d. %s", number, text);
  }

  // 引用
  public static String blockquote(String text) {
    return String.format("> %s", text);
  }

  // 分隔线
  public static String horizontalRule() {
    return "---";
  }

  // 判断是否是表格行
  public static boolean isTableRow(String line) {
    return line.startsWith("|") && line.endsWith("|");
  }

  // 表格
  public static String table(String[] headers, String[][] rows) {
    StringBuilder sb = new StringBuilder();

    // 添加表头
    sb.append("|");
    for (String header : headers) {
      sb.append(" ").append(header).append(" |");
    }
    sb.append(System.lineSeparator());

    // 添加分隔行
    sb.append("|");
    sb.append(" --- |".repeat(headers.length));
    sb.append(System.lineSeparator());

    // 添加数据行
    for (String[] row : rows) {
      sb.append("|");
      for (String cell : row) {
        sb.append(" ").append(cell).append(" |");
      }
      sb.append(System.lineSeparator());
    }

    return sb.toString();
  }

  // 任务列表
  public static String taskList(String text, boolean completed) {
    return String.format("- [%s] %s", completed ? "x" : " ", text);
  }

  // 段落
  public static String paragraph(String text) {
    return text + System.lineSeparator() + System.lineSeparator();
  }
}
