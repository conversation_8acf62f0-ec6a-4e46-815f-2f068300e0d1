package com.fxiaoke.file.process.utils;

import static com.fxiaoke.file.process.domain.constants.Constants.SINGLE_IMAGE_TO_MD_MAX_SIZE;

import com.fxiaoke.file.process.domain.constants.FileType;
import com.fxiaoke.file.process.domain.exception.BaseException;

public class CodingUtils {

  private  CodingUtils() {
  }

  public static void checkBatchFileSize(long totalSize, long maxSize, String module) {
    if (totalSize > maxSize) {
      throw new BaseException(400, "Batch file size exceeds limit", module, totalSize);
    }
  }

  public static void checkSingleFileSizeByType(long fileSize, String ext, long maxSize, String module) {
    if (fileSize > maxSize) {
      throw new BaseException(400,"File size exceeds limit", module, fileSize);
    }
    if (FileType.isImage(ext) && fileSize > SINGLE_IMAGE_TO_MD_MAX_SIZE) {
      throw new BaseException(400, "Image file size exceeds limit", module, fileSize);
    }
  }

}
