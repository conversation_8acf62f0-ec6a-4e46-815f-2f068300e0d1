package com.fxiaoke.file.process.utils;

import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.domain.constants.FileType;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.model.MarkdownImgInfo;
import com.fxiaoke.file.process.domain.model.VisCompRes;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class MarkDownUtils {

  private static final String MODEL = "MarkDownUtils";

  private MarkDownUtils() {
  }

  /**
   * IMAGE_PATTERN 匹配 ![图片描述](url) <br> 
   * 支持带title的格式：![图片描述](url "title") <br>
   * 标准格式 ![图片描述]<br> 
   * 没有URL的情况 ![图片描述] <br> 
   * 其他内容、图片标签后跟其他内容
   */
  private static final Pattern IMAGE_PATTERN = Pattern.compile("!\\[(.*?)](?:\\((.*?)(?:\\s+\"[^\"]*\")?\\)|\\b)");

  /**
   * 用于匹配特定格式的目录（TOC）<br> 引用链接 匹配 [章节标题](#_toc1) <br> [参考资料](#_ref23) <br> 注：其中 [ ] 之间的内容会被捕获 <br>
   * #_ 后面可以是 toc 或 ref <br> 加上一个或多个数字
   */
  private static final Pattern TOC_LINK_PATTERN = Pattern.compile(
      "\\[(.*?)]\\(#_(?:toc|ref)\\d+\\)");

  /**
   * 用于匹配HTML文档中的锚点标签 匹配格式:
   * <a name="_xxx">content</a> <br>
   * 其中: name属性必须以下划线(_)开头,标签可以包含其他属性 <br> 如: class、id 等,标签内可以包含任意文本内容 <br> 示例: <a
   * name="_123">text</a> <br>
   * <a name="_abc" class="someclass">text</a> <br>
   */
  private static final Pattern ANCHOR_TAG_PATTERN = Pattern.compile(
      "<a\\s+name=\"_[^>]*+>.*?</a>");


  /**
   * 用于匹配文档中的目录引用和参考链接格式 <br> 匹配格式: (#_toc数字) 或 (#_ref数字) <br> 其中: #_ 是固定前缀、toc 表示目录链接、ref 表示参考链接
   * <br> 数字可以是一个或多个 示例: (#_toc1)、(#_ref23)、(#_toc123) <br>
   */
  private static final Pattern TOC_REF_PATTERN = Pattern.compile("\\(#_(toc|ref)\\d+\\)");

  /**
   * 用于匹配heading锚点标签格式 <br> 
   * 匹配格式: <a name="heading_数字"></a> <br>
   * 其中: name属性以heading_开头，后面跟数字 <br>
   * 示例需要清理的: <a name="heading_0"></a>, <a name="heading_1"></a>, <a name="heading_127"></a> <br>
   */
  private static final Pattern HEADING_ANCHOR_TAG_PATTERN = Pattern.compile(
      "<a\\s+[^>]*name=\"heading_\\d+\"[^>]*></a>");

  private static final Pattern IMG_PATTERN = Pattern.compile(
      "<img\\s+[^>]*?src\\s*=\\s*['\"]([^'\"]*)['\"][^>]*?alt\\s*=\\s*['\"]([^'\"]*)['\"][^>]*?/?>");

  // Markdown链接的正则模式：支持多种URL格式
  // 1. [文本](https://url)
  // 2. [文本](<https://url>)
  // 3. ![文本](https://url)
  // 4. ![文本](<https://url>)
  private static final Pattern MARKDOWN_LINK_PATTERN = Pattern.compile(
      "!?\\[([^]]*)]\\(<?https[^)>]*>?\\)");

  /**
   * 表格行判断
   */
  private static boolean isTableLine(String line) {
    if (line == null || line.trim().isEmpty()) {
      return false;
    }
    String trimmed = line.trim();
    return trimmed.startsWith("<table") ||
        trimmed.startsWith("|") ||
        trimmed.startsWith("+-") ||
        trimmed.startsWith("|-");
  }

  public static String cleanImageTag(String line) {
    // 空值检查
    if (line == null || line.isEmpty()) {
      return "";
    }

    // 标准化图片标签：将所有图片标签转换为 ![](url) 格式
    return IMAGE_PATTERN.matcher(line).replaceAll(matchResult -> {
      String url = matchResult.group(2);
      if (url != null && !url.trim().isEmpty()) {
        // 移除可能存在的title部分（在引号中的内容）
        String cleanUrl = url.replaceAll("\\s+\"[^\"]*\"$", "").trim();
        return "![](" + cleanUrl + ")";
      } else {
        // 如果没有URL，则移除整个图片标签
        return "";
      }
    });
  }

  /**
   * 获取Markdown文件中的所有图片标签
   *
   * @param filePath Markdown文件路径
   * @return 包含所有图片信息的列表，按照在文档中出现的顺序排列
   * @throws BaseException 当文件读取出错时抛出异常
   */
  public static List<MarkdownImgInfo> extractImages(Path filePath) {
    List<MarkdownImgInfo> images = new ArrayList<>();
    int imageIndex = 1;

    try (BufferedReader reader = Files.newBufferedReader(filePath, StandardCharsets.UTF_8)) {
      String line;
      int lineNumber = 0;

      while ((line = reader.readLine()) != null) {
        lineNumber++;
        if (line.contains("![")) {
          Matcher matcher = IMAGE_PATTERN.matcher(line);
          while (matcher.find()) {
            String fullTag = matcher.group(0);
            Path imagePath = filePath.resolveSibling(matcher.group(2));
            images.add(MarkdownImgInfo.of(
                imageIndex++,
                fullTag,
                imagePath,
                lineNumber,
                matcher.start(),
                matcher.end()
            ));
          }
        }
      }
      return images;
    } catch (IOException e) {
      throw new BaseException(e, 500, MODEL + ".extractImages", filePath);
    }
  }

  /**
   * 清理Aspose.Words 转换的Markdown文件中的标签
   *
   * @param filePath     输入的Markdown文件路径
   * @param keepImageTag 是否保留图片标签 1. 需要清理如下标签：
   *                     <a name="_top"></a>  <br>
   *                     <a name="_toc498468432"></a>  <br>
   *                     <a name="_hlk102914415"></a> 的书签锚点 <br>
   *                     2. 需要处理目录标签： <br> [第一章 前言	1](#_toc105324020) <br> 第一章 前言 <br>
   *                     [<sup>\[2\]</sup>](#_ref104903549) <br>
   *                     <sup>\[2\]</sup>  <br>
   *                     3. 如果表格是使用html的<table></table>语法则转换为markdown的表格语法 <br> 4. 根据参数决定是否需要保留图片
   *                     <br>
   */
  public static Path mdSTD(Path filePath, boolean keepImageTag) {
    // 生成一个新的文件路径
    Path mdFilePath = FilenameUtil.fileNameAppendAFSuffix(filePath);

    try (BufferedReader reader = Files.newBufferedReader(filePath, StandardCharsets.UTF_8);
        BufferedWriter writer = Files.newBufferedWriter(mdFilePath, StandardCharsets.UTF_8)) {
      String line;
      while ((line = reader.readLine()) != null) {
        if (isTableStart(line)) {
          String htmlTableStr = JsoupUtil.captureMdFileInHtmlTable(reader, line);
          writer.write(JsoupUtil.convertHtmlTableToMarkdown(htmlTableStr));
        } else {
          String text = cleanTags(keepImageTag, line);
          if (!text.isEmpty()) {
            writer.write(text);
          }
        }
        writer.newLine();
      }
      return mdFilePath;
    } catch (IOException e) {
      throw new BaseException(e, 500, MODEL + ".mdSTD", filePath, mdFilePath);
    }
  }

  /**
   * 清理文本中的特殊标签
   *
   * @param keepImageTag 是否保留图片标签（为true时会标准化图片标签格式）
   * @param line 需要处理的文本行
   * @return 清理后的文本
   */
  private static String cleanTags(boolean keepImageTag, String line) {
    // 空值检查
    if (line == null || line.isEmpty()) {
      return "";
    }

    // 清理目录链接，只保留链接文本
    String cleaned = TOC_LINK_PATTERN.matcher(line).replaceAll("$1");

    // 清理HTML锚点标签
    cleaned = ANCHOR_TAG_PATTERN.matcher(cleaned).replaceAll("");

    // 清理目录引用标记
    cleaned = TOC_REF_PATTERN.matcher(cleaned).replaceAll("");

    // 清理heading锚点标签
    cleaned = HEADING_ANCHOR_TAG_PATTERN.matcher(cleaned).replaceAll("");

    if (keepImageTag) {
      // 保留图片标签时，标准化图片标签格式为 ![](url)
      return cleanImageTag(cleaned);
    } else {
      // 不保留图片标签时，完全移除图片标签
      return IMAGE_PATTERN.matcher(cleaned).replaceAll("");
    }
  }


  /**
   * 根据markdownImages 信息替换 filePath 中与originalTag匹配图片标记为content 替换时可根据 index、 lineNumber
   * 优化性能，根据startIndex、endIndex 精确替换 最终返回新的Markdown文件路径
   *
   * @param filePath            Markdown文件路径
   * @param visCompRes          包含图片识别结果及原始图片布局信息的列表
   * @return 替换后的新Markdown文件路径
   * @throws BaseException 当文件读取或写入出错时抛出异常
   */
  public static Path replaceImageTags(Path filePath, List<VisCompRes<MarkdownImgInfo>> visCompRes,
      FileType fileType) {
    // 生成一个新的文件路径，用于存储替换后的内容
    Path mdFilePath = FilenameUtil.fileNameAppendAFSuffix(filePath);

    try (BufferedReader reader = Files.newBufferedReader(filePath, StandardCharsets.UTF_8);
        BufferedWriter writer = Files.newBufferedWriter(mdFilePath, StandardCharsets.UTF_8)) {

      String line;
      int currentLineNumber = 0;

      // 按行号对图片进行分组，优化查找效率
      // 这样可以快速定位到每一行需要替换的图片信息
      Map<Integer, List<VisCompRes<MarkdownImgInfo>>> imagesByLine = visCompRes.stream()
          .collect(Collectors.groupingBy(res -> res.getData().getLineNumber()));

      while ((line = reader.readLine()) != null) {
        currentLineNumber++;

        // 检查当前行是否包含需要替换的图片
        List<VisCompRes<MarkdownImgInfo>> compResList = imagesByLine.get(currentLineNumber);

        if (compResList != null && !compResList.isEmpty()) {
          // 按照在行中的位置倒序排序，从后向前替换，避免位置偏移
          // 这是因为如果从前向后替换，每次替换都会改变后续内容的起始位置
          compResList.sort((res1, res2)
              -> Integer.compare(res2.getData().getStartIndex(), res1.getData().getStartIndex()));

          StringBuilder modifiedLine = new StringBuilder(line);

          for (VisCompRes<MarkdownImgInfo> res : compResList) {
            String contentToInsert = res.getContent();

            if (contentToInsert == null || contentToInsert.isEmpty()) {
              contentToInsert = ""; // 如果内容为空，则替换为空字符串，相当于删除标签
            } else {
              // 如果内容存在，检查是否为表格行，如果是则处理换行符
              if (isTableLine(line)) {
                contentToInsert = contentToInsert.replaceAll("\\n", "<br>");
              }
            }

            // 执行替换操作，确保原始标签被正确处理
            modifiedLine.replace(
                res.getData().getStartIndex(),
                res.getData().getEndIndex(),
                contentToInsert
            );
          }

          // 根据文件类型决定输出格式
          String finalContent = modifiedLine.toString();
          if (fileType.isSlides() || fileType.isPDF()) {
            // 对于 Slides 类型，直接写入修改后的内容
            writer.write(finalContent);
          } else {
            // 对于其他类型，添加图片标签标记
            writer.write(Constants.IMAGE_TAG_START);
            writer.newLine();
            writer.write(finalContent);
            writer.newLine();
            writer.write(Constants.IMAGE_TAG_END);
          }

        } else {
          // 如果当前行没有需要替换的图片，直接写入原始内容
          writer.write(line);
        }

        writer.newLine();
      }

      return mdFilePath;
    } catch (IOException e) {
      throw new BaseException(e, 500, MODEL + ".replaceImageTag", filePath, mdFilePath);
    }
  }

  /**
   * 表格起始判断
   */
  private static boolean isTableStart(String line) {
    if (line == null || line.trim().isEmpty()) {
      return false;
    }
    return line.trim().startsWith("<table");
  }

  /**
   * 清理AI模型识别结果中的幻觉字符串
   * 1. 处理HTML img标签：将其替换为alt文本
   * 2. 清理Markdown链接：移除包含 ](https 特征的链接
   *
   * @param input 输入字符串
   * @return 清理后的字符串
   */
  public static String cleanVlIllusionStr(String input) {
    if (input == null) {
      return null;
    }

    // 第一步：处理HTML img标签
    String result = processHtmlImgTag(input);

    // 第二步：清理Markdown链接
    result = cleanMarkdownLinks(result);

    return result;
  }

  /**
   * 处理HTML img标签，将其替换为alt文本
   *
   * @param input 输入字符串
   * @return 处理后的字符串
   */
  private static String processHtmlImgTag(String input) {
    if (!input.contains("<img")) {
      return input;
    }

    StringBuilder result = new StringBuilder(input.length());
    Matcher matcher = IMG_PATTERN.matcher(input);

    int lastEnd = 0;
    while (matcher.find()) {
      // 添加img标签之前的内容
      result.append(input, lastEnd, matcher.start())
          .append(matcher.group(2)); // 直接添加alt文本

      lastEnd = matcher.end();
    }

    // 添加剩余内容
    if (lastEnd < input.length()) {
      result.append(input, lastEnd, input.length());
    }

    return result.toString();
  }

  /**
   * 处理Markdown链接：保留链接文本，移除URL部分
   * 支持的格式：
   * [文本](https://url) -> 文本
   * [文本](<https://url>) -> 文本
   * ![文本](https://url) -> 空（图片链接完全移除）
   * ![文本](<https://url>) -> 空（图片链接完全移除）
   * @param input 输入字符串
   * @return 清理后的字符串
   */
  private static String cleanMarkdownLinks(String input) {

    StringBuilder result = new StringBuilder(input.length());
    Matcher matcher = MARKDOWN_LINK_PATTERN.matcher(input);

    int lastEnd = 0;
    while (matcher.find()) {
      // 添加链接之前的内容
      result.append(input, lastEnd, matcher.start());

      // 检查是否是图片链接（以!开头）
      String fullMatch = matcher.group(0);
      if (!fullMatch.startsWith("!")) {
        // 普通链接：保留链接文本
        result.append(matcher.group(1));
      }

      lastEnd = matcher.end();
    }

    // 添加剩余内容
    if (lastEnd < input.length()) {
      result.append(input, lastEnd, input.length());
    }

    return result.toString();
  }

  public static Path clearNbsp(Path filePath){
    // 生成一个新的文件路径
    Path mdFilePath = FilenameUtil.fileNameAppendAFSuffix(filePath);

    try (BufferedReader reader = Files.newBufferedReader(filePath, StandardCharsets.UTF_8);
         BufferedWriter writer = Files.newBufferedWriter(mdFilePath, StandardCharsets.UTF_8)) {
      String line;
      while ((line = reader.readLine()) != null) {
        // 清除每行中的 &nbsp;
        String cleanedLine = line.replaceAll("&nbsp;", "");
        writer.write(cleanedLine);
        writer.newLine();
      }
      return mdFilePath;
    } catch (IOException e) {
      throw new BaseException(e, 500, MODEL + ".clearNbsp", filePath, mdFilePath);
    }
  }

  // 如果一行中以<br> 或 <br/> 或 <br /> 开头，则清除该行的<br>标签
  public static Path clearBr(Path filePath){
    // 生成一个新的文件路径
    Path mdFilePath = FilenameUtil.fileNameAppendAFSuffix(filePath);

    try (BufferedReader reader = Files.newBufferedReader(filePath, StandardCharsets.UTF_8);
         BufferedWriter writer = Files.newBufferedWriter(mdFilePath, StandardCharsets.UTF_8)) {
      String line;
      while ((line = reader.readLine()) != null) {
        // 清除每行开头的 <br> 或 <br/> 或 <br />
        String cleanedLine = line.replaceAll("^(<br\\s*/?>)+", "");
        writer.write(cleanedLine);
        writer.newLine();
      }
      return mdFilePath;
    } catch (IOException e) {
      throw new BaseException(e, 500, MODEL + ".clearBr", filePath, mdFilePath);
    }
  }
}
