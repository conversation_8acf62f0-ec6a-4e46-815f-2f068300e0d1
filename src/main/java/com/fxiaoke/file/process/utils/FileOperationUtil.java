package com.fxiaoke.file.process.utils;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;

/**
 * 文件操作工具类 依赖 commons-io
 */
@Slf4j
public class FileOperationUtil {

  private FileOperationUtil() {
  }

  private static final int BUFFER_SIZE = 8192;

  public static boolean notExists(Path filePath) {
    return !Files.exists(filePath);
  }

  /**
   * 递归创建目录
   *
   * @param filePath 目录路径
   * @throws IOException 如果创建失败
   */
  public static void createRecursively(Path filePath) throws IOException {
    if (Files.exists(filePath)) {
      return;
    }
    FileUtils.forceMkdir(filePath.toFile());
  }

  /**
   * 完全删除文件或整个目录（包括目录本身）
   *
   * @param filePath 文件或目录路径
   * @throws IOException 如果删除失败
   */
  public static void deleteFileOrDir(Path filePath) throws IOException {
    if (!Files.exists(filePath)) {
      return;
    }
    if (Files.isDirectory(filePath)) {
      FileUtils.forceDelete(filePath.toFile());
    } else {
      Files.delete(filePath);
    }
  }

  /**
   * 保存 InputStream 到文件
   *
   * @param input    输入流
   * @param filePath 文件路径
   * @throws IOException IO异常
   */
  public static void saveInputStream(InputStream input, Path filePath) throws IOException {
    // 参数校验
    Objects.requireNonNull(input, "InputStream must not be null");
    Objects.requireNonNull(filePath, "FilePath must not be null");
    // 确保目录存在
    createRecursively(filePath.getParent());
    try (InputStream in = new BufferedInputStream(input, BUFFER_SIZE);
        OutputStream out = Files.newOutputStream(filePath,
            StandardOpenOption.CREATE,
            StandardOpenOption.TRUNCATE_EXISTING)) {
      IOUtils.copy(in, out);
    }
  }

  /**
   * 保存字节数组到文件
   *
   * @param data     字节数组
   * @param filePath 文件路径
   * @throws IOException IO异常
   */
  public static void saveBytes(byte[] data, Path filePath) throws IOException {
    // 参数校验
    Objects.requireNonNull(data, "Data must not be null");
    Objects.requireNonNull(filePath, "FilePath must not be null");

    // 确保目录存在
    createRecursively(filePath.getParent());

    // 使用 Files.write 方法写入数据
    Files.write(filePath, data);
  }


  /**
   * 获取文件大小
   *
   * @param filePath 文件路径
   * @return 文件大小（字节）
   * @throws IOException IO异常
   */
  public static long getFileSize(Path filePath) throws IOException {
    Objects.requireNonNull(filePath, "FilePath must not be null");
    return Files.size(filePath);
  }

  /**
   * 检查目录是否为空
   *
   * @param directory 目录路径
   * @return 如果目录为空，则返回 true；否则返回 false
   * @throws IOException IO异常
   */
  public static boolean isEmptyDirectory(Path directory) throws IOException {
    Objects.requireNonNull(directory, "Directory path must not be null");
    if (!Files.isDirectory(directory)) {
      return false;
    }
    try (DirectoryStream<Path> dirStream = Files.newDirectoryStream(directory)) {
      return !dirStream.iterator().hasNext();
    }
  }
}
