package com.fxiaoke.file.process.utils;

import com.fxiaoke.stone.commons.domain.constant.Constants;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

public class SignatureUtil {

  private SignatureUtil() {
  }

  private static final String key = "4lFDCuwuQb8PslEGGmkHe2HWj24CbRch";

  public static String getSignatureWithHmacSha1(String input) throws StoneCommonClientException {
    SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), Constants.SIGNATURE_ALGORITHM);
    Mac mac;
    try {
      mac = Mac.getInstance(Constants.SIGNATURE_ALGORITHM);
      mac.init(keySpec);
    } catch (NoSuchAlgorithmException | InvalidKeyException e) {
      throw new StoneCommonClientException(e,"signature calculation fail",400,key,input);
    }
    byte[] inputBytes = input.getBytes(StandardCharsets.UTF_8);
    byte[] signatureData = mac.doFinal(inputBytes);
    return Base64.getUrlEncoder().encodeToString(signatureData);
  }

  public static boolean validateSignature(String input, String signature) throws StoneCommonClientException {
    String newSignature = getSignatureWithHmacSha1(input);
    return newSignature.equals(signature);
  }

}
