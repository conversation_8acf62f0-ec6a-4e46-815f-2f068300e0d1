package com.fxiaoke.file.process.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.regex.Pattern;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.parser.Parser;
import org.jsoup.safety.Safelist;

/**
 * Jsoup工具类，提供HTML解析和处理相关功能
 */
public class JsoupUtil {

  private JsoupUtil(){}

  private static final int INITIAL_BUFFER_SIZE = 2048;
  
  // 常用正则表达式模式
  private static final Pattern HTML_FORMATTING_PATTERN = Pattern.compile("</?(?:b|strong|i|em)>");
  private static final Pattern BR_PATTERN = Pattern.compile("<br\\s*/?>");
  private static final Pattern HTML_TAG_PATTERN = Pattern.compile("<[^>]+>");
  private static final Pattern MULTI_SPACE_PATTERN = Pattern.compile("\\s+");
  
  // 缓存不同配置的解析器，避免重复创建
  private static final ConcurrentMap<String, Parser> PARSER_CACHE = new ConcurrentHashMap<>();

  /**
   * 捕获Markdown文件中的HTML表格
   * @param reader BufferedReader
   * @param firstLine 第一行内容
   * @return 捕获的Markdown文件内容
   * @throws IOException 如果发生IO错误
   */
  public static String captureMdFileInHtmlTable(BufferedReader reader, String firstLine) throws IOException {
    if (reader == null || firstLine == null) {
      throw new IllegalArgumentException("Reader and firstLine cannot be null");
    }

    StringBuilder sb = new StringBuilder(INITIAL_BUFFER_SIZE);
    sb.append(firstLine).append('\n');
    int tableDepth = 1;

    String line;
    while (tableDepth > 0 && (line = reader.readLine()) != null) {
      sb.append(line).append('\n');
      if (line.contains("<table")) {
        tableDepth++;
      }
      if (line.contains("</table>")) {
        tableDepth--;
      }
    }
    return sb.toString();
  }

  /**
   * 将HTML表格转换为Markdown格式
   * @param html 包含表格的HTML字符串
   * @return Markdown格式的表格字符串
   */
  public static String convertHtmlTableToMarkdown(String html) {
    if (html == null || html.isEmpty()) {
      return "";
    }

    Document doc = Jsoup.parse(html);
    Element table = doc.selectFirst("table");
    if (table == null) {
      return "";
    }

    List<List<String>> tableData = parseTableData(table);
    return buildMarkdownTable(tableData);
  }

  /**
   * 获取指定配置的解析器
   * @param parserType 解析器类型
   * @return 对应的Parser实例
   */
  public static Parser getParser(String parserType) {
    return PARSER_CACHE.computeIfAbsent(parserType, key -> Parser.htmlParser());
  }

  /**
   * 从字符串解析HTML文档
   * @param html HTML内容
   * @return 解析后的Document
   */
  public static Document parseHtml(String html) {
    return Jsoup.parse(html);
  }

  /**
   * 从输入流解析HTML文档
   * @param inputStream 输入流
   * @param charset 字符集
   * @param baseUri 基础URI
   * @return 解析后的Document
   * @throws IOException 如果发生IO错误
   */
  public static Document parseHtml(InputStream inputStream, Charset charset, String baseUri) throws IOException {
    return Jsoup.parse(inputStream, charset.name(), baseUri);
  }

  /**
   * 清理HTML，移除危险标签和属性
   * @param html 原始HTML
   * @param safeList 安全列表，如果为null则使用basic()
   * @return 清理后的HTML
   */
  public static String clean(String html, Safelist safeList) {
    return Jsoup.clean(html, safeList != null ? safeList : Safelist.basic());
  }

  /**
   * 提取纯文本内容
   * @param html HTML内容
   * @return 提取的纯文本
   */
  public static String extractText(String html) {
    if (html == null || html.isEmpty()) {
      return "";
    }
    return Jsoup.parse(html).text();
  }

  /**
   * 计算表格最大列数
   */
  private static int calculateMaxColumns(List<Element> rows) {
    return rows.stream().mapToInt(
            row -> row.select("th, td").stream().mapToInt(JsoupUtil::getColspanValue).sum()).max()
        .orElse(0);
  }


  /**
   * 解析表格数据 - 优化版本
   */
  private static List<List<String>> parseTableData(Element table) {
    if (table == null) {
      return new ArrayList<>();
    }
    
    // 获取行和列信息
    List<Element> rows = table.select("tr");
    int rowCount = rows.size();
    if (rowCount == 0) {
      return new ArrayList<>();
    }

    // 计算最大列数
    int maxColumns = calculateMaxColumns(rows);

    // 创建表格数据和合并跟踪器
    List<List<String>> tableData = new ArrayList<>(rowCount);
    String[][] mergeTracker = new String[rowCount][maxColumns];

    // 处理每一行
    processTableRows(rows, tableData, mergeTracker, maxColumns);

    return tableData;
  }

  /**
   * 填充被合并的单元格
   */
  private static int fillMergedCells(List<String> currentRow, String[] rowTracker, int startCol,
      int maxColumns) {
    int colIndex = startCol;
    while (colIndex < maxColumns && rowTracker[colIndex] != null) {
      currentRow.add(rowTracker[colIndex]);
      colIndex++;
    }
    return colIndex;
  }

  /**
   * HTML转Markdown - 优化版本使用单次遍历
   */
  private static String htmlToMarkdown(String html) {
    if (html == null || html.isEmpty()) {
      return "";
    }

    String result = html;

    // 使用通用的格式化标签替换
    result = HTML_FORMATTING_PATTERN.matcher(result).replaceAll("**");
    result = BR_PATTERN.matcher(result).replaceAll("  \n");
    result = HTML_TAG_PATTERN.matcher(result).replaceAll("");
    result = MULTI_SPACE_PATTERN.matcher(result).replaceAll(" ");

    return result.trim();
  }


  /**
   * 获取colspan属性值
   */
  private static int getColspanValue(Element cell) {
    if (cell == null) {
      return 1;
    }
    
    try {
      return Math.max(1, Integer.parseInt(cell.attr("colspan")));
    } catch (NumberFormatException e) {
      return 1;
    }
  }

  /**
   * 获取rowspan属性值
   */
  private static int getRowspanValue(Element cell) {
    if (cell == null) {
      return 1;
    }
    
    try {
      return Math.max(1, Integer.parseInt(cell.attr("rowspan")));
    } catch (NumberFormatException e) {
      return 1;
    }
  }

  /**
   * 处理单元格合并
   */
  private static void processCellMerging(String[][] mergeTracker, int rowIndex, int colIndex,
      String content, int rowspan, int colspan, int maxColumns) {
    for (int i = 0; i < rowspan && rowIndex + i < mergeTracker.length; i++) {
      for (int j = 0; j < colspan && colIndex + j < maxColumns; j++) {
        mergeTracker[rowIndex + i][colIndex + j] = content;
      }
    }
  }


  /**
   * 处理表格单行
   */
  private static void processTableRow(Element row, List<String> currentRow, String[][] mergeTracker,
      int rowIndex, int maxColumns) {
    if (row == null || currentRow == null || mergeTracker == null) {
      return;
    }
    
    int colIndex = 0;

    // 填充被行合并的单元格
    colIndex = fillMergedCells(currentRow, mergeTracker[rowIndex], colIndex, maxColumns);

    // 处理当前行的单元格
    for (Element cell : row.select("th, td")) {
      // 跳过已处理的列
      while (colIndex < maxColumns && mergeTracker[rowIndex][colIndex] != null) {
        colIndex++;
      }

      if (colIndex >= maxColumns) {
        break;
      }

      // 处理单元格内容
      String content = htmlToMarkdown(cell.html());
      int colspan = getColspanValue(cell);
      int rowspan = getRowspanValue(cell);

      // 处理行列合并
      processCellMerging(mergeTracker, rowIndex, colIndex, content, rowspan, colspan, maxColumns);

      // 添加到当前行
      for (int i = 0; i < colspan && colIndex + i < maxColumns; i++) {
        currentRow.add(content);
      }

      colIndex += colspan;
    }

    // 确保所有列都被填充
    while (currentRow.size() < maxColumns) {
      currentRow.add("");
    }
  }

  /**
   * 处理表格所有行
   */
  private static void processTableRows(List<Element> rows, List<List<String>> tableData,
      String[][] mergeTracker, int maxColumns) {
    if (rows == null || tableData == null || mergeTracker == null) {
      return;
    }
    
    int rowIndex = 0;
    for (Element row : rows) {
      List<String> currentRow = new ArrayList<>(maxColumns);
      processTableRow(row, currentRow, mergeTracker, rowIndex, maxColumns);
      tableData.add(currentRow);
      rowIndex++;
    }
  }

  /**
   * 构建Markdown表格
   */
  private static String buildMarkdownTable(List<List<String>> data) {
    if (data == null || data.isEmpty() || data.getFirst().isEmpty()) {
      return "";
    }

    StringBuilder buffer = new StringBuilder(INITIAL_BUFFER_SIZE);

    // 处理表头
    appendTableRow(buffer, data.getFirst());
    buffer.append('\n');

    // 生成分隔线
    appendTableSeparator(buffer, data.getFirst().size());
    buffer.append('\n');

    // 处理数据行
    for (int i = 1; i < data.size(); i++) {
      appendTableRow(buffer, data.get(i));
      buffer.append('\n');
    }

    return buffer.toString();
  }


  /**
   * 添加表格行
   */
  private static void appendTableRow(StringBuilder buffer, List<String> rowData) {
    if (buffer == null || rowData == null) {
      return;
    }
    
    StringJoiner joiner = new StringJoiner(" | ", "| ", " |");
    rowData.forEach(joiner::add);
    buffer.append(joiner);
  }

  /**
   * 添加表格分隔符
   */
  private static void appendTableSeparator(StringBuilder buffer, int columns) {
    if (buffer == null || columns <= 0) {
      return;
    }
    
    StringJoiner joiner = new StringJoiner(" | ", "| ", " |");
    for (int i = 0; i < columns; i++) {
      joiner.add("---");
    }
    buffer.append(joiner);
  }
  
  /**
   * 从HTML表格中提取数据为二维列表
   * @param html 包含表格的HTML
   * @return 表格数据的二维列表
   */
  public static List<List<String>> extractTableData(String html) {
    if (html == null || html.isEmpty()) {
      return new ArrayList<>();
    }
    
    Document doc = Jsoup.parse(html);
    Element table = doc.selectFirst("table");
    if (table == null) {
      return new ArrayList<>();
    }
    
    return parseTableData(table);
  }

}

