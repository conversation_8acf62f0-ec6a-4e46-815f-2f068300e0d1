package com.fxiaoke.file.process.utils;

import com.fxiaoke.file.process.domain.constants.FileType;
import com.fxiaoke.file.process.domain.model.ImageBaseInfo;
import com.fxiaoke.file.process.domain.model.ImageDimension;
import java.awt.AlphaComposite;
import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.Transparency;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Iterator;
import java.util.Optional;
import java.util.Set;
import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;

public class ImageBaseUtil {

  private ImageBaseUtil() {
  }

  // 定义支持处理的图片格式集合
  private static final Set<String> SUPPORTED_FORMATS = Set.of(FileType.PNG.getFileType(),
      FileType.JPG.getFileType(), FileType.JPEG.getFileType(), FileType.WEBP.getFileType());

  /**
   * 根据给定的宽度、高度和最大分辨率计算图像压缩后的尺寸。 如果原始图像的像素总数超过最大分辨率，则计算压缩后的宽度和高度。
   * 如果原始图像的像素总数未超过最大分辨率，则将根据最大分辨率和原始图像的宽高比计算新的尺寸。
   *
   * @param width         原始图像的宽度
   * @param height        原始图像的高度
   * @param maxResolution 允许的最大分辨率（像素总数）
   * @return 一个 ImageDimension 对象，包含压缩后的宽度和高度或膨胀后的尺寸
   */
  public static ImageDimension getScopeWH(double width, double height, double maxResolution) {
    // 检查原始图像的像素总数是否超过最大分辨率
    if (width * height > maxResolution) {
      // 计算压缩比例。通过原始图像像素总数与最大分辨率的比值的平方根得到
      double ratio = Math.sqrt((width * height) / maxResolution);
      // 计算压缩后的尺寸，将结果四舍五入为整数
      int newWidth = (int) Math.round(width / ratio);
      int newHeight = (int) Math.round(height / ratio);
      return new ImageDimension(newWidth, newHeight);
    } else {
      // 如果原始图像的像素总数未超过最大分辨率，则根据最大分辨率和原始图像的宽高比计算新的尺寸
      double ratio = Math.sqrt(maxResolution / (width * height));
      int newWidth = (int) Math.round(width * ratio);
      int newHeight = (int) Math.round(height * ratio);
      return new ImageDimension(newWidth, newHeight);
    }
  }

  /**
   * 图片处理工具类 1. 判断图片是否小于参数  minResolution ,如果小于则返回 Optional.empty() 2. 判断图片是否大于参数  maxResolution
   * ,如果大于则进行压缩,并将压缩后的图片覆盖原图 imagePath,处理完成后返回 Optional.of(imagePath)
   */
  public static Optional<Path> imageCompressing(Path imagePath, int minResolution,
      int maxResolution) {
    // 检查图片基本条件
    Optional<ImageBaseInfo> imageInfoOpt = checkImageBasicConditions(imagePath, minResolution);
    if (imageInfoOpt.isEmpty()) {
      return Optional.empty();
    }

    ImageBaseInfo imageBaseInfo = imageInfoOpt.get();

    try {

      // 如果图片分辨率大于最大分辨率，进行压缩
      if (imageBaseInfo.resolution() > maxResolution) {
        // 计算压缩后的尺寸
        ImageDimension compressingWH = getCompressingWH(imageBaseInfo.width(),
            imageBaseInfo.height(), maxResolution);

        // 读取原始图片
        BufferedImage originalImage = ImageIO.read(imagePath.toFile());

        // 调整图片尺寸
        BufferedImage compressedImage = resizeImage(originalImage, compressingWH.getWidth(),
            compressingWH.getHeight());

        // 将压缩后的图片覆盖原图
        ImageIO.write(compressedImage, imageBaseInfo.imageType(), imagePath.toFile());
      }

      // 处理完成后返回图片路径
      return Optional.of(imagePath);
    } catch (IOException e) {
      return Optional.empty();
    }
  }

  /**
   * 根据给定的宽度、高度和最大分辨率计算图像压缩后的尺寸。 如果原始图像的像素总数超过最大分辨率，则计算压缩后的宽度和高度。 如果原始图像的像素总数未超过最大分辨率，则返回原始的宽度和高度。
   *
   * @param width         原始图像的宽度
   * @param height        原始图像的高度
   * @param maxResolution 允许的最大分辨率（像素总数）
   * @return 一个 ImageDimension 对象，包含压缩后的宽度和高度
   */
  public static ImageDimension getCompressingWH(int width, int height, double maxResolution) {
    // 检查原始图像的像素总数是否超过最大分辨率
    if (width * height > maxResolution) {
      // 计算压缩比例。通过原始图像像素总数与最大分辨率的比值的平方根得到
      double ratio = Math.sqrt(width * height / maxResolution);
      // 计算压缩后的尺寸，将结果四舍五入为整数
      int newWidth = (int) Math.round(width / ratio);
      int newHeight = (int) Math.round(height / ratio);
      return new ImageDimension(newWidth, newHeight);
    }
    // 如果原始图像的像素总数未超过最大分辨率，直接返回原始的宽度和高度
    return new ImageDimension(width, height);
  }

  /**
   * 检查图片是否满足基本处理条件
   *
   * @param imagePath     图片路径
   * @param minResolution 最小分辨率
   * @return 如果图片存在、格式支持且分辨率大于等于最小分辨率，返回图片信息；否则返回空
   */
  private static Optional<ImageBaseInfo> checkImageBasicConditions(Path imagePath,
      int minResolution) {
    try {
      // 检查文件是否存在
      if (!Files.exists(imagePath)) {
        return Optional.empty();
      }

      // 获取图片类型
      String imageType = getImageType(imagePath);
      if (!SUPPORTED_FORMATS.contains(imageType)) {
        return Optional.empty();
      }

      // 获取图片分辨率
      ImageDimension dimension = getImageResolution(imagePath);
      int originalWidth = dimension.getWidth();
      int originalHeight = dimension.getHeight();
      int originalResolution = originalWidth * originalHeight;

      // 如果图片分辨率小于最小分辨率，返回空
      if (originalResolution < minResolution) {
        return Optional.empty();
      }

      return Optional.of(
          new ImageBaseInfo(imageType, originalWidth, originalHeight, originalResolution));
    } catch (IOException e) {
      return Optional.empty();
    }
  }

  /**
   * 调整图片尺寸，保持图像质量
   *
   * @param originalImage 原始图像
   * @param targetWidth   目标宽度
   * @param targetHeight  目标高度
   * @return 调整大小后的图像
   */
  private static BufferedImage resizeImage(BufferedImage originalImage, int targetWidth,
      int targetHeight) {
    // 确定图像类型（RGB或ARGB）
    int type = originalImage.getTransparency() == Transparency.OPAQUE ?
        BufferedImage.TYPE_INT_RGB : BufferedImage.TYPE_INT_ARGB;

    // 创建新的BufferedImage对象
    BufferedImage resizedImage = new BufferedImage(targetWidth, targetHeight, type);
    Graphics2D g2d = resizedImage.createGraphics();

    // 处理透明图像
    if (type == BufferedImage.TYPE_INT_ARGB) {
      g2d.setComposite(AlphaComposite.Src);
      g2d.setColor(new Color(0, 0, 0, 0));
      g2d.fillRect(0, 0, targetWidth, targetHeight);
    }

    // 设置高质量渲染选项
    configureHighQualityRendering(g2d);

    // 绘制调整大小后的图像
    g2d.drawImage(originalImage, 0, 0, targetWidth, targetHeight, null);
    g2d.dispose(); // 释放系统资源

    return resizedImage;
  }

  /**
   * 配置Graphics2D对象以进行高质量渲染
   *
   * @param g2d Graphics2D对象
   */
  private static void configureHighQualityRendering(Graphics2D g2d) {
    // 设置双三次插值算法，提供最高质量的图像缩放
    g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION,
        RenderingHints.VALUE_INTERPOLATION_BICUBIC);

    // 设置渲染质量优先
    g2d.setRenderingHint(RenderingHints.KEY_RENDERING,
        RenderingHints.VALUE_RENDER_QUALITY);

    // 启用抗锯齿
    g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING,
        RenderingHints.VALUE_ANTIALIAS_ON);
  }

  /**
   * 获取图片类型的辅助方法 首先尝试从文件扩展名判断，如果失败则通过文件魔数判断
   *
   * @param imgPath 图片路径
   * @return 图片类型字符串
   * @throws IOException 当读取文件失败时抛出
   */
  private static String getImageType(Path imgPath) throws IOException {
    // 从文件名获取扩展名
    String fileName = imgPath.getFileName().toString();
    int lastDotIndex = fileName.lastIndexOf(".");
    if (lastDotIndex > 0) {
      String extension = fileName.substring(lastDotIndex + 1);
      if (SUPPORTED_FORMATS.contains(extension)) {
        return extension;
      }
    }
    return FileTypeUtil.getImageTypeByMagicNumber(imgPath);
  }

  /**
   * 获取图片分辨率
   *
   * @param imagePath 图片路径
   * @return 返回图片分辨率对象，包含宽度和高度
   * @throws IOException 当读取图片发生错误时抛出
   */
  public static ImageDimension getImageResolution(Path imagePath) throws IOException {
    // 创建图片输入流
    try (ImageInputStream iis = ImageIO.createImageInputStream(imagePath.toFile())) {
      if (iis == null) {
        throw new IOException("Unable to create image input stream");
      }

      // 获取图片读取器
      Iterator<ImageReader> readers = ImageIO.getImageReaders(iis);
      if (!readers.hasNext()) {
        throw new IOException("A suitable image reader could not be found");
      }

      // 读取图片尺寸
      ImageReader reader = readers.next();
      try {
        reader.setInput(iis, true, true);
        int width = reader.getWidth(0);
        int height = reader.getHeight(0);
        return new ImageDimension(width, height);
      } finally {
        reader.dispose();
      }
    } catch (SecurityException e) {
      throw new SecurityException("No permission to access the file", e);
    } catch (IllegalArgumentException e) {
      throw new IllegalArgumentException("The file format is incorrect", e);
    }
  }

}
