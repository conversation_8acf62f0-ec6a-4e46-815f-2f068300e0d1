package com.fxiaoke.file.process.domain.api.response;

import com.github.trace.TraceContext;
import lombok.Data;

/**
 * <h2>提交异步文档转Markdown任务返回结果</h2>
 * <ul>
 * <li><strong>jobId:</strong> 本次提交任务ID,用于后续查询接口进行查询的唯一标识</li>
 * <li><strong>requestId:</strong> 请求ID,用于排查问题基本等价于traceId</li>
 */
@Data
public class DocToMdJobSubmitResult {

  private String jobId;
  private String requestId;

  public static DocToMdJobSubmitResult of(String jobId) {
    DocToMdJobSubmitResult result = new DocToMdJobSubmitResult();
    result.setJobId(jobId);
    result.setRequestId(TraceContext.get().getTraceId());
    return result;
  }
}
