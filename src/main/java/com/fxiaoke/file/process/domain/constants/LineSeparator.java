package com.fxiaoke.file.process.domain.constants;

import lombok.Getter;

@Getter
public enum LineSeparator {

  WINDOWS("\r\n"),
  // MacOS与Linux通用
  UNIX("\n"),
  MAC_OLD("\r");

  private final String line;

  LineSeparator(String line) {
    this.line = line;
  }

  public static String getForOS(String os) {
    try {
      return valueOf(os.toUpperCase()).getLine();
    } catch (IllegalArgumentException e) {
      return System.lineSeparator();
    }
  }
}
