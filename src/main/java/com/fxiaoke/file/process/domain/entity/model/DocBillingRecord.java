package com.fxiaoke.file.process.domain.entity.model;

import com.fxiaoke.file.process.domain.entity.fieids.DocParseJobFieIds;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class DocBillingRecord {

  // 文档页数
  @Property(DocParseJobFieIds.pageCount)
  private int pageCount;

  // 文件时长
  @Property(DocParseJobFieIds.duration)
  private double duration;

  // 文档大小
  @Property(DocParseJobFieIds.fileSize)
  private long fileSize;

  // 文档中的图片数量
  @Property(DocParseJobFieIds.imageCount)
  private int imageCount;

  // 模型名称
  @Property(DocParseJobFieIds.modelName)
  private String modelName;

  @Property(DocParseJobFieIds.totalInputTokens)
  private int totalInputTokens;

  @Property(DocParseJobFieIds.totalOutputTokens)
  private int totalOutputTokens;

  @Property(DocParseJobFieIds.totalTokens)
  private int totalTokens;

  // 纷享定价算粒代币(1算粒≈1毛钱)
  @Property(DocParseJobFieIds.pricingTokens)
  private double pricingTokens;
}
