package com.fxiaoke.file.process.domain.api.request.model;

import com.google.gson.annotations.SerializedName;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 单句的详细识别结果，包含单个词的时间偏移，一般用于生成字幕的场景。
 * 被如下接口引用：DescribeTaskStatus。
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class SentenceDetail {

  /**
   * 单句最终识别结果
   * 注意：此字段可能返回 null，表示取不到有效值。
   * 示例值：你好，今天很开心
   */
  @SerializedName("FinalSentence")
  private String finalSentence;

  /**
   * 单句中间识别结果，使用空格拆分为多个词
   * 注意：此字段可能返回 null，表示取不到有效值。
   * 示例值：你好 今天 很 开心
   */
  @SerializedName("SliceSentence")
  private String sliceSentence;

  /**
   * 口语转书面语结果，开启改功能才有值
   * 注意：此字段可能返回 null，表示取不到有效值。
   * 示例值：你好，今天很开心。
   */
  @SerializedName("WrittenText")
  private String writtenText;

  /**
   * 单句开始时间（毫秒）
   * 注意：此字段可能返回 null，表示取不到有效值。
   * 示例值：0
   */
  @SerializedName("StartMs")
  private Integer startMs;

  /**
   * 单句结束时间（毫秒）
   * 注意：此字段可能返回 null，表示取不到有效值。
   * 示例值：2000
   */
  @SerializedName("EndMs")
  private Integer endMs;

  /**
   * 单句中词个数
   * 注意：此字段可能返回 null，表示取不到有效值。
   * 示例值：4
   */
  @SerializedName("WordsNum")
  private Integer wordsNum;

  /**
   * 单句中词详情
   * 注意：此字段可能返回 null，表示取不到有效值。
   */
  @SerializedName("Words")
  private List<SentenceWords> words;

  /**
   * 单句语速，单位：字数/秒
   * 注意：此字段可能返回 null，表示取不到有效值。
   * 示例值：5.9
   */
  @SerializedName("SpeechSpeed")
  private Float speechSpeed;

  /**
   * 声道或说话人 Id（请求中如果设置了 speaker_diarization或者ChannelNum为双声道，可区分说话人或声道）
   * 单声道话者分离时不同的值代表不同的说话人； 8k双声道话者分离时speakerId的值为0代表左声道，值为1代表右声道。
   * 注意：此字段可能返回 null，表示取不到有效值。
   * 示例值：0
   */
  @SerializedName("SpeakerId")
  private Integer speakerId;

  /**
   * 情绪能量值，取值为音量分贝值/10。取值范围：[1,10]。值越高情绪越强烈。
   * 注意：此字段可能返回 null，表示取不到有效值。
   * 示例值：1
   */
  @SerializedName("EmotionalEnergy")
  private Float emotionalEnergy;

  /**
   * 本句与上一句之间的静音时长
   * 注意：此字段可能返回 null，表示取不到有效值。
   * 示例值：0
   */
  @SerializedName("SilenceTime")
  private Integer silenceTime;

  /**
   * 情绪类型（可能为空，有2种情况 1、没有对应资源包；2、情绪跟语音效果相关，如果情绪不够强烈时可能无法识别）
   * 注意：此字段可能返回 null，表示取不到有效值。
   * 示例值：happy
   */
  @SerializedName("EmotionType")
  private List<String> emotionType;

  /**
   * 关键词识别结果列表
   * 注意：此字段可能返回 null，表示取不到有效值。
   */
  @SerializedName("KeyWordResults")
  private List<KeyWordResult> keyWordResults;
}