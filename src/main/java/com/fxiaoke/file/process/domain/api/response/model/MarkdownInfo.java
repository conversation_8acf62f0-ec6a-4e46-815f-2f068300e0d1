package com.fxiaoke.file.process.domain.api.response.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class MarkdownInfo {
  private String path;
  private String filename;
  private String ext;
  private Long size;

  public static MarkdownInfo of(String path, String filename, String ext, Long size) {
    MarkdownInfo markdownInfo = new MarkdownInfo();
    markdownInfo.setPath(path);
    markdownInfo.setFilename(filename);
    markdownInfo.setExt(ext);
    markdownInfo.setSize(size);
    return markdownInfo;
  }
}
