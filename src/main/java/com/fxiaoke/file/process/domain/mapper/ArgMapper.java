package com.fxiaoke.file.process.domain.mapper;

import com.fxiaoke.file.process.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.domain.api.request.JobSubmitArg;
import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.domain.constants.FileType;
import com.fxiaoke.file.process.domain.entity.model.JobParseArg;
import com.fxiaoke.file.process.utils.StrUtils;
import jakarta.annotation.Resource;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.MappingTarget;
import org.springframework.stereotype.Component;

@Component
@Mapper(componentModel = ComponentModel.SPRING)
public abstract class ArgMapper {

  @Resource
  protected CmsPropertiesConfig cmsPropertiesConfig;

  @Mapping(target = "ocrModel", ignore = true)
  @Mapping(target = "ocrUserPrompt", ignore = true)
  @Mapping(target = "ocrSystemPrompt", ignore = true)
  @Mapping(target = "llmEnhancementUserPrompt", ignore = true)
  @Mapping(target = "llmEnhancementSystemPrompt", ignore = true)
  public abstract JobParseArg toJobParseArg(JobSubmitArg arg);

  @AfterMapping
  void afterMapping(@MappingTarget JobParseArg arg) {
    if (!arg.isOcr()) {
      return;
    }
    
    setDefaultIgnoreMinImagePixel(arg);
    configureOcrProperties(arg);
  }

  /**
   * 设置默认的最小图片像素值
   */
  private void setDefaultIgnoreMinImagePixel(JobParseArg arg) {
    if (arg.getIgnoreMinImagePixel() == 0) {
      arg.setIgnoreMinImagePixel(Constants.IGNORE_MIN_IMAGE_PIXEL);
    }
  }

  /**
   * 根据文件类型配置OCR属性
   */
  private void configureOcrProperties(JobParseArg arg) {
    if (isPresentationOrPdfFile(arg.getSourceType())) {

      // 设置OCR属性
      setOcrPropertiesIfBlank(arg, 
          cmsPropertiesConfig.getVlModel(),
          cmsPropertiesConfig.getFullSystemPrompt(),
          cmsPropertiesConfig.getFullUserPrompt());

      if (FileType.isPDF(arg.getSourceType())&&!arg.isOcr()){
        // 如果是PDF文件且未启用OCR，则设置默认的OCR模型和提示
        arg.setLlmEnhancement(true);
        arg.setLlmEnhancementModel(Constants.MISTRAL_OCR_MODEL);
      }

    } else if (isWordOrHtmlFile(arg.getSourceType())) {
      setOcrPropertiesIfBlank(arg,
          cmsPropertiesConfig.getVlModel(),
          cmsPropertiesConfig.getIllustrationSystemPrompt(),
          cmsPropertiesConfig.getIllustrationUserPrompt());
    } else if (isImageFile(arg.getSourceType())) {
      arg.setOcrModel(cmsPropertiesConfig.getVlModel());
      arg.setOcrUserPrompt(cmsPropertiesConfig.getImageUserPrompt());
      arg.setOcrSystemPrompt(cmsPropertiesConfig.getImageSystemPrompt());
    } else if (FileType.isMedia(arg.getSourceType())){
      // 如果是音视频文件需要设置llm模型
      arg.setLlmEnhancement(true);
      arg.setLlmEnhancementModel(Constants.TENCENT_ASR_ENGINE);
    }
  }

  private boolean isImageFile(String sourceType) {
    return FileType.isImage(sourceType);
  }

  /**
   * 判断是否为演示文稿或PDF文件
   */
  private boolean isPresentationOrPdfFile(String sourceType) {
    return FileType.isSlides(sourceType) || FileType.isPDF(sourceType);
  }

  /**
   * 判断是否为文档或HTML文件
   */
  private boolean isWordOrHtmlFile(String sourceType) {
    return FileType.isWords(sourceType) || FileType.isHtml(sourceType);
  }

  /**
   * 统一设置OCR属性（仅在当前值为空时设置）
   */
  private void setOcrPropertiesIfBlank(JobParseArg arg, String model, String systemPrompt, String userPrompt) {
    if (StrUtils.isBlank(arg.getOcrModel())) {
      arg.setOcrModel(model);
    }
    if (StrUtils.isBlank(arg.getOcrSystemPrompt())) {
      arg.setOcrSystemPrompt(systemPrompt);
    }
    if (StrUtils.isBlank(arg.getOcrUserPrompt())) {
      arg.setOcrUserPrompt(userPrompt);
    }
  }
}
