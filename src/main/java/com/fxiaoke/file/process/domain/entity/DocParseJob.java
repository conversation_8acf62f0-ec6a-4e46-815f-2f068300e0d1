package com.fxiaoke.file.process.domain.entity;

import com.fxiaoke.file.process.domain.entity.fieids.DocParseJobFieIds;
import com.fxiaoke.file.process.domain.entity.model.DocBillingRecord;
import com.fxiaoke.file.process.domain.entity.model.JobParseArg;
import com.fxiaoke.file.process.domain.entity.model.JobRecords;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.IndexOptions;
import org.mongodb.morphia.annotations.Indexes;
import org.mongodb.morphia.annotations.Property;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Entity(value = "DocParseJob", noClassnameStored = true)
@Indexes({
    @Index(fields = {@Field(DocParseJobFieIds.uniqueIds)},
        options = @IndexOptions(name = DocParseJobFieIds.uniqueIds_index_name, background = true)),
    @Index(fields = {@Field(DocParseJobFieIds.uniqueIds), @Field(DocParseJobFieIds.jobStatus)},
        options = @IndexOptions(name = DocParseJobFieIds.uniqueIds_jobStatus_index_name, background = true)),
})
public class DocParseJob {

  @Id
  private ObjectId _id;


  @Property(DocParseJobFieIds.uniqueIds)
  private String uniqueIds;

  /**
   * <li><strong>jobStatus:</strong> 任务状态</li>
   * -1:FAILED(任务失败)</br>
   * 0:QUEUE(任务排队中)</br>
   * 1:PREPROCESS(任务进行中)</br>
   * 2:COMPLETED(任务完成)
   */
  @Property(DocParseJobFieIds.jobStatus)
  private int jobStatus;

  @Embedded(DocParseJobFieIds.jobRecords)
  private JobRecords jobRecords;

  @Embedded(DocParseJobFieIds.jobParseArg)
  private JobParseArg jobParseArg;

  @Property(DocParseJobFieIds.fileID)
  private String fileID;

  @Embedded(DocParseJobFieIds.docBillingRecord)
  private DocBillingRecord docBillingRecord;

  @Property(DocParseJobFieIds.billingReportDone)
  private boolean billingReportDone;

  @Property(DocParseJobFieIds.idempotent)
  private boolean idempotent;

  @Property(DocParseJobFieIds.idempotentJobId)
  private String idempotentJobId;
}
