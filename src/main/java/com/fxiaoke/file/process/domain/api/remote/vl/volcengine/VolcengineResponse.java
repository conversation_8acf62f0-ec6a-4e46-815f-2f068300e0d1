package com.fxiaoke.file.process.domain.api.remote.vl.volcengine;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

@Data
public class VolcengineResponse {
    private List<Choice> choices;
    private long created;
    private String id;
    private String model;
    @JsonProperty("service_tier")
    private String serviceTier;
    private String object;
    private Usage usage;

    @Data
    public static class Choice {
        @JsonProperty("finish_reason")
        private String finishReason;
        private int index;
        private Object logprobs;
        private Message message;
    }

    @Data
    public static class Message {
        private String content;
        private String role;
    }

    @Data
    public static class Usage {
        @JsonProperty("completion_tokens")
        private int completionTokens;
        @JsonProperty("prompt_tokens")
        private int promptTokens;
        @JsonProperty("total_tokens")
        private int totalTokens;
        @JsonProperty("prompt_tokens_details")
        private TokenDetails promptTokensDetails;
        @JsonProperty("completion_tokens_details")
        private TokenDetails completionTokensDetails;
    }

    @Data
    public static class TokenDetails {
        @JsonProperty("cached_tokens")
        private int cachedTokens;
        @JsonProperty("reasoning_tokens")
        private int reasoningTokens;
    }
} 