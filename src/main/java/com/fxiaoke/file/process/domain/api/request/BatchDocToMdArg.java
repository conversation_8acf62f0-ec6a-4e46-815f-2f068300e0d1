package com.fxiaoke.file.process.domain.api.request;

import com.fxiaoke.file.process.annotation.TotalSizeLimit;
import com.fxiaoke.file.process.domain.api.request.model.DocInfo;
import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.domain.model.FileDownloadParams;
import com.fxiaoke.file.process.domain.model.FileMetaParams;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class BatchDocToMdArg {

  @NotBlank(message = "ea is required")
  private String ea;

  @NotNull(message = "employeeId is required")
  private Integer employeeId;

  @NotNull(message = "docInfos is required")
  @Size(max = 5, message = "docInfos size must be less than or equal to 5")
  @TotalSizeLimit
  private List<DocInfo> docInfos;

  @AssertTrue(message = "employeeId must be -10000 or greater than 0")
  private boolean validateEmployeeId() {
    return employeeId == -10000 || employeeId > 0;
  }

  public List<FileMetaParams> getFileMetaParams() {
    return docInfos.stream()
        .map(docInfo -> new FileMetaParams(ea, employeeId, docInfo.getPath(),
            ""))
        .toList();
  }

  public List<FileDownloadParams> getFileDownloadParams() {
    return docInfos.stream()
        .map(docInfo -> new FileDownloadParams(ea, employeeId, docInfo.getPath(),
            Constants.DEFAULT_SECURITY_GROUP,docInfo.getFilename(), docInfo.getExt()))
        .toList();
  }
}
