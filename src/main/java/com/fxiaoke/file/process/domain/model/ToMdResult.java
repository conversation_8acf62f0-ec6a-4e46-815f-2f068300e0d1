package com.fxiaoke.file.process.domain.model;

import java.nio.file.Path;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class ToMdResult {
  private int pageCount;
  private String fileName;
  private Path filePath;
  private Usages usages;

  public ToMdResult(int pageCount, String fileName, Path filePath) {
    this.pageCount = pageCount;
    this.fileName = fileName;
    this.filePath = filePath;
  }

  public Path getClearPath() {
    return filePath.getParent();
  }
}
