package com.fxiaoke.file.process.domain.api.request;

import com.fxiaoke.file.process.domain.api.request.model.SentenceDetail;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 腾讯云录音识别回调请求参数
 * <p>
 * 该类用于接收腾讯云录音识别服务的回调请求，包含任务状态、结果等信息。
 * </p>
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class TencentCallbackReq {

  /**
   * 回调任务ID（非腾讯云参数）
   */
  private String jobId;

  /**
   * 回调签名（非腾讯云参数）
   * 用于验证回调请求的合法性，通常是通过预先设定的密钥生成的签名字符串。
   */
  private String sign;

  /**
   * 任务状态码，0为成功，其他：失败
   * 10000 转码失败，请确认音频格式是否符合标准
   * 10001 识别失败
   * 10002 语音时长太短
   * 10003 语音时长太长
   * 10004 无效的语音文件
   * 10005 其他失败
   * 10006 音轨个数不匹配
   * 10007 音频文件下载失败
   */
  private int code;

  /**
   * 失败原因文字描述，成功时此值为空
   */
  private String message;
  /**
   * 任务唯一标识，与录音识别请求中返回的 TaskId 一致
   */
  private Long requestId;

  /**
   * 腾讯云应用 ID
   */
  private Long appid;

  /**
   * 腾讯云项目 ID
   */
  private Integer projectid;

  /**
   * 语音 url，如创建任务时为上传数据的方式，则不包含该字段
   */
  private String audioUrl;

  /**
   * 识别出的结果文本
   */
  private String text;

  /**
   * 包含 详细识别结果，如创建任务时 ResTextFormat 为0，则不包含该字段
   */
  private List<SentenceDetail> resultDetail;

  /**
   * 语音总时长
   */
  private Double audioTime;


  /**
   * 检查是否成功
   */
  public boolean isSuccess() {
    return code == 0;
  }

  /**
   * 获取错误描述
   */
  public String getErrorDescription() {
    return switch (code) {
      case 0 -> "成功";
      case 10000 -> "转码失败，请确认音频格式是否符合标准";
      case 10001 -> "识别失败";
      case 10002 -> "语音时长太短";
      case 10003 -> "语音时长太长";
      case 10004 -> "无效的语音文件";
      case 10005 -> "其他失败";
      case 10006 -> "音轨个数不匹配";
      case 10007 -> "音频下载失败";
      default -> "未知错误码: " + code;
    };
  }

  /**
   * 获取安全的文本内容（避免null）
   */
  public String safeText() {
    return text != null ? text : "";
  }

  /**
   * 获取安全的音频时长
   */
  public double safeAudioTime() {
    return audioTime != null ? audioTime : 0.0;
  }
}
