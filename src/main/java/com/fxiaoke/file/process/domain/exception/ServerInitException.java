package com.fxiaoke.file.process.domain.exception;

import java.util.Arrays;
import lombok.Getter;

@Getter
public class ServerInitException extends RuntimeException {

  private static final String MODULE_NAME = "module:";
  private static final String ARGS_NAME = "args:";
  private final String message;
  private final int code;

  public ServerInitException(int code, String message, String module, Object... args) {
    super(message);
    this.code = code;
    this.message = MODULE_NAME + module + "-" + message + "," + ARGS_NAME + Arrays.toString(args);
  }

  public ServerInitException(Exception e, int code, String module, Object... args) {
    super(e.getMessage(), e);
    this.code = code;
    this.message = MODULE_NAME + module + "-" + e.getMessage() + "," + ARGS_NAME + Arrays.toString(args);
  }
}
