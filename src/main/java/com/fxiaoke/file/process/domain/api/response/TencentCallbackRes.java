package com.fxiaoke.file.process.domain.api.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class TencentCallbackRes {

  /**
   * 响应状态码，0为成功，其他值代表失败
   */
  private int code;

  /**
   * 错误信息
   */
  private String message;

  public static TencentCallbackRes ok() {
    return new TencentCallbackRes(0, "success");
  }

  public static TencentCallbackRes error(int code, String message) {
    return new TencentCallbackRes(code, message);
  }
}
