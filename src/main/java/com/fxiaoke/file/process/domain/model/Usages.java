package com.fxiaoke.file.process.domain.model;

import com.fxiaoke.file.process.domain.constants.Constants;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 模型用量信息
 */
@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class Usages {
  /* 模型名称 */
  private String model;
  /* 总数 */
  private int count;
  /* 总输入token数 */
  private int totalInputTokens;
  /* 总输出token数 */
  private int totalOutputTokens;
  /* 总token数 */
  private int totalTokens;

  public Usages(String model) {
    this.model = model;
  }

  public void addCount() {
    this.count += 1;
  }

  public void addUsage(Usages usage) {
    if (usage != null) {
      this.count += usage.getCount();
      this.totalInputTokens += usage.getTotalInputTokens();
      this.totalOutputTokens += usage.getTotalOutputTokens();
      this.totalTokens += usage.getTotalTokens();
    }
  }

  public void addInputTokens(int inputTokens) {
    this.totalInputTokens += inputTokens;
  }

  public void addInputTokens(long inputTokens) {
    this.totalInputTokens += (int) inputTokens;
  }

  public void addOutputTokens(int outputTokens) {
    this.totalOutputTokens += outputTokens;
  }

  public void addOutputTokens(long outputTokens) {
    this.totalOutputTokens += (int) outputTokens;
  }

  public void addTotalTokens(int totalTokens) {
    this.totalTokens += totalTokens;
  }

  public void addTotalTokens(long totalTokens) {
    this.totalTokens += (int) totalTokens;
  }

  public static Usages ofMistralOcr(int pageCount){
    Usages usages = new Usages();
    usages.setCount(pageCount);
    usages.setModel(Constants.MISTRAL_OCR_MODEL);
    // 每页平均输入输出token数为500（因为算粒需要求使用Token来换算价格）
    int aveInOutToken = pageCount * 500;
    usages.setTotalInputTokens(aveInOutToken);
    usages.setTotalOutputTokens(aveInOutToken);
    usages.setTotalTokens(aveInOutToken * 2);
    return usages;
  }
}
