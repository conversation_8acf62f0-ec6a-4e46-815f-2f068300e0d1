package com.fxiaoke.file.process.domain.model;

import java.nio.file.Path;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class MarkdownImgInfo {

  /* index 图片在文档中的顺序（从1开始） */
  private int index;
  /* originalTag 原始的完整图片标签内容 */
  private String originalTag;
  /* imagePath 图片标签中的文件路径 */
  private Path imagePath;
  /* lineNumber 图片标签在文档中的行号 */
  private int lineNumber;
  /* startIndex 图片标签在行中的起始位置 */
  private int startIndex;
  /* endIndex 图片标签在行中的结束位置 */
  private int endIndex;

  public static MarkdownImgInfo of(int index, String originalTag, Path imagePath, int lineNumber,
      int startIndex, int endIndex) {
    MarkdownImgInfo markdownImgInfo = new MarkdownImgInfo();
    markdownImgInfo.setIndex(index);
    markdownImgInfo.setOriginalTag(originalTag);
    markdownImgInfo.setImagePath(imagePath);
    markdownImgInfo.setLineNumber(lineNumber);
    markdownImgInfo.setStartIndex(startIndex);
    markdownImgInfo.setEndIndex(endIndex);
    return markdownImgInfo;
  }
}
