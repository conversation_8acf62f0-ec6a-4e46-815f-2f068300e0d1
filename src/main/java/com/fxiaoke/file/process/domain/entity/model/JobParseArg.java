package com.fxiaoke.file.process.domain.entity.model;

import com.fxiaoke.file.process.domain.entity.fieids.DocParseJobFieIds;
import com.fxiaoke.file.process.domain.model.FileDownloadParams;
import com.fxiaoke.file.process.domain.model.FileMetaParams;
import com.google.common.hash.Hashing;
import java.nio.charset.StandardCharsets;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.mongodb.morphia.annotations.Property;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class JobParseArg {

  @Property(DocParseJobFieIds.ea)
  private String ea;

  @Property(DocParseJobFieIds.employeeId)
  private Integer employeeId;

  @Property(DocParseJobFieIds.path)
  private String path;

  @Property(DocParseJobFieIds.securityGroup)
  private String securityGroup;

  @Property(DocParseJobFieIds.sourceType)
  private String sourceType;

  @Property(DocParseJobFieIds.targetType)
  private String targetType;

  @Property(DocParseJobFieIds.fileName)
  private String fileName;

  @Property(DocParseJobFieIds.ocr)
  private boolean ocr;

  @Property(DocParseJobFieIds.ocrModel)
  private String ocrModel;

  @Property(DocParseJobFieIds.ocrSystemPrompt)
  private String ocrSystemPrompt;

  @Property(DocParseJobFieIds.ocrUserPrompt)
  private String ocrUserPrompt;

  @Property(DocParseJobFieIds.ignoreMinImagePixel)
  private int ignoreMinImagePixel;

  @Property(DocParseJobFieIds.llmEnhancement)
  private boolean llmEnhancement;

  @Property(DocParseJobFieIds.llmEnhancementModel)
  private String llmEnhancementModel;

  @Property(DocParseJobFieIds.llmEnhancementSystemPrompt)
  private String llmEnhancementSystemPrompt;

  @Property(DocParseJobFieIds.llmEnhancementUserPrompt)
  private String llmEnhancementUserPrompt;

  @Property(DocParseJobFieIds.business)
  private String business;

  @Property(DocParseJobFieIds.displayName)
  private String displayName;

  @Property(DocParseJobFieIds.apiName)
  private String apiName;

  @Property(DocParseJobFieIds.asyncCallbackMqTag)
  private String asyncCallbackMqTag;

  public String getUniqueIds() {
    return ea + ":" + Hashing.sha256().hashString(uniqueStr(), StandardCharsets.UTF_8);
  }

  public FileMetaParams getFileMetaParams() {
    return new FileMetaParams(getEa(), getEmployeeId(),
        getPath(), getSecurityGroup());
  }

  public FileDownloadParams getFileDownloadParams() {
    return new FileDownloadParams(getEa(), getEmployeeId(),
        getPath(), getSecurityGroup(),getFileName(),getSourceType());
  }

  private String uniqueStr() {
    return ea + ":" + path + ":"
        + sourceType + ":" + targetType + ":"
        + ocr + ":" + ocrModel + ":"
        + llmEnhancement + ":" + llmEnhancementModel;
  }

  @Override
  public String toString() {
    return "JobParseArg{" +
        "ea='" + ea + '\'' +
        ", employeeId=" + employeeId +
        ", path='" + path + '\'' +
        ", securityGroup='" + securityGroup + '\'' +
        ", sourceType='" + sourceType + '\'' +
        ", targetType='" + targetType + '\'' +
        ", fileName='" + fileName + '\'' +
        ", ocr=" + ocr +
        ", ocrModel='" + ocrModel + '\'' +
        ", ignoreMinImagePixel=" + ignoreMinImagePixel +
        ", llmEnhancement=" + llmEnhancement +
        ", business='" + business + '\'' +
        ", displayName='" + displayName + '\'' +
        ", apiName='" + apiName + '\'' +
        ", asyncCallbackMqTag='" + asyncCallbackMqTag + '\'' +
        ", llmEnhancementModel='" + llmEnhancementModel + '\'' +
        '}';
  }
}
