package com.fxiaoke.file.process.domain.api.remote.bigfile;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.bson.types.ObjectId;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class FileMetaEntity {
  protected ObjectId id;
  private String eTag;
  private String objectKey;  // Ali-OSS 对应的Key值
  private String name;
  private Long size;
  private String filePath;
  private String fileExt;
  private String mimeType;
  private String ownEA;
  private boolean deleted;
  private boolean ossDeleted;                     // 阿里云OSS上的源文件是否已删除
  private String appName;
  private Long createTime;
  private Long updateTime;
  private Long expireTime;
  private Long gcTime;
}
