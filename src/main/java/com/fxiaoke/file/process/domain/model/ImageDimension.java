package com.fxiaoke.file.process.domain.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
public class ImageDimension {
  private int width;
  private int height;

  public ImageDimension(int width, int height) {
    this.width = width;
    this.height = height;
  }

  public int getResolution() {
    return width * height;
  }
}
