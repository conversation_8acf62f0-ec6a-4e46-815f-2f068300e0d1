package com.fxiaoke.file.process.domain.exception;

import com.fxiaoke.file.process.domain.constants.ErrorCodes;
import jakarta.validation.ConstraintDeclarationException;
import java.util.Arrays;
import lombok.Getter;

/**
 * 特殊校验异常，需要自定义错误码
 */
@Getter
public class BusException extends ConstraintDeclarationException {

  private static final String MODULE_NAME = "module:";
  private static final String ERROR_REASON = "reason:";
  private static final String ARGS_NAME = "args:";
  private final int code;
  private final int busCode;
  private final String message;
  private final String reason;

  public BusException(int code, int busCode, String message, String module, Object... args) {
    super(message);
    this.code = code;
    this.busCode = busCode;
    this.message = message;
    this.reason = MODULE_NAME + module + "-" +
        ERROR_REASON + message + "-" +
        ARGS_NAME + Arrays.toString(args);
  }

  /**
   * 创建OCR必须启用异常
   */
  public static BusException ocrRequired(String module, Object... args) {
    return new BusException(ErrorCodes.HTTP_BAD_REQUEST, ErrorCodes.OCR_REQUIRED,
        "OCR must be enabled for image files", module, args);
  }
}
