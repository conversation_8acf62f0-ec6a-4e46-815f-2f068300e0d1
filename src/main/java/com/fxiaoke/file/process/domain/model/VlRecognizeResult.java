package com.fxiaoke.file.process.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 视觉模型识别结果
 */
@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class VlRecognizeResult {
  /* 模型名称 */
  private String model;
  /* 输入token数 */
  private int inputToken;
  /* 输出token数 */
  private int outputToken;
  /* 总token数 */
  private int totalToken;
  /* 图片信息 */
  private MarkdownImgInfo markdownImgInfo;
}
