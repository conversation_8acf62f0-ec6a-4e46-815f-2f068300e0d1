package com.fxiaoke.file.process.domain.constants;

public class Constants {

  private Constants() {
  }

  // 本服务的业务标识
  public static final String BUSINESS = "FileProcess";

  // 网盘默认安全组
  public static final String DEFAULT_SECURITY_GROUP = "XiaoKeNetDisk";

  // 批量转换Markdown文件总大小限制
  public static final int BATCH_TO_MD_MAX_SIZE = 10485760;

  // 单个文档转Markdown任务文件大小限制
  public static final int SINGLE_TO_MD_MAX_SIZE = 104857600;

  // 单个图片转Markdown任务文件大小限制
  public static final int SINGLE_IMAGE_TO_MD_MAX_SIZE = 10485760;

  // MQ-文档解析任务标签

  // 文档解析任务提交
  public static final String MQ_TAG_DOC_PARSE_JOB_SUBMIT = "doc_parse_job";
  // 远程依赖异步解析任务
  public static final String MQ_TAG_DOC_ASYNC_TASK = "doc_async_task";

  public static final String MQ_TAG_DOC_TOKEN_REPORT = "doc_token_report";
  public static final String MQ_TAG_DOC_TASK_WAKEUP = "doc_task_wakeup";

  // Mongo GridFS默认存储桶
  public static final String GRID_FS_DEFAULT_BUCKET = "FileProcess";

  // ====== S3 =====

  public static final String S3_BUCKET_ROOT_DIR_VL_OCR = "vl-ocr";
  public static final String S3_BUCKET_ROOT_DIR_MISTRAL_OCR = "mistral-ocr";

  // ====== Image ======

  public static final int DCI_2K = 2211840;

  // ====== Aspose ======
  public static final String CACHE_FOLDER = "/opt/tomcat/localCache";
  public static final String SLIDES_CACHE_FOLDER = CACHE_FOLDER + "/slides";
  public static final String CELLS_CACHE_FOLDER = CACHE_FOLDER + "/cells";
  public static final String PDF_CACHE_FOLDER = CACHE_FOLDER + "/pdf";
  public static final String WORD_CACHE_FOLDER = CACHE_FOLDER + "/word";

  // ====== Ark VL ======

  public static final String VL_DEFAULT_MODEL = "ep-m-20250514113154-9whxf";

  public static final String INVALID_CONTENT = "<InvalidImage/>";

  public static final String IMAGE_TAG_START = "<img>";
  public static final String IMAGE_TAG_END = "</img>";

  // 火山引擎 视觉模型温度、置信度配置
  public static final Double ARK_TEMPERATURE = 1.0D;
  public static final Double ARK_TOP_P = 0.7D;
  public static final Integer ARK_MAX_TOKENS = 4096;
  public static final Double ARK_FREQUENCY_PENALTY = 0D;

  // 默认忽略文档中图片大小
  public static final int IGNORE_MIN_IMAGE_PIXEL = 10000;

  // ====== Mistral OCR ======
  public static final String MISTRAL_OCR_MODEL = "mistral-ocr-latest";
  public static final String MISTRAL_OCR_DOCUMENT_SOURCE_TYPE = "document_url";

  // ====== Tencent ASR ======
  public static final String DEFAULT_REC_ENGINE = "16k_zh";
  public static final String TENCENT_ASR_ENGINE = "tencent-asr-16k_zh";

  public static final Long SOURCE_TYPE_URL = 0L;
  public static final Long CHANNEL_NUM_ONE = 1L;
  public static final Long SpeakerDiarization = 0L;
  public static final Long SpeakerNumber = 0L;
  public static final Long ResTextFormat = 1L;



}
