package com.fxiaoke.file.process.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 为防止有限资源被无限制的请求占用
 * 锁定资源的实体类，避免资源被过多请求占用，造成资源耗尽
 * <p>
 */
@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class RsLock {

  private String lockType;

  private long currentValue;

  private long maxValue;

  private boolean isLocked;
}
