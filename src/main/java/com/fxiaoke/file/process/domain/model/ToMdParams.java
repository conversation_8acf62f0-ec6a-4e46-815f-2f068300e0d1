package com.fxiaoke.file.process.domain.model;

import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.domain.constants.VisCompDetail;
import com.fxiaoke.file.process.domain.entity.model.JobParseArg;
import com.fxiaoke.file.process.utils.FilenameUtil;
import java.nio.file.Path;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
public class ToMdParams {

  private String jobId;

  private String ea;
  private int employeeId;

  private String fileName;
  private Path filePath;

  private boolean highPerformance = false;

  private VisCompConfig visCompConfig;

  public Path generatMdFilePath() {
    return FilenameUtil.replaceExtension(filePath, "md");
  }

  public ToMdParams(String fileName, Path filePath) {
    this.fileName = fileName;
    this.filePath = filePath;
    this.visCompConfig = new VisCompConfig();
    this.visCompConfig.setOcr(false);
    this.visCompConfig.setIgnoreMinImagePixel(Constants.IGNORE_MIN_IMAGE_PIXEL);
  }

  public static ToMdParams of(String fileName, Path filePath) {
    return new ToMdParams(fileName, filePath);
  }

  public static ToMdParams of(String fileName, Path filePath, JobParseArg arg) {
    ToMdParams params = new ToMdParams(fileName, filePath);
    VisCompConfig visCompConfig = new VisCompConfig();
    visCompConfig.setOcr(arg.isOcr());
    visCompConfig.setModel(arg.getOcrModel());
    visCompConfig.setSystemPrompt(arg.getOcrSystemPrompt());
    visCompConfig.setUserPrompt(arg.getOcrUserPrompt());
    visCompConfig.setIgnoreMinImagePixel(arg.getIgnoreMinImagePixel());
    visCompConfig.setDetail(VisCompDetail.HIGH);
    params.setVisCompConfig(visCompConfig);
    return params;
  }

  public String getParentDir() {
    return filePath.getParent().toString();
  }
}
