package com.fxiaoke.file.process.domain.model;

import java.nio.file.Path;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class FileUploadParams {

  private String ea;
  private Integer employeeId;
  private Path filePath;
  private String sg;
  private String fileName;
  private String extension;
}
