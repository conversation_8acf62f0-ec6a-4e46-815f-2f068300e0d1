package com.fxiaoke.file.process.domain.api.response;

import com.fxiaoke.file.process.domain.model.Billing;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class JobQueryResult {

  /**
   * 任务ID
   */
  private String jobId;

  /**
   * <li><strong>jobStatus:</strong> 任务状态</li>
   * -1:FAILED(任务失败)</br> 0:QUEUE(任务排队中)</br> 1:PREPROCESS(任务进行中)</br> 2:COMPLETED(任务完成)
   */
  private int jobStatus;

  /**
   * 账单信息
   * <ul>
   *   <li>文档页数：由 {@code pageCount} 表示。</li>
   *   <li>文档大小：由 {@code fileSize} 表示。</li>
   *   <li>图片数量：由 {@code imageCount} 表示，通过文档解析生成的图片数。</li>
   *   <li>模型名称：由 {@code modelName} 表示。</li>
   *   <li>输入Token总数：由 {@code totalInputTokens} 表示。</li>
   *   <li>输出Token总数：由 {@code totalOutputTokens} 表示。</li>
   *   <li>总Token数：由 {@code totalTokens} 表示，是输入和输出Token的总和。</li>
   *   <li>定价代币：由 {@code pricingTokens} 表示，用于计算任务的费用。</li>
   * </ul>
   */
  private Billing billing;

  /**
   * 表示错误代码，用于标识任务执行过程中出现的错误类型或状态。
   * <ul>
   *   <li>0 默认值，表示无异常</li>
   *   <li>4xx 表示客户端错误</li>
   *   <li>5xx 表示服务端异常</li>
   * </ul>
   */
  private int errCode;

  /**
   * 表示错误消息的详细描述信息，通常用于补充说明错误代码（errCode）<br>
   * 更易于定位和解决问题。
   */
  private String errMsg;

  /**
   * trace id 用于追踪和查询本次请求的相关信息。
   */
  private String requestId;
}