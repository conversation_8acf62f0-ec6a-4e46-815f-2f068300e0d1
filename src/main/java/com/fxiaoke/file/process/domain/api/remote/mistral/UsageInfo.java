package com.fxiaoke.file.process.domain.api.remote.mistral;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@JsonIgnoreProperties(ignoreUnknown = true)
public class UsageInfo {
  @JsonProperty("pages_processed")
  private int pagesProcessed;
  @JsonProperty("doc_size_bytes")
  private int docSizeBytes;
}
