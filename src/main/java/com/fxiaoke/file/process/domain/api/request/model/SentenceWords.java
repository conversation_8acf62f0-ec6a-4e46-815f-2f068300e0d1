package com.fxiaoke.file.process.domain.api.request.model;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 识别结果中词文本，以及对应时间偏移
 * 被如下接口引用：DescribeTaskStatus。
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class SentenceWords {

  /**
   * 词文本
   * 注意：此字段可能返回 null，表示取不到有效值。
   * 示例值：晴天
   */
  @SerializedName("Word")
  private String word;

  /**
   * 在句子中的开始时间偏移量
   * 注意：此字段可能返回 null，表示取不到有效值。
   * 示例值：200
   */
  @SerializedName("OffsetStartMs")
  private Integer offsetStartMs;

  /**
   * 在句子中的结束时间偏移量
   * 注意：此字段可能返回 null，表示取不到有效值。
   * 示例值：400
   */
  @SerializedName("OffsetEndMs")
  private Integer offsetEndMs;
}
