package com.fxiaoke.file.process.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * ToKenReport 类用于表示令牌使用的详细报告信息。
 * 包含租户信息、用户信息、业务信息、API名称、模型信息以及令牌统计数据。
 */
@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class ToKenReport {

  /** 企业账号 */
  private String ea;

  /** 员工ID */
  private Integer employeeId;


  /** 业务名称 */
  private String business;

  /** API名称 */
  private String apiName;

  /** 显示名称 */
  private String displayName;


  /** 模型名称 */
  private String model;

  /** 输入令牌数 */
  private Integer promptTokens = 0;

  /** 输出令牌数 */
  private Integer completionTokens;

  /** 总牌数 */
  private Integer totalTokens;

  /** 算粒 */
  private Double pricingTokens;
}
