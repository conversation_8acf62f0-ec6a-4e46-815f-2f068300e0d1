package com.fxiaoke.file.process.domain.api.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class JobGetContent {

  @NotBlank(message = "jobId not be blank")
  @Pattern(
      regexp = "^[0-9a-fA-F]{24}$",
      message = "invalid jobId"
  )
  private String jobId;
}