package com.fxiaoke.file.process.domain.api.remote.mistral;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@JsonIgnoreProperties(ignoreUnknown = true)
public class Image {
  private String id;

  @JsonProperty("top_left_x")
  private int topLeftX;

  @JsonProperty("top_left_y")
  private int topLeftY;

  @JsonProperty("bottom_right_x")
  private int bottomRightX;

  @JsonProperty("bottom_right_y")
  private int bottomRightY;

  @JsonProperty("image_base64")
  private String imageBase64;

  @JsonProperty("image_annotation")
  private String imageAnnotation;
}
