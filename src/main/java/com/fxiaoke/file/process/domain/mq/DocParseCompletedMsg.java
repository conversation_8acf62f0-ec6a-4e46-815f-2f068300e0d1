package com.fxiaoke.file.process.domain.mq;

import com.github.trace.TraceContext;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <h2>文档解析完成消息</h2>
 * <ul>
 * <li><strong>ea:</strong> 企业账号</li>
 * <li><strong>jobId:</strong> 任务ID</li>
 * <li><strong>jobFailCode:</strong> 任务失败错误码 4xx 客户端错误, 5xx 服务端错误</li>
 * <li><strong>jobFailMsg:</strong> 任务失败信息</li>
 * <li><strong>success:</strong> 任务是否成功</li>
 * </ul>
 */
@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class DocParseCompletedMsg {

  /**
   * 企业账号
   */
  private String ea;

  /**
   * 任务ID
   */
  private String jobId;

  /**
   * 任务是否成功
   */
  private boolean success;

  /**
   * 任务失败错误码 4xx 客户端错误, 5xx 服务端错误
   */
  private Integer jobFailCode;

  /**
   * 任务失败信息
   */
  private String jobFailMsg;

  /**
   * trace id 用于追踪日志
   */
  private String requestId;

  public static DocParseCompletedMsg ok(String ea,String jobId){
    DocParseCompletedMsg msg = new DocParseCompletedMsg();
    msg.setEa(ea);
    msg.setJobId(jobId);
    msg.setSuccess(true);
    msg.setRequestId(TraceContext.get().getTraceId());
    return msg;
  }

  public static DocParseCompletedMsg fail(String ea,String jobId,Integer jobFailCode,String jobFailMsg){
    DocParseCompletedMsg msg = new DocParseCompletedMsg();
    msg.setEa(ea);
    msg.setJobId(jobId);
    msg.setJobFailCode(jobFailCode);
    msg.setJobFailMsg(jobFailMsg);
    msg.setSuccess(false);
    msg.setRequestId(TraceContext.get().getTraceId());
    return msg;
  }
}
