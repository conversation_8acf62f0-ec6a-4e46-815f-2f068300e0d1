package com.fxiaoke.file.process.domain.entity;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.IndexOptions;
import org.mongodb.morphia.annotations.Indexes;

import com.fxiaoke.file.process.domain.entity.fieids.AsyncPollingTaskFieIds;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Entity(value = "AsyncPollingTask", noClassnameStored = true)
@Indexes({@Index(fields = {@Field(AsyncPollingTaskFieIds.jobId)},
        options = @IndexOptions(name = AsyncPollingTaskFieIds.jobId_index_name, background = true)),
    @Index(fields = {@Field(AsyncPollingTaskFieIds.jobId), @Field(AsyncPollingTaskFieIds.jobStatus)},
        options = @IndexOptions(name = AsyncPollingTaskFieIds.jobId_jobStatus_index_name, background = true)),
    @Index(fields = {@Field(AsyncPollingTaskFieIds.taskId)},
        options = @IndexOptions(name = AsyncPollingTaskFieIds.taskId_index_name, background = true))})
public class AsyncPollingTask {

  @Id
  private ObjectId _id;

  /**
   * 当前任务-任务ID
   */
  private String jobId;

  /**
   * 企业账号
   */
  private String ea;

  /**
   * 员工ID
   */
  private int employeeId;

  /**
   * 异步任务类型
   */
  private String taskType;

  /**
   * 第三方请求ID
   */
  private String requestId;

  /**
   * 第三方任务ID(轮询任务ID)
   */
  private String taskId;

  /**
   * 任务状态
   * 0: 未开始
   * 1: 进行中
   * 2: 成功
   * -1: 失败
   */
  private int jobStatus;

  private Date createTime;

  private Date updateTime;
}
