package com.fxiaoke.file.process.domain.api.request;

import com.fxiaoke.file.process.domain.model.FileDownloadParams;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.io.FilenameUtils;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class DocRanksStreamArg {

  @NotBlank(message = "ea is required")
  private String ea;

  private Integer employeeId;

  @NotBlank
  @Pattern(regexp = "^(N_|TN_|C_|TC_|TA_|A_|G_).{0,48}$", message = "Invalid path")
  private String path;

  private String securityGroup;

  @NotBlank
  @Pattern(regexp = "^(xls|xlsx)$", message = "Invalid type")
  private String sourceType;

  private boolean rowRead;

  public String getPath() {
    return FilenameUtils.getBaseName(path);
  }

  public FileDownloadParams getFileDownloadParams() {
    return new FileDownloadParams(ea, employeeId, getPath(), securityGroup,"", sourceType);
  }
}
