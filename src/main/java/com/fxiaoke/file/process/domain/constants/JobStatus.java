package com.fxiaoke.file.process.domain.constants;

import lombok.Getter;

/**
 * <li><strong>jobStatus:</strong> 任务状态</li>
 * -1:FAILED(任务失败)</br>
 * 0:QUEUE(任务排队中)</br>
 * 1:PREPROCESS(任务进行中)</br>
 * 2:COMPLETED(任务完成)
 */
@Getter
public enum JobStatus {

  FAILED(-1),
  QUEUE(0),
  PREPROCESS(1),
  COMPLETED(2);

  private final int value;

  JobStatus(int value) {
    this.value = value;
  }

  public static JobStatus of(int value) {
    for (JobStatus status : values()) {
      if (status.value == value) {
        return status;
      }
    }
    throw new IllegalArgumentException("Invalid DocParseJobStatus value: " + value);
  }
}
