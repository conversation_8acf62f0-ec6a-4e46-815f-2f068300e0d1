package com.fxiaoke.file.process.domain.api.request.model;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class DocInfo {
  @NotBlank(message = "path is required")
  @Pattern(
      regexp = "^(N_|TN_).{0,48}$",
      message = "Invalid path"
  )
  private String path;

  @NotBlank(message = "filename is required")
  private String filename;

  @NotBlank(message = "ext is required")
  @Pattern(
      regexp = "^(pdf|docx|doc|ppt|pptx|xls|xlsx)$",
      message = "Invalid ext"
  )
  private String ext;

  @NotNull(message = "size is required")
  @Min(value = 205,message = "size must be greater than or equal to 205")
  @Max(value = 5242880,message = "size must be less than or equal to 5242880")
  private Integer size;
}
