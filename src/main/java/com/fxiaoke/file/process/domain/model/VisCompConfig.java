package com.fxiaoke.file.process.domain.model;

import com.fxiaoke.file.process.domain.constants.VisCompDetail;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class VisCompConfig {
  private boolean ocr;
  private String model;
  private VisCompDetail detail;
  private String systemPrompt;
  private String userPrompt;
  private int ignoreMinImagePixel;
}
