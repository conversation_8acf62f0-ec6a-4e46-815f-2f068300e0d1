package com.fxiaoke.file.process.domain.constants;

import lombok.Getter;

/**
 * RocketMQ 延时级别枚举
 */
@Getter
public enum MessageDelayLevel {

  // 不延时
  LEVEL_0(0, "0s", 0),
  // 延时
  LEVEL_1(1, "1s", 1000),
  LEVEL_2(2, "5s", 5000),
  LEVEL_3(3, "10s", 10000),
  LEVEL_4(4, "30s", 30000),
  LEVEL_5(5, "1m", 60000),
  LEVEL_6(6, "2m", 120000),
  LEVEL_7(7, "3m", 180000),
  LEVEL_8(8, "4m", 240000),
  LEVEL_9(9, "5m", 300000),
  LEVEL_10(10, "6m", 360000),
  LEVEL_11(11, "7m", 420000),
  LEVEL_12(12, "8m", 480000),
  LEVEL_13(13, "9m", 540000),
  LEVEL_14(14, "10m", 600000),
  LEVEL_15(15, "20m", 1200000),
  LEVEL_16(16, "30m", 1800000),
  LEVEL_17(17, "1h", 3600000),
  LEVEL_18(18, "2h", 7200000);

  /**
   * 延时级别
   */
  private final int level;

  /**
   * 延时描述
   */
  private final String desc;

  /**
   * 延时毫秒数
   */
  private final long delayTimeMillis;

  MessageDelayLevel(int level, String desc, long delayTimeMillis) {
    this.level = level;
    this.desc = desc;
    this.delayTimeMillis = delayTimeMillis;
  }

  /**
   * 获取延时级别
   *
   * @param timeMillis 延时时间(毫秒)
   * @return 延时级别
   */
  public static MessageDelayLevel getLevel(long timeMillis) {
    MessageDelayLevel[] values = MessageDelayLevel.values();
    for (int i = values.length - 1; i >= 0; i--) {
      if (timeMillis >= values[i].getDelayTimeMillis()) {
        return values[i];
      }
    }
    return LEVEL_1;
  }

  /**
   * 获取大于指定时间的最小延时级别
   *
   * @param timeMillis 延时时间(毫秒)
   * @return 延时级别
   */
  public static MessageDelayLevel getMinLevel(long timeMillis) {
    MessageDelayLevel[] values = MessageDelayLevel.values();
    for (MessageDelayLevel value : values) {
      if (value.getDelayTimeMillis() >= timeMillis) {
        return value;
      }
    }
    return LEVEL_18;
  }

  /**
   * 是否为有效的延时级别
   *
   * @param level 延时级别
   * @return true:有效 false:无效
   */
  public static boolean isValidLevel(int level) {
    return level >= LEVEL_1.getLevel() && level <= LEVEL_18.getLevel();
  }
}

