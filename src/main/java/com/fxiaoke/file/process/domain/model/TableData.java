package com.fxiaoke.file.process.domain.model;

import com.fxiaoke.file.process.utils.MarkdownFormatUtil;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class TableData {
  private final List<List<String>> data;
  private int rows;
  private int columns;
  private final int slideNumber;      // PPT页码
  private final int tableIndex;       // 当前页面中的表格序号

  public TableData(int slideNumber, int tableIndex) {
    this.data = new ArrayList<>();
    this.slideNumber = slideNumber;
    this.tableIndex = tableIndex;
  }

  // 添加行数据
  public void addRow(List<String> row) {
    data.add(row);
    updateDimensions();
  }

  private void updateDimensions() {
    this.rows = data.size();
    this.columns = data.isEmpty() ? 0 : data.getFirst().size();
  }

  // 转换为Markdown格式，包含页面信息
  public String toMarkdown() {
    if (data.isEmpty()) return "";

    StringBuilder sb = new StringBuilder();

    // 添加表格内容
    sb.append("|");
    for (String cell : data.getFirst()) {
      sb.append(" ").append(standardizeLineBreaks(cell)).append(" |");
    }
    sb.append(MarkdownFormatUtil.LINE_SEPARATOR);

    // 添加分隔行
    sb.append("|");
    sb.append(" --- |".repeat(Math.max(0, columns)));
    sb.append(MarkdownFormatUtil.LINE_SEPARATOR);

    // 添加数据行
    for (int i = 1; i < rows; i++) {
      sb.append("|");
      for (String cell : data.get(i)) {
        sb.append(" ").append(standardizeLineBreaks(cell)).append(" |");
      }
      sb.append(MarkdownFormatUtil.LINE_SEPARATOR);
    }

    return sb.toString();
  }

  // 表格内换行符标准化
  private String standardizeLineBreaks(String cell) {
    if (cell!=null&&!cell.isEmpty()){
      return cell.replaceAll("\r\n", MarkdownFormatUtil.HTML_LINE_SEPARATOR)
          .replaceAll("\n", MarkdownFormatUtil.HTML_LINE_SEPARATOR)
          .replaceAll("\r", MarkdownFormatUtil.HTML_LINE_SEPARATOR);
    }
    return cell;
  }
}
