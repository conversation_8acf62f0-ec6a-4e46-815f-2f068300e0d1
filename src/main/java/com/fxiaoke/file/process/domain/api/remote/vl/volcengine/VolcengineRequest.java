package com.fxiaoke.file.process.domain.api.remote.vl.volcengine;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

@Data
public class VolcengineRequest {
    private String model;

    /**
     * 采样温度。控制了生成文本时对每个候选词的概率分布进行平滑的程度。。当取值为 0 时模型仅考虑对数概率最大的一个 token。
     * 较高的值（如 0.8）会使输出更加随机，而较低的值（如 0.2）会使输出更加集中确定。
     * 通常建议仅调整 temperature 或 top_p 其中之一，不建议两者都修改。
     */
    private Double temperature;

    /**
     * 取值范围为 [0, 1]。
     * 核采样概率阈值。模型会考虑概率质量在 top_p 内的 token 结果。当取值为 0 时模型仅考虑对数概率最大的一个 token。
     * 0.1 意味着只考虑概率质量最高的前 10% 的 token，取值越大生成的随机性越高，取值越低生成的确定性越高。通常建议仅调整
     */
    @JsonProperty("top_p")
    private Double topP;

    /**
     * 生成的答案允许的最大令牌数.
     */
    @JsonProperty("max_tokens")
    Integer maxTokens;

    /**
     * 频率惩罚系数
     * 取值范围 [-2.0,2.0]
     * 如果值为正，会根据新 token 在文本中的出现频率对其进行惩罚，从而降低模型逐字重复的可能性。
     */
    @JsonProperty("frequency_penalty")
    Double frequencyPenalty;

    private List<Message> messages;

    @Data
    public static class Message {
        private List<Content> content;
        private String role;
    }

    @Data
    public static class Content {
        private String text;
        private String type;
        @JsonProperty("image_url")
        private ImageUrl imageUrl;
    }

    @Data
    public static class ImageUrl {
        private String url;
        /**
         * messages.content.image_url.detail string / null  默认值 auto <br>
         * 支持手动设置图片的质量，取值范围high、low、auto。<br>
         * high：高细节模式，适用于需要理解图像细节信息的场景，如对图像的多个局部信息/特征提取、复杂/丰富细节的图像理解等场景，理解更全面。<br>
         * low：低细节模式，适用于简单的图像分类/识别、整体内容理解/描述等场景，理解更快速。<br>
         * auto：默认模式，不同模型选择的模式略有不同，具体请参见理解 <a href="https://www.volcengine.com/docs/82379/1362931#bf4d9224">图像的深度控制</a>。
         */
        private String detail;
    }
} 