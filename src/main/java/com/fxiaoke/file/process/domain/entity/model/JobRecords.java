package com.fxiaoke.file.process.domain.entity.model;

import com.fxiaoke.file.process.domain.entity.fieids.DocParseJobFieIds;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class JobRecords {

  @Property(DocParseJobFieIds.jobSubmitTime)
  private Date jobSubmitTime;

  @Property(DocParseJobFieIds.jobProcessStartTime)
  private Date jobProcessStartTime;

  @Property(DocParseJobFieIds.jobProcessEndTime)
  private Date jobProcessEndTime;

  @Property(DocParseJobFieIds.jobImgProcessCostTime)
  private long jobImgProcessCostTime;

  @Property(DocParseJobFieIds.jobCompletionTime)
  private Date jobCompletionTime;

  @Property(DocParseJobFieIds.billingReportTime)
  private Date billingReportTime;

  @Property(DocParseJobFieIds.jobFailTime)
  private Date jobFailTime;

  @Property(DocParseJobFieIds.jobFailCode)
  private int jobFailCode;

  @Property(DocParseJobFieIds.jobFailReason)
  private String jobFailReason;

  public static JobRecords jobInit(){
    JobRecords jobRecords = new JobRecords();
    jobRecords.setJobSubmitTime(new Date());
    return jobRecords;
  }
}
