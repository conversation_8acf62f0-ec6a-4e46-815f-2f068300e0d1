package com.fxiaoke.file.process.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class Billing {

  /**
   * 表示文档的页数。
   * 用于记录文档中包含的总页数，以便后续进行文档分割、分析或其他处理操作。
   */
  private int pageCount;

  /**
   * 表示文档的文件大小。
   * 用于记录文档的体积大小，以字节为单位。
   */
  private long fileSize;

  /**
   * 表示文档中包含的图片总数。
   * 用于统计文档中图片的数量，以便后续进行分析或处理。
   */
  private int imageCount;

  /**
   * 模型名称，用于标识所使用的模型的名称或类型。
   * 在文档解析、模型运行或相关处理过程中，用作模型的唯一标识。
   */
  private String modelName;

  /**
   * 表示输入的总Token数量。
   * 用于记录在处理文档或模型运行过程中接收的所有Token的总和，
   * 以便统计输入资源消耗或评估模型输入规模。
   */
  private int totalInputTokens;

  /**
   * 表示输出的总Token数量。
   * 用于记录在处理文档或模型运行过程中生成的所有Token总和，
   * 以便统计模型性能或计算相关消耗。
   */
  private int totalOutputTokens;

  /**
   * 总的Token数量。
   * 表示输入Token与输出Token的总和，用于衡量文档加工或模型操作中的总体Token消耗情况。
   */
  private int totalTokens;

  // 纷享定价算粒代币(1算粒≈1毛钱)
  private double pricingTokens;
}
