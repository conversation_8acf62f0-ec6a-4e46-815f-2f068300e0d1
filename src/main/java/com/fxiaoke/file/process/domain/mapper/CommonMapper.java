package com.fxiaoke.file.process.domain.mapper;

import com.fxiaoke.file.process.domain.api.response.JobQueryResult;
import com.fxiaoke.file.process.domain.entity.DocParseJob;
import com.fxiaoke.file.process.domain.entity.model.DocBillingRecord;
import com.fxiaoke.file.process.domain.entity.model.JobParseArg;
import com.fxiaoke.file.process.domain.model.Billing;
import com.fxiaoke.file.process.domain.model.ToKenReport;
import com.github.trace.TraceContext;
import org.bson.types.ObjectId;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.springframework.stereotype.Component;

@Component
@Mapper(componentModel = ComponentModel.SPRING)
public abstract class CommonMapper {

  public abstract Billing toBilling(DocBillingRecord docBillingRecord);

  @Mapping(target = "jobId", source = "_id")
  @Mapping(target = "jobStatus", source = "jobStatus")
  @Mapping(target = "billing", source = "docBillingRecord")
  @Mapping(target = "errCode", constant = "0")
  @Mapping(target = "errMsg", constant = "")
  @Mapping(target = "requestId", expression = "java(getTraceId())")
  public abstract JobQueryResult toJobQueryResult(DocParseJob docParseJob);

  protected String map(ObjectId value) {
    return value != null ? value.toString() : null;
  }

  protected String getTraceId() {
    return TraceContext.get().getTraceId();
  }

  @Mapping(target = "ea", source = "jobParseArg.ea")
  @Mapping(target = "employeeId", source = "jobParseArg.employeeId")
  @Mapping(target = "business", source = "jobParseArg.business")
  @Mapping(target = "apiName", source = "jobParseArg.apiName")
  @Mapping(target = "displayName", source = "jobParseArg.displayName")
  @Mapping(target = "model", source = "docBillingRecord.modelName")
  @Mapping(target = "promptTokens", source = "docBillingRecord.totalInputTokens")
  @Mapping(target = "completionTokens", source = "docBillingRecord.totalOutputTokens")
  @Mapping(target = "totalTokens", source = "docBillingRecord.totalTokens")
  @Mapping(target = "pricingTokens", source = "docBillingRecord.pricingTokens")
  public abstract ToKenReport toToKenReport(JobParseArg jobParseArg, DocBillingRecord docBillingRecord);
}
