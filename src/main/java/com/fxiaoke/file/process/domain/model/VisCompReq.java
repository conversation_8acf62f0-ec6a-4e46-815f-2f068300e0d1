package com.fxiaoke.file.process.domain.model;

import com.fxiaoke.file.process.domain.constants.VisCompDetail;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class VisCompReq<T> {

  /* 模型名称 */
  private String model;
  /* 模型识别精度 */
  private VisCompDetail detail;
  /* 图片URL地址 */
  private String imageUrl;

  /* 用户提示词 */
  private String userPrompt;
  /* 系统提示词 */
  private String systemPrompt;

  private T data;

  public static <T> VisCompReq<T> of(VisCompConfig visCompConfig, T data) {
    VisCompReq<T> visCompReq = new VisCompReq<>();
    visCompReq.setDetail(visCompConfig.getDetail());
    visCompReq.setModel(visCompConfig.getModel());
    visCompReq.setUserPrompt(visCompConfig.getUserPrompt());
    visCompReq.setSystemPrompt(visCompConfig.getSystemPrompt());
    visCompReq.setData(data);
    return visCompReq;
  }
}
