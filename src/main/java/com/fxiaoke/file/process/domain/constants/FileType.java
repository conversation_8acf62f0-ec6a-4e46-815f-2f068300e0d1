package com.fxiaoke.file.process.domain.constants;

import lombok.Getter;

@Getter
public enum FileType {

  DOC("doc"),
  DOCX("docx"),
  PPT("ppt"),
  PPTX("pptx"),
  XLS("xls"),
  XLSX("xlsx"),
  PDF("pdf"),

  OTHER("other"),

  JPG("jpg"),
  JPEG("jpeg"),
  PNG("png"),
  WEBP("webp"),
  BMP("bmp"),
  GIF("gif"),

  SVG("svg"),

  CSV("csv"),

  HTM("htm"),
  XML("xml"),
  HTML("html"),
  JSON("json"),
  TXT("txt"),
  TEXT("text"),
  MARKDOWN("md"),

  WAV("wav"),
  MP3("mp3"),
  M4A("m4a"),
  WMA("wma"),
  AMR("amr"),
  AAC("aac"),
  OPUS("opus"),
  FLAC("flac"),

  FLV("flv"),
  MP4("mp4"),
  THREE_GP("3gp"),

  MOV("mov");

  private final String fileType;

  FileType(String fileType) {
    this.fileType = fileType;
  }

  public static boolean isHtml(String sourceType) {
    return sourceType.equalsIgnoreCase("html");
  }

  public boolean isOffice(){
    return this.equals(PDF)
        || this.equals(DOC)
        || this.equals(DOCX)
        || this.equals(XLS)
        || this.equals(XLSX)
        || this.equals(PPT)
        || this.equals(PPTX);
  }

  public boolean isCells() {
    return this.equals(XLS)
        || this.equals(XLSX);
  }

  public boolean isXls() {
    return this.equals(XLS);
  }

  public boolean isWords() {
    return this.equals(DOC)
        || this.equals(DOCX);
  }

  public static boolean isWords(String fileType) {
    return fileType.equalsIgnoreCase("doc")
        || fileType.equalsIgnoreCase("docx");
  }

  public boolean isDoc() {
    return this.equals(DOC);
  }

  public boolean isSlides() {
    return this.equals(PPT)
        || this.equals(PPTX);
  }

  public static boolean isSlides(String fileType) {
    return fileType.equalsIgnoreCase("ppt")
        || fileType.equalsIgnoreCase("pptx");
  }

  public boolean isPpt() {
    return this.equals(PPT);
  }

  public boolean isPDF() {
    return this.equals(PDF);
  }

  public static boolean isPDF(String fileType) {
    return fileType.equalsIgnoreCase("pdf");
  }

  public boolean isImage() {
    return this.equals(JPG)
        || this.equals(JPEG)
        || this.equals(PNG)
        || this.equals(WEBP)
        || this.equals(BMP)
        || this.equals(GIF);
  }

  public static boolean isImage(String fileType) {
    return fileType.equalsIgnoreCase("jpg")
        || fileType.equalsIgnoreCase("jpeg")
        || fileType.equalsIgnoreCase("png")
        || fileType.equalsIgnoreCase("webp")
        || fileType.equalsIgnoreCase("bmp")
        || fileType.equalsIgnoreCase("gif");
  }

  public static boolean isMedia(String fileType){
    return isAudio(fileType)
        || isVideo(fileType);
  }

  public boolean isAudio(){
    return this.equals(WAV)
        || this.equals(MP3)
        || this.equals(M4A)
        || this.equals(WMA)
        || this.equals(AMR)
        || this.equals(AAC)
        || this.equals(OPUS)
        || this.equals(FLAC);
  }

  public static boolean isAudio(String fileType) {
    return fileType.equalsIgnoreCase("wav")
        || fileType.equalsIgnoreCase("mp3")
        || fileType.equalsIgnoreCase("m4a")
        || fileType.equalsIgnoreCase("wma")
        || fileType.equalsIgnoreCase("amr")
        || fileType.equalsIgnoreCase("aac")
        || fileType.equalsIgnoreCase("opus")
        || fileType.equalsIgnoreCase("flac");
  }

  public boolean isVideo() {
    return this.equals(FLV)
        || this.equals(MP4)
        || this.equals(THREE_GP)
        || this.equals(MOV);
  }

  public static boolean isVideo(String fileType) {
    return fileType.equalsIgnoreCase("flv")
        || fileType.equalsIgnoreCase("mp4")
        || fileType.equalsIgnoreCase("3gp")
        || fileType.equalsIgnoreCase("mov");
  }

  public boolean isMultimedia() {
    return this.equals(MP4)
        || this.equals(MOV)
        || this.equals(MP3);
  }

  public boolean isPlainText() {
    return this.equals(TXT)
        || this.equals(TEXT)
        || this.equals(HTML)
        || this.equals(HTM)
        || this.equals(XML)
        || this.equals(JSON)
        || this.equals(CSV);
  }

  public static FileType of(String fileTypeName) {
    for (FileType fileType : FileType.values()) {
      if (fileType.getFileType().equalsIgnoreCase(fileTypeName)) {
        return fileType;
      }
    }
    return OTHER;
  }
}
