package com.fxiaoke.file.process.domain.exception;

import java.util.Arrays;
import lombok.Getter;

/**
 * 基础异常 此异常必须在具体实现层被抛出 Service层被捕获并处理 不能抛出到Controller层
 */
@Getter
public class BaseException extends RuntimeException {

  private static final String MODULE_NAME = "module:";
  private static final String ERROR_REASON = "reason:";
  private static final String ARGS_NAME = "args:";
  private final int code;
  private final String message;
  private final String reason;

  public BaseException(int code, String message, String module, Object... args) {
    super(message);
    this.code = code;
    this.message = message;
    this.reason = MODULE_NAME + module + "-" +
        ERROR_REASON + message + "-" +
        ARGS_NAME + Arrays.toString(args);
  }

  public BaseException(Exception e, int code, String module, Object... args) {
    super(e.getMessage(), e);
    this.code = code;
    this.message = e.getMessage();
    this.reason = MODULE_NAME + module + "-" +
        ERROR_REASON + e.getMessage() + "-" +
        ARGS_NAME + Arrays.toString(args);
  }
}
