package com.fxiaoke.file.process.domain.api.request;

import com.fxiaoke.file.process.annotation.ValidConvertFormat;
import com.fxiaoke.file.process.utils.FilenameUtil;
import com.fxiaoke.file.process.utils.StrUtils;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ValidConvertFormat
public class JobSubmitArg {

  @NotBlank(message = "ea is required")
  private String ea;

  @NotNull(message = "employeeId is required")
  private Integer employeeId;

  @NotBlank
  @Pattern(regexp = "^(N_|TN_|C_|TC_|TA_|A_|G_|ALIOSS_).{0,48}$", message = "Invalid path")
  private String path;

  private String securityGroup;

  @NotBlank(message = "sourceType is required")
  private String sourceType;

  @NotBlank(message = "targetType is required")
  private String targetType;

  @NotBlank(message = "文件名不能为空")
  private String fileName;

  private boolean ocr;

  private String ocrModel;

  private int ignoreMinImagePixel;

  private boolean llmEnhancement;

  private String llmEnhancementModel;

  /**
   * 业务类型，对应消耗场景，需要<a href="https://wiki.firstshare.cn/x/do2iG">申请</a>
   */
  @NotBlank(message = "业务类型不能为空")
  private String business;

  /**
   * API名称，以__c结尾，业务侧定，范围比business小
   */
  private String apiName;

  /**
   * 显示名称，对应消耗源头
   */
  private String displayName;

  private String asyncCallbackMqTag;

  public String getPath() {
    if (StrUtils.notBlank(path)) {
      return FilenameUtil.getBaseName(path);
    }
    return path;
  }
}
