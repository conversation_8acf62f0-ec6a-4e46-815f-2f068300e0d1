package com.fxiaoke.file.process.domain.model;

import com.fxiaoke.file.process.domain.constants.AsyncTaskType;
import java.nio.file.Path;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class AsyncTaskReq {

  /**
   * 当前任务-任务ID
   * 注意：任务ID必须是字符串类型
   */
  private String jobId;

  /**
   * 企业账号
   * 注意：企业账号必须是字符串类型
   */
  private String ea;

  /**
   * 员工ID
   * 注意：员工ID必须是数字类型
   */
  private int employeeId;

  /**
   * 文件路径
   * 注意：文件路径必须是本地文件系统的绝对路径
   */
  private Path filePath;
  /**
   * 文件名
   * 注意：文件名不包含路径信息
   */
  private String fileName;

  /**
   * 使用的异步任务类型（便于后续拓展）
   */
  private AsyncTaskType taskType;
}
