package com.fxiaoke.file.process.domain.api.remote.mistral;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.nio.file.Path;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class MistralOcrRequest {

  // Getters and Setters
  private String model;
  private Document document;
  @JsonProperty("include_image_base64")
  private boolean includeImageBase64;

  private Integer[] pages;

  @Data
  @JsonInclude(JsonInclude.Include.NON_NULL)
  public static class Document {
    private String type;
    @JsonProperty("document_url")
    private String documentUrl;
    @JsonProperty("document_name")
    private String documentName;
  }

  @JsonProperty("image_limit")
  @JsonInclude(JsonInclude.Include.NON_EMPTY)
  private Integer imageLimit;

  @JsonProperty("image_min_size")
  @JsonInclude(JsonInclude.Include.NON_EMPTY)
  private Integer imageMinSize;

  @JsonIgnore
  private Path mdFilePath;
}
