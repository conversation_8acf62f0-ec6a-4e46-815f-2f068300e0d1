package com.fxiaoke.file.process.domain.api.request.model;

import com.google.gson.annotations.SerializedName;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 关键字识别结果
 * 被如下接口引用：DescribeTaskStatus。
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class KeyWordResult {

  /**
   * 关键词库ID
   * 注意：此字段可能返回 null，表示取不到有效值。
   * 示例值：abcdedfh
   */
  @SerializedName("KeyWordLibID")
  private String keyWordLibID;

  /**
   * 关键词库名称
   * 注意：此字段可能返回 null，表示取不到有效值。
   * 示例值：测试词库
   */
  @SerializedName("KeyWordLibName")
  private String keyWordLibName;

  /**
   * 匹配到的关键词
   * 注意：此字段可能返回 null，表示取不到有效值。
   * 示例值：["冰淇淋","月亮","六便士"]
   */
  @SerializedName("KeyWords")
  private List<String> keyWords;
}
