package com.fxiaoke.file.process.domain.model;

import com.fxiaoke.file.process.domain.constants.AsyncTaskType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class AsyncTaskRes {

  /**
   * 当前任务-任务ID
   */
  private String jobId;

  /**
   * 企业账号
   */
  private String ea;

  /**
   * 员工ID
   */
  private int employeeId;

  /**
   * 异步任务类型
   */
  private AsyncTaskType taskType;

  /**
   * 第三方请求ID
   */
  private String requestId;

  /**
   * 第三方任务ID
   */
  private String taskId;
}
