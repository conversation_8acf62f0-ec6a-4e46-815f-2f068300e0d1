package com.fxiaoke.file.process.domain.entity.fieids;

public class DocParseJobFieIds {

  private DocParseJobFieIds(){}

  public static final String jobId = "_id";

  public static final String uniqueIds = "uniqueIds";

  public static final String jobRecords = "jobRecords";
  public static final String jobStatus = "jobStatus" ;
  public static final String jobSubmitTime = "jobSubmitTime";
  public static final String jobProcessStartTime = "jobProcessStartTime";
  public static final String jobProcessEndTime = "jobProcessEndTime";
  public static final String jobImgProcessCostTime = "jobImgProcessCostTime";
  public static final String jobCompletionTime = "jobCompletionTime";
  public static final String billingReportTime = "billingReportTime";
  public static final String jobFailTime = "jobFailTime";
  public static final String jobFailCode = "jobFailCode";
  public static final String jobFailReason =  "jobFailReason";

  public static final String jobParseArg = "jobParseArg";
  public static final String ea = "ea";
  public static final String employeeId = "employeeId";
  public static final String path = "path";
  public static final String securityGroup = "securityGroup";
  public static final String sourceType = "sourceType";
  public static final String targetType = "targetType";

  public static final String fileName = "fileName";

  public static final String imageStrategy = "imageStrategy";

  public static final String ocr = "ocr";
  public static final String ocrModel = "ocrModel";
  public static final String ocrSystemPrompt = "ocrSystemPrompt";
  public static final String ocrUserPrompt = "ocrUserPrompt";
  public static final String ignoreMinImagePixel = "ignoreMinImagePixel";

  public static final String llmEnhancement = "llmEnhancement";
  public static final String llmEnhancementModel = "llmEnhancementModel";
  public static final String llmEnhancementSystemPrompt = "llmEnhancementSystemPrompt";
  public static final String llmEnhancementUserPrompt = "llmEnhancementUserPrompt";

  public static final String business = "business";
  public static final String displayName = "displayName";
  public static final String apiName = "apiName";

  public static final String asyncCallbackMqTag = "asyncCallbackMqTag";

  public static final String fileID = "fileID";

  public static final String docBillingRecord = "docBillingRecord";
  public static final String pageCount = "pageCount";
  public static final String duration = "duration";
  public static final String fileSize = "fileSize";
  public static final String imageCount = "imageCount";
  public static final String modelName = "modelName";
  public static final String totalInputTokens = "totalInputTokens";
  public static final String totalOutputTokens = "totalOutputTokens";
  public static final String totalTokens = "totalTokens";
  public static final String pricingTokens = "pricingTokens";

  public static final String billingReportDone = "billingReportDone";

  public static final String idempotent = "idempotent";
  public static final String idempotentJobId = "idempotentJobId";

  public static final String uniqueIds_index_name = "uniqueIds_1";
  public static final String uniqueIds_jobStatus_index_name = "uniqueIds_1_jobStatus_1";

}
