package com.fxiaoke.file.process.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 视觉模型名称和提示词信息及图片信息
 */
@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class VlRecognizeParams {

  /* 模型名称 */
  private String model;
  /* 用户提示词 */
  private String userPrompt;
  /* 系统提示词 */
  private String systemPrompt;

  /* 图片信息 */
  private MarkdownImgInfo markdownImgInfo;

  public static VlRecognizeParams of(String model, String userPrompt, String systemPrompt, MarkdownImgInfo markdownImgInfo) {
    VlRecognizeParams vlRecognizeParams = new VlRecognizeParams();
    vlRecognizeParams.model = model;
    vlRecognizeParams.userPrompt = userPrompt;
    vlRecognizeParams.systemPrompt = systemPrompt;
    vlRecognizeParams.markdownImgInfo = markdownImgInfo;
    return vlRecognizeParams;
  }
}
