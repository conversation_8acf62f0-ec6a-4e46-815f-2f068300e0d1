package com.fxiaoke.file.process.service;

import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.model.FileDownloadParams;
import com.fxiaoke.file.process.domain.model.FileDownloadResult;
import com.fxiaoke.file.process.domain.model.FileMetaParams;
import com.fxiaoke.file.process.domain.model.FileUploadParams;
import com.fxiaoke.file.process.domain.model.FileUploadResult;
import com.fxiaoke.file.process.domain.model.MarkdownImgInfo;
import com.fxiaoke.file.process.domain.model.VisCompReq;
import java.io.InputStream;
import java.nio.file.Path;
import java.util.List;
import java.util.Optional;

public interface FileService {

  long getTotalFileSize(List<FileMetaParams> paramsList) throws BaseException;

  List<FileUploadResult> uploadFiles(List<FileUploadParams> params) throws BaseException;

  String uploadLocalFileToS3(String bucketRootDir, Path filePath);

  String getS3PreSignUrl(String objectKey, String process);

  String getPreSignUrl(String bucketRootDir,Path filePath);

  List<VisCompReq<MarkdownImgInfo>> localMdFileToS3PreSignUrl(List<VisCompReq<MarkdownImgInfo>> visCompReqs);

  Optional<String> downloadNFile(String ea, int employeeId, String path, String saveDir);

  FileDownloadResult downloadFileToLocal(FileDownloadParams params) throws BaseException;

  List<FileDownloadResult> downloadFilesToLocal(
      List<FileDownloadParams> params) throws BaseException;

  long getFileSize(FileMetaParams params);

  FileUploadResult uploadFile(FileUploadParams params) throws BaseException;

  String saveToGridFs(Path filePath,String fileName) throws BaseException;

  String saveToGridFs(String content, String fileName, String extensions);

  InputStream getFileByGridFs(String fileId);

  // 将图片上传到对象存储并获取预签名URL
  Optional<VisCompReq<MarkdownImgInfo>> uploadAndGeneratePreSignUrl(
      VisCompReq<MarkdownImgInfo> visCompReq);
}
