package com.fxiaoke.file.process.service.impl;

import com.facishare.fsi.proxy.exception.FsiClientException;
import com.facishare.fsi.proxy.model.warehouse.a.ADownloadFile;
import com.facishare.fsi.proxy.model.warehouse.a.AGetFileMetaData;
import com.facishare.fsi.proxy.model.warehouse.a.User;
import com.facishare.fsi.proxy.model.warehouse.g.GFileDownload;
import com.facishare.fsi.proxy.service.AFileStorageService;
import com.facishare.fsi.proxy.service.GFileStorageService;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileDownloadRequest;
import com.facishare.stone.sdk.request.StoneFileGetMetaDataRequest;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.facishare.stone.sdk.response.StoneFileGetFileMetaResponse;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.fxiaoke.file.process.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.model.FileDownloadParams;
import com.fxiaoke.file.process.domain.model.FileDownloadResult;
import com.fxiaoke.file.process.domain.model.FileMetaParams;
import com.fxiaoke.file.process.domain.model.FileUploadParams;
import com.fxiaoke.file.process.domain.model.FileUploadResult;
import com.fxiaoke.file.process.domain.model.MarkdownImgInfo;
import com.fxiaoke.file.process.domain.model.VisCompReq;
import com.fxiaoke.file.process.manager.ThreadManager;
import com.fxiaoke.file.process.service.FileService;
import com.fxiaoke.file.process.service.GridFsStore;
import com.fxiaoke.file.process.service.S3Service;
import com.fxiaoke.file.process.utils.FileOperationUtil;
import com.fxiaoke.file.process.utils.FileServerUtil;
import com.fxiaoke.file.process.utils.FileTypeUtil;
import com.fxiaoke.file.process.utils.FilenameUtil;
import com.fxiaoke.file.process.utils.SampleUUID;
import com.fxiaoke.file.process.utils.StrUtils;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j(topic = "FileServiceImpl")
public class FileServiceImpl implements FileService {

  private static final String MODULE = "FileServiceImpl";

  private final S3Service s3Service;
  private final StoneProxyApi nFileApi;
  private final GridFsStore gridFsStore;
  private final AFileStorageService aFileApi;
  private final GFileStorageService gFileApi;
  private final CmsPropertiesConfig config;

  private final  ThreadManager threadManager;

  public FileServiceImpl(S3Service s3Service, StoneProxyApi stoneProxyApi, GridFsStore gridFsStore,
      GFileStorageService gFileStorageService, AFileStorageService aFileStorageService,
      CmsPropertiesConfig cmsPropertiesConfig,ThreadManager threadManager) {
    this.s3Service = s3Service;
    this.nFileApi = stoneProxyApi;
    this.gridFsStore = gridFsStore;
    this.aFileApi = aFileStorageService;
    this.gFileApi = gFileStorageService;
    this.config = cmsPropertiesConfig;
    this.threadManager = threadManager;
  }

  @Override
  public long getTotalFileSize(List<FileMetaParams> paramsList) throws BaseException {
    long totalSize = 0;
    for (FileMetaParams params : paramsList) {
      totalSize += getFileSize(params);
    }
    return totalSize;
  }

  @Override
  public List<FileUploadResult> uploadFiles(List<FileUploadParams> params) throws BaseException {
    List<FileUploadResult> fileUploadResults = new ArrayList<>();
    for (FileUploadParams param : params) {
      FileUploadResult fileUploadResult = uploadFile(param);
      fileUploadResults.add(fileUploadResult);
    }
    return fileUploadResults;
  }

  @Override
  public String uploadLocalFileToS3(String bucketRootDir, Path filePath) {
    log.debug("uploadLocalFileToS3 bucketRootDir:{},filePath:{}", bucketRootDir, filePath);
    String objectKey = s3Service.uploadToS3(bucketRootDir, filePath);
    log.debug("uploadLocalFileToS3 objectKey:{}", objectKey);
    return objectKey;
  }

  @Override
  public String getS3PreSignUrl(String objectKey, String process) {
    log.debug("getS3PreSignUrl objectKey:{},process:{}", objectKey, process);
    String preSignUrl = s3Service.getPreSignUrl(objectKey, process);
    log.debug("getS3PreSignUrl preSignUrl:{}", preSignUrl);
    return preSignUrl;
  }

  @Override
  public String getPreSignUrl(String bucketRootDir, Path filePath) {
    log.debug("getPreSignUrl bucketRootDir:{},filePath:{}", bucketRootDir, filePath);
    String objectKey = s3Service.uploadToS3(bucketRootDir, filePath);
    log.debug("getPreSignUrl objectKey:{}", objectKey);
    String preSignUrl = s3Service.getPreSignUrl(objectKey, "");
    log.debug("getPreSignUrl preSignUrl:{}", preSignUrl);
    return preSignUrl;
  }

  @Override
  public List<VisCompReq<MarkdownImgInfo>> localMdFileToS3PreSignUrl(
      List<VisCompReq<MarkdownImgInfo>> visCompReqs) {
    if (visCompReqs == null || visCompReqs.isEmpty()) {
      return new ArrayList<>();
    }

    int visCompCount = visCompReqs.size();
    log.info("S3 Batch Process Start,visCompCount:{}", visCompCount);
    // 根据最大并发数和任务总数计算批次数
    int batchSize = calculateOptimalBatchSize(visCompCount);

    // 根据批次大小将任务分割成多个批次
    List<List<VisCompReq<MarkdownImgInfo>>> taskList = splitIntoBatches(visCompReqs, batchSize);

    // 为每个批次创建一个 CompletableFuture 列表
    List<List<CompletableFuture<Optional<VisCompReq<MarkdownImgInfo>>>>> futures = new ArrayList<>();
    for (List<VisCompReq<MarkdownImgInfo>> batch : taskList) {
      List<CompletableFuture<Optional<VisCompReq<MarkdownImgInfo>>>> batchTask = createS3UploadAsyncTasks(
          batch);
      futures.add(batchTask);
    }

    // 分批处理所有任务
    for (List<CompletableFuture<Optional<VisCompReq<MarkdownImgInfo>>>> batchFutures : futures) {
      // 等待所有任务完成，无论成功失败
      for (CompletableFuture<Optional<VisCompReq<MarkdownImgInfo>>> future : batchFutures) {
        try {
          future.get(config.getS3TimeoutSeconds(), TimeUnit.SECONDS);
        } catch (Exception e) {
          // 不取消其他任务，让它们继续执行
          log.warn("S3 Batch Process, File UploadFail Error,time:{}", config.getS3TimeoutSeconds(),
              e);
        }
      }
    }

    // 安全收集所有批次的成功结果
    List<VisCompReq<MarkdownImgInfo>> results = new ArrayList<>();

    for (List<CompletableFuture<Optional<VisCompReq<MarkdownImgInfo>>>> batchFutures : futures) {
      for (CompletableFuture<Optional<VisCompReq<MarkdownImgInfo>>> future : batchFutures) {
        try {
          Optional<VisCompReq<MarkdownImgInfo>> result = future.get();
          result.ifPresent(results::add);
        } catch (Exception e) {
          log.warn("S3 Batch Process, Get FutureResult Fail {}", e.getMessage());
        }
      }
    }

    log.info("S3 Batch Process End,visCompCount:{},results size: {}", visCompCount, results.size());
    return results;
  }

  @Override
  public Optional<String> downloadNFile(String ea, int employeeId, String path, String saveDir) {
    try {
      Optional<String> typeOpt = findNFileType(ea, path);
      if (typeOpt.isPresent()) {
        String ext = typeOpt.get();
        String filename = FilenameUtil.getBaseName(path).concat(".").concat(ext);
        FileDownloadParams params = new FileDownloadParams(ea, employeeId, path, "", filename, ext);
        Path tempPath = Paths.get(saveDir, filename);
        FileOperationUtil.saveInputStream(downloadNFile(params), tempPath);
        return Optional.of(tempPath.toString());
      }
    } catch (Exception e) {
      log.warn("downloadNFile error:{},ea:{},employeeId:{},path:{}", e.getMessage(), ea, employeeId,
          path);
    }
    return Optional.empty();
  }

  @Override
  public FileDownloadResult downloadFileToLocal(FileDownloadParams params) throws BaseException {
    try {
      Path tempPath = getTempPath(params.path(), params.ext());
      if (FileServerUtil.isGFile(params.path())) {
        byte[] gFile = downloadGFile(params);
        FileOperationUtil.saveBytes(gFile, tempPath);

      } else if (FileServerUtil.isAFile(params.path())) {
        byte[] aFile = downloadAFile(params);
        FileOperationUtil.saveBytes(aFile, tempPath);
      } else if (FileServerUtil.isBigFile(params.path())) {
        FileOperationUtil.saveInputStream(downloadBigFile(params), tempPath);
      } else {
        FileOperationUtil.saveInputStream(downloadNFile(params), tempPath);
      }
      return new FileDownloadResult(params.filename(), tempPath);
    } catch (Exception e) {
      throw new BaseException(e, 400, MODULE + ".downloadFileToLocal", params);
    }
  }

  @Override
  public List<FileDownloadResult> downloadFilesToLocal(List<FileDownloadParams> params)
      throws BaseException {
    List<FileDownloadResult> fileDownloadResults = new ArrayList<>();
    for (FileDownloadParams param : params) {
      FileDownloadResult fileDownloadResult = downloadFileToLocal(param);
      fileDownloadResults.add(fileDownloadResult);
    }
    return fileDownloadResults;
  }

  @Override
  public long getFileSize(FileMetaParams params) {
    try {
      if (FileServerUtil.isGFile(params.path())) {
        return getGFileSize(params.employeeId(), params.path(), params.sg());
      } else if (FileServerUtil.isAFile(params.path())) {
        return getAFileSize(params.ea(), params.employeeId(), params.path(), params.sg());
      } else if (FileServerUtil.isBigFile(params.path())) {
        return getBigFileSize(params.ea(), params.path());
      } else {
        return getNFileSize(params.ea(), params.employeeId(), params.path());
      }
    } catch (BaseException e) {
      throw e;
    } catch (Exception e) {
      throw new BaseException(e, 400, MODULE + ".getFileSize", params);
    }
  }

  @Override
  public FileUploadResult uploadFile(FileUploadParams params) throws BaseException {
    log.info("upload file start, params:{}", params);
    try (InputStream stream = Files.newInputStream(params.getFilePath())) {
      StoneFileUploadRequest request = new StoneFileUploadRequest();
      request.setEa(params.getEa());
      request.setEmployeeId(params.getEmployeeId());
      request.setNeedCdn(false);
      request.setNeedThumbnail(false);
      request.setBusiness(Constants.BUSINESS);
      request.setOriginName(params.getFileName());
      request.setExtensionName(params.getExtension());
      request.setFileSize((int) Files.size(params.getFilePath()));
      StoneFileUploadResponse fileUploadResponse = nFileApi.tempFileUploadByStream("n", request,
          stream);
      FileUploadResult fileUploadResult = new FileUploadResult(fileUploadResponse.getPath(),
          params.getFileName(), fileUploadResponse.getExtensionName(),
          fileUploadResponse.getSize());
      log.info("upload file end, result:{}", fileUploadResult);
      return fileUploadResult;
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".uploadFile", params);
    }
  }

  @Override
  public String saveToGridFs(Path filePath, String fileName) throws BaseException {
    String fileExtension = FilenameUtil.getFileExtension(filePath);
    return gridFsStore.saveFile(filePath, fileName, fileExtension);
  }

  @Override
  public String saveToGridFs(String content, String fileName, String extensions) {
    return gridFsStore.saveFile(content, fileName, extensions);
  }

  @Override
  public InputStream getFileByGridFs(String fileId) {
    return gridFsStore.getFileById(fileId);
  }

  /**
   * Get size for N_ or TN_ files
   */
  private long getNFileSize(String ea, int employeeId, String path) throws FRestClientException {
    StoneFileGetMetaDataRequest request = new StoneFileGetMetaDataRequest();
    request.setEa(ea);
    request.setEmployeeId(employeeId);
    request.setPath(path);
    request.setBusiness(Constants.BUSINESS);
    request.setSecurityGroup(Constants.DEFAULT_SECURITY_GROUP);
    StoneFileGetFileMetaResponse fileMetaData = nFileApi.getFileMetaData(request);
    return fileMetaData.getSize();
  }

  /**
   * Get size for G_ files Note: This method downloads the entire file to get its size
   */
  private long getGFileSize(int employeeId, String path, String securityGroup)
      throws FsiClientException {
    byte[] fileData = downloadGFile(new FileDownloadParams(employeeId, path, securityGroup, ""));
    return fileData.length;
  }

  /**
   * Get size for A_ or TA_ files
   */
  private long getAFileSize(String ea, int employeeId, String path, String securityGroup)
      throws FsiClientException {
    AGetFileMetaData.Arg arg = new AGetFileMetaData.Arg();
    arg.setFileName(path);
    arg.setBusiness("Preview");
    arg.setFileSecurityGroup(securityGroup);
    User user = new User(ea, employeeId);
    arg.setUser(user);
    return aFileApi.getFileMetaData(arg).getSize();
  }

  private long getBigFileSize(String ea, String path) {
    return s3Service.getBigFileSize(ea, path);
  }

  /**
   * 生成临时文件路径
   *
   * @param name 文件唯一标识ID
   * @param ext  文件扩展名
   * @return 临时文件路径
   */
  private Path getTempPath(String name, String ext) {
    String filename = name.concat(".").concat(ext);
    return Paths.get(config.getLocalCacheDirPath(), SampleUUID.getUUID(), filename);
  }

  /**
   * 下载N文件（隶属于企业文件服务）
   *
   * @param params 文件下载参数 {@link FileDownloadParams}
   * @return 临时文件路径
   */
  private InputStream downloadNFile(FileDownloadParams params) throws FRestClientException {
    StoneFileDownloadRequest request = new StoneFileDownloadRequest();
    request.setEa(params.ea());
    request.setPath(params.path());
    request.setBusiness(Constants.BUSINESS);
    request.setEmployeeId(params.employeeId());
    request.setSecurityGroup(params.sg());
    return nFileApi.downloadStream(request);
  }

  private Optional<String> findNFileType(String ea, String path) throws FRestClientException {
    StoneFileGetMetaDataRequest request = new StoneFileGetMetaDataRequest();
    request.setEa(ea);
    request.setPath(path);
    StoneFileGetFileMetaResponse fileMetaData = nFileApi.getFileMetaData(request);

    String mimeType = fileMetaData.getMimeType();

    if (StrUtils.notBlank(mimeType) && FileTypeUtil.isSupportImageByMimeType(mimeType)) {
      return Optional.of(FileTypeUtil.getExtensionByMimeType(mimeType));
    }

    String extensionName = fileMetaData.getExtensionName();
    if (StrUtils.notBlank(extensionName) && FileTypeUtil.isSupportImageByExtension(extensionName)) {
      return Optional.of(extensionName);
    }
    return Optional.empty();
  }

  /**
   * 下载A文件（隶属于互联文件服务）
   *
   * @param params 文件下载参数 {@link FileDownloadParams}
   */
  private byte[] downloadAFile(FileDownloadParams params) throws FsiClientException {
    try {
      ADownloadFile.Arg arg = new ADownloadFile.Arg();
      arg.setaPath(params.path());
      arg.setBusiness(Constants.BUSINESS);
      arg.setFileSecurityGroup(params.sg());
      User user = new User(params.ea(), params.employeeId());
      arg.setUser(user);
      return aFileApi.downloadFile(arg).getData();
    } catch (FsiClientException e) {
      throw new BaseException(e, 400, MODULE + ".downloadAFile", params);
    }
  }

  /**
   * 下载G文件（隶属于全局文件服务）
   *
   * @param params 文件下载参数 {@link FileDownloadParams}
   */
  private byte[] downloadGFile(FileDownloadParams params) throws FsiClientException {
    GFileDownload.Arg arg = new GFileDownload.Arg();
    arg.gPath = params.path();
    arg.downloadUser = "E." + params.employeeId();
    arg.downloadSecurityGroup = params.sg();
    return gFileApi.downloadFile(arg).data;
  }

  private InputStream downloadBigFile(FileDownloadParams params) throws FsiClientException {
    return s3Service.downloadBigFile(params.ea(), params.path());
  }

  private int calculateOptimalBatchSize(int totalTasks) {
    int configuredBatchSize = config.getS3BatchSize();
    // 如果任务总数小于配置的批处理大小，直接使用任务总数
    return Math.min(totalTasks, configuredBatchSize);
  }

  private List<List<VisCompReq<MarkdownImgInfo>>> splitIntoBatches(
      List<VisCompReq<MarkdownImgInfo>> visCompReqs, int batchSize) {
    int taskCount = visCompReqs.size();
    List<List<VisCompReq<MarkdownImgInfo>>> batches = new ArrayList<>();
    for (int i = 0; i < taskCount; i += batchSize) {
      int end = Math.min(i + batchSize, taskCount);
      batches.add(visCompReqs.subList(i, end));
    }
    return batches;
  }

  @Override
  public Optional<VisCompReq<MarkdownImgInfo>> uploadAndGeneratePreSignUrl(
      VisCompReq<MarkdownImgInfo> visCompReq) {
    try {
      MarkdownImgInfo info = visCompReq.getData();
      String objectKey = s3Service.uploadToS3(Constants.S3_BUCKET_ROOT_DIR_VL_OCR,
          info.getImagePath());
      String s3PreSignUrl = s3Service.getPreSignUrl(objectKey, "");
      visCompReq.setImageUrl(s3PreSignUrl);
      log.debug("S3 UploadAndSign End, Index: {} , FilePath: {}", info.getIndex(),
          info.getImagePath());
      return Optional.of(visCompReq);
    } catch (Exception e) {
      log.error("S3 UploadAndSign Fail, {}", visCompReq, e);
      return Optional.empty();
    }
  }

  private CompletableFuture<Optional<VisCompReq<MarkdownImgInfo>>> createS3UploadAsyncTask(
      VisCompReq<MarkdownImgInfo> visCompReq) {
    return CompletableFuture.supplyAsync(() -> uploadAndGeneratePreSignUrl(visCompReq), threadManager.getExecutor());
  }

  private List<CompletableFuture<Optional<VisCompReq<MarkdownImgInfo>>>> createS3UploadAsyncTasks(
      List<VisCompReq<MarkdownImgInfo>> visCompReqs) {
    List<CompletableFuture<Optional<VisCompReq<MarkdownImgInfo>>>> futures = new ArrayList<>();
    for (VisCompReq<MarkdownImgInfo> visCompReq : visCompReqs) {
      CompletableFuture<Optional<VisCompReq<MarkdownImgInfo>>> future = createS3UploadAsyncTask(
          visCompReq);
      futures.add(future);
    }
    return futures;
  }
}
