package com.fxiaoke.file.process.service.impl;

import com.fxiaoke.file.process.dao.AsyncPollingTaskDao;
import com.fxiaoke.file.process.dao.DocParseJobDao;
import com.fxiaoke.file.process.domain.api.request.BatchDocToMdArg;
import com.fxiaoke.file.process.domain.api.request.DocRanksStreamArg;
import com.fxiaoke.file.process.domain.api.request.JobGetContent;
import com.fxiaoke.file.process.domain.api.response.BatchDocToMdResult;
import com.fxiaoke.file.process.domain.api.response.model.MarkdownInfo;
import com.fxiaoke.file.process.domain.constants.AsyncTaskType;
import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.domain.constants.FileType;
import com.fxiaoke.file.process.domain.constants.JobStatus;
import com.fxiaoke.file.process.domain.entity.DocParseJob;
import com.fxiaoke.file.process.domain.entity.model.DocBillingRecord;
import com.fxiaoke.file.process.domain.entity.model.JobParseArg;
import com.fxiaoke.file.process.domain.entity.model.JobRecords;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.mapper.CommonMapper;
import com.fxiaoke.file.process.domain.model.AsyncTaskReq;
import com.fxiaoke.file.process.domain.model.AsyncTaskRes;
import com.fxiaoke.file.process.domain.model.FileDownloadResult;
import com.fxiaoke.file.process.domain.model.FileUploadParams;
import com.fxiaoke.file.process.domain.model.FileUploadResult;
import com.fxiaoke.file.process.domain.model.ToKenReport;
import com.fxiaoke.file.process.domain.model.ToMdParams;
import com.fxiaoke.file.process.domain.model.ToMdResult;
import com.fxiaoke.file.process.domain.mq.DocParseCompletedMsg;
import com.fxiaoke.file.process.manager.ModelPriceManager;
import com.fxiaoke.file.process.mq.JobProducer;
import com.fxiaoke.file.process.service.CommonConvertService;
import com.fxiaoke.file.process.service.FileService;
import com.fxiaoke.file.process.service.TokenUsagesService;
import com.fxiaoke.file.process.utils.CodingUtils;
import com.fxiaoke.file.process.utils.FileOperationUtil;
import com.fxiaoke.file.process.utils.StrUtils;
import com.github.trace.TraceContext;
import java.io.BufferedWriter;
import java.io.InputStream;
import java.nio.file.Path;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j(topic = "DocParseServiceImpl")
public class DocParseServiceImpl extends ParseServiceImpl {

  private static final String MODULE = "DocParseServiceImpl";

  private final FileService fileService;

  private final TokenUsagesService tokenUsagesService;
  private final CommonConvertService commonConvertService;

  private final CommonMapper commonMapper;

  protected DocParseServiceImpl(JobProducer jobProducer, DocParseJobDao docParseJobDao,
      AsyncPollingTaskDao asyncPollingTaskDao,
      ModelPriceManager modelPriceManager, FileService fileService,
      TokenUsagesService tokenUsagesService,
      CommonConvertService commonConvertService, CommonMapper commonMapper) {
    super(jobProducer, docParseJobDao,asyncPollingTaskDao, modelPriceManager);
    this.fileService = fileService;
    this.tokenUsagesService = tokenUsagesService;
    this.commonConvertService = commonConvertService;
    this.commonMapper = commonMapper;
  }

  @Override
  public void ranksStream(DocRanksStreamArg arg, BufferedWriter writer) {
    // 下载文件到本地
    FileDownloadResult fileDownloadResult = fileService.downloadFileToLocal(
        arg.getFileDownloadParams());
    Path filePath = fileDownloadResult.filePath();

    // 按行列方式读取Excel文件内容到流
    commonConvertService.ranksStream(filePath, arg.isRowRead(), writer);
  }

  @Override
  public BatchDocToMdResult batchDocToMd(BatchDocToMdArg arg) {
    log.info("batchDocToMd start, params:{}", arg);

    // 校验文件大小
    long totalFileSize = fileService.getTotalFileSize(arg.getFileMetaParams());
    CodingUtils.checkBatchFileSize(totalFileSize, Constants.BATCH_TO_MD_MAX_SIZE, MODULE);

    // 批量下载文件到本地
    List<FileDownloadResult> downloadResults = fileService.downloadFilesToLocal(
        arg.getFileDownloadParams());

    // 转换为ToMdParams并批量转换文件为Markdown
    List<ToMdParams> toMdParams = toToMdParams(downloadResults, arg);
    List<ToMdResult> results = commonConvertService.batchDocToMd(toMdParams);

    // 准备上传参数并上传文件
    List<FileUploadParams> fileUploadParams = toFileUploadParams(arg.getEa(),
        arg.getEmployeeId(), results);
    List<FileUploadResult> fileUploadResults = fileService.uploadFiles(fileUploadParams);
    // 转换为API响应格式
    List<MarkdownInfo> markdownInfos = toMarkdownInfos(fileUploadResults);

    log.info("batchDocToMd end: result:{}", markdownInfos);
    return new BatchDocToMdResult(markdownInfos, TraceContext.get().getTraceId());
  }

  @Override
  public String submitJob(JobParseArg arg) {

    // 验证是否还有算粒配额
    boolean allowUsing = tokenUsagesService.allowUsing(arg.getEa(), arg.getEmployeeId());
    if (!allowUsing) {
      throw new BaseException(403,
          "ai usage permission is not opened or token usage limit exceeded", MODULE + ".submitJob",
          arg);
    }

    log.info("submitJob start, arg:{}", arg);
    // 幂等性处理（自动创建一个与原始任务参数相同的任务）
    String asyncCallbackMqTag = arg.getAsyncCallbackMqTag();

    Optional<DocParseJob> jobIdOption = docParseJobDao.idempotentJob(arg.getUniqueIds(),asyncCallbackMqTag);
    if (jobIdOption.isPresent()) {
      DocParseJob docParseJob = jobIdOption.get();
      String jobId = docParseJob.get_id().toString();
      JobStatus jobStatus = JobStatus.of(docParseJob.getJobStatus());
      // 成功、或失败的任务、根据有无异步回调tag与状态发送延迟通知消息
      if (jobStatus == JobStatus.COMPLETED || jobStatus == JobStatus.FAILED) {
        sendDelayNotificationMsg(docParseJob);
      }else {
        // 队列中、处理中直接返回幂等复制的任务ID(原始任务完成后会发送通知消息，将复制的幂等任务唤醒)
        log.info("idempotent job created, jobId:{}, status:{}, wake-up waiting", jobId, jobStatus);
      }
      return jobId;
    }

    // 创建任务
    String jobId = docParseJobDao.create(arg);
    log.info("submitJob create job, jobId:{}", jobId);

    // 根据文件类型不同，发不同的Tag以便分类处理
    String jobTag = getJobTag(arg.getSourceType());
    // 发送任务消息
    jobProducer.sendJobMsg(jobId,jobTag);
    log.info("submitJob end, jobId:{}", jobId);
    return jobId;
  }

  private String getJobTag(String sourceType) {
    FileType fileType = FileType.of(sourceType);
    if (fileType.isAudio()) {
      return Constants.MQ_TAG_DOC_ASYNC_TASK;
    } else if (fileType.isVideo()) {
      return Constants.MQ_TAG_DOC_ASYNC_TASK;
    } else {
      return Constants.MQ_TAG_DOC_PARSE_JOB_SUBMIT;
    }
  }

  @Override
  public void processJob(DocParseJob docParseJob) {
    // 获取任务ID和参数
    String jobId = docParseJob.get_id().toString();
    JobParseArg job = docParseJob.getJobParseArg();
    try {
      // 更新任务状态为开始处理
      log.info("processJob start, jobId:{}", jobId);
      docParseJobDao.process(jobId);

      // 获取文件并校验文件大小
      long fileSize = fileService.getFileSize(job.getFileMetaParams());
      CodingUtils.checkSingleFileSizeByType(fileSize, job.getSourceType(),
          Constants.SINGLE_TO_MD_MAX_SIZE, MODULE);
      log.info("processJob get fileSize: {}, jobId:{}", fileSize, jobId);

      // 下载文件到本地
      FileDownloadResult downloadResult = fileService.downloadFileToLocal(
          job.getFileDownloadParams());
      log.info("processJob file downloaded, jobId:{}, filePath:{}", jobId,
          downloadResult.filePath());

      // 转换为ToMdParams并转换文件为Markdown
      ToMdParams toMdParams = toToMdParams(jobId,downloadResult, job);
      ToMdResult toMdResult = commonConvertService.docToMd(toMdParams);
      log.info("processJob file converted to markdown, jobId:{}, result:{}", jobId, toMdResult);

      // 保存文件到GridFS
      String fileId = fileService.saveToGridFs(toMdResult.getFilePath(), toMdResult.getFileName());
      log.info("processJob file saved to GridFS, jobId:{}, fileId:{}", jobId, fileId);

      // 更新任务状态为完成并保存账单信息
      DocBillingRecord docBillingRecord = convertToDocBillingRecord(toMdResult, fileSize);
      docParseJobDao.complete(jobId, fileId, docBillingRecord);
      log.info("processJob job completed, jobId:{}, fileId:{}, billingRecord:{}", jobId, fileId,
          docBillingRecord);

      // 发送完成消息(如果tag不空,同时上报账单信息-利用MQ重试确保可靠性)
      sendCompletedNotification(job.getEa(), jobId, job.getAsyncCallbackMqTag());

      // 清理文件(清理父文件夹)
      FileOperationUtil.deleteFileOrDir(toMdResult.getClearPath());
    } catch (Exception e) {
      // 失败不会清理文件，需要配置服务定时重启以清理cache
      failJob(docParseJob, e);
    }
  }

  @Override
  public boolean subTask(DocParseJob docParseJob) {
    // 获取任务ID和参数
    String jobId = docParseJob.get_id().toString();
    JobParseArg job = docParseJob.getJobParseArg();
    try{
      // 更新任务状态为开始处理
      log.info("AsyncTask start, jobId:{}", jobId);
      docParseJobDao.process(jobId);

      // 获取文件并校验文件大小
      long fileSize = fileService.getFileSize(job.getFileMetaParams());
      CodingUtils.checkSingleFileSizeByType(fileSize, job.getSourceType(),
          Constants.SINGLE_TO_MD_MAX_SIZE, MODULE);
      log.info("AsyncTask get fileSize: {}, jobId:{}", fileSize, jobId);

      // 下载文件到本地
      FileDownloadResult downloadResult = fileService.downloadFileToLocal(
          job.getFileDownloadParams());
      log.info("AsyncTask file downloaded, jobId:{}, filePath:{}", jobId,
          downloadResult.filePath());

      AsyncTaskReq asyncTaskReq = toAsyncTaskReq(jobId,job, downloadResult);
      AsyncTaskRes asyncTaskRes = commonConvertService.subTask(asyncTaskReq);
      log.info("AsyncTask subTask created, jobId:{}, taskId:{}, requestId:{}", jobId,
          asyncTaskRes.getTaskId(), asyncTaskRes.getRequestId());
      return true;
    }catch (Exception e) {
      // 失败不会清理文件，需要配置服务定时重启以清理cache
      failJob(docParseJob, e);
    }
    return  false;
  }

  private AsyncTaskReq toAsyncTaskReq(String jobId,JobParseArg job,FileDownloadResult downloadResult) {
    AsyncTaskReq asyncTaskReq = new AsyncTaskReq();
    asyncTaskReq.setJobId(jobId);
    asyncTaskReq.setEa(job.getEa());
    asyncTaskReq.setEmployeeId(job.getEmployeeId());
    asyncTaskReq.setFilePath(downloadResult.filePath());
    asyncTaskReq.setFileName(downloadResult.filename());
    asyncTaskReq.setTaskType(AsyncTaskType.ASR_TENCENT);
    return  asyncTaskReq;
  }

  @Override
  public Optional<DocParseJob> queryJob(String jobId) {
    return docParseJobDao.queryJob(jobId);
  }

  @Override
  public boolean reportTokenUsage(String jobId) {
    DocParseJob docParseJob = getCompletedJob(jobId);
    JobParseArg jobParseArg = docParseJob.getJobParseArg();
    DocBillingRecord docBillingRecord = docParseJob.getDocBillingRecord();
    boolean reportResult = true;
    if (docBillingRecord != null && docBillingRecord.getPricingTokens() >= 0) {
      ToKenReport toKenReport = commonMapper.toToKenReport(jobParseArg, docBillingRecord);
      // 上报token使用情况
      reportResult = tokenUsagesService.report(toKenReport);
    }
    // 更新数据库上报结果
    docParseJobDao.billingReportDone(jobId, reportResult);
    return true; // 如果token使用量为0，直接返回true表示上报成功
  }

  /**
   * 根据原始任务唤醒幂等的任务
   * 1. 根据消息中的任务ID查询幂等任务信息
   * 2. 根据幂等任务信息结合消息状态更新任务状态
   * 3. 更新完成后发送幂等任务完成的MQ消息
   * @param jobId 原始任务完成消息Id
   * @return 是否成功唤醒任务
   */
  @Override
  public boolean wakeupTask(String jobId) {
    // 查询原始任务信息
    Optional<DocParseJob> docParseJobOpt = docParseJobDao.queryJob(jobId);
    if (docParseJobOpt.isEmpty()) {
      log.error("wakeupTask original job not found, jobId:{}", jobId);
      return false;
    }
    DocParseJob originalDocParseJob = docParseJobOpt.get();

    // 查询幂等任务信息
    String uniqueIds = originalDocParseJob.getUniqueIds();
    Optional<List<DocParseJob>> docParseJobs = docParseJobDao.queryJobByUniqueIds(uniqueIds);
    if (docParseJobs.isEmpty()) {
      log.info("wakeupTask idempotent job not found, uniqueIds:{}", uniqueIds);
      return false;
    }

    JobStatus originalJobStatus = JobStatus.of(originalDocParseJob.getJobStatus());
    JobRecords originalJobRecords = originalDocParseJob.getJobRecords();
    // 更新幂等任务状态
    List<DocParseJob> idempotentJobs = docParseJobs.get();
    for (DocParseJob idempotentJob : idempotentJobs) {
      String idempotentJobId = idempotentJob.get_id().toString();
      JobStatus jobStatus = JobStatus.of(idempotentJob.getJobStatus());
      JobParseArg jobParseArg = idempotentJob.getJobParseArg();
      String asyncCallbackMqTag = jobParseArg.getAsyncCallbackMqTag();
      if (jobStatus == JobStatus.PREPROCESS || jobStatus == JobStatus.QUEUE) {

        // 根据原始任务状态更新幂等任务状态
        if (originalJobStatus==JobStatus.COMPLETED){
          // 更新为完成状态
          docParseJobDao.complete(idempotentJobId, originalDocParseJob.getFileID(),
              originalDocParseJob.getDocBillingRecord());
        }else {
          // 更新为失败状态
          docParseJobDao.fail(idempotentJobId, originalJobRecords.getJobFailCode(),
              originalJobRecords.getJobFailReason());
        }

        if (StrUtils.notBlank(asyncCallbackMqTag)) {
          // 发送幂等任务完成消息
          DocParseCompletedMsg msg = switch (originalJobStatus) {
            case COMPLETED -> DocParseCompletedMsg.ok(jobParseArg.getEa(), idempotentJobId);
            case FAILED -> DocParseCompletedMsg.fail(jobParseArg.getEa(), idempotentJobId,
                originalJobRecords.getJobFailCode(), originalJobRecords.getJobFailReason());
            default -> null;
          };
          jobProducer.sendIdempotentNotificationMsg(msg, asyncCallbackMqTag);
        }

        log.info("wakeupTask idempotent job completed, jobId:{}", idempotentJobId);
      }
    }
    return false;
  }


  @Override
  public InputStream getContent(JobGetContent arg) {
    DocParseJob docParseJob = getCompletedJob(arg.getJobId());
    // 获取文件ID并从GridFS获取文件内容
    String fileID = docParseJob.getFileID();
    return fileService.getFileByGridFs(fileID);
  }

  private DocParseJob getCompletedJob(String jobId) {

    // 查询任务是否存在且检查任务状态是否为完成
    Optional<DocParseJob> docParseJobOpt = docParseJobDao.queryCompleteJob(jobId);
    if (docParseJobOpt.isEmpty()) {
      throw new BaseException(400, "jobId not found or not completed ", MODULE + ".getCompletedJob",
          jobId);
    }

    return docParseJobOpt.get();
  }

}
