package com.fxiaoke.file.process.service;

import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.model.AsyncTaskReq;
import com.fxiaoke.file.process.domain.model.AsyncTaskRes;
import com.fxiaoke.file.process.domain.model.ToMdParams;
import com.fxiaoke.file.process.domain.model.ToMdResult;
import java.io.BufferedWriter;
import java.nio.file.Path;
import java.util.List;

public interface CommonConvertService {


  /**
   * 读取Excel文件的内容并输出到流中
   *
   * @param filePath Excel文件路径
   * @param isRowMode true表示按行读取，false表示按列读取
   * @param writer 输出流, 用于输出解析结果
   */
  void ranksStream(Path filePath, boolean isRowMode, BufferedWriter writer)
      throws BaseException;

  /**
   * 将文件转换为Markdown格式
   *
   * @param params 文档文件信息 {@link ToMdParams}
   * @return 转换后的Markdown信息
   * @throws BaseException 异常
   */
  ToMdResult docToMd(ToMdParams params) throws BaseException;

  /**
   * 批量转换文件为Markdown格式
   *
   * @param params 代转换文档文件信息列表 {@link ToMdParams}
   * @return 转换后的Markdown信息列表
   * @throws BaseException 异常
   */
  List<ToMdResult> batchDocToMd(List<ToMdParams> params) throws BaseException;

  AsyncTaskRes subTask(AsyncTaskReq asyncTaskReq) throws BaseException;
}
