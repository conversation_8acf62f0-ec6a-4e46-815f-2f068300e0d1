package com.fxiaoke.file.process.service.options;

import com.fxiaoke.file.process.client.ArkClient;
import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.domain.constants.VisCompDetail;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.model.ImageDimension;
import com.fxiaoke.file.process.domain.model.Usages;
import com.fxiaoke.file.process.domain.model.VisCompConfig;
import com.fxiaoke.file.process.domain.model.ToMdParams;
import com.fxiaoke.file.process.domain.model.ToMdResult;
import com.fxiaoke.file.process.domain.model.VisCompReq;
import com.fxiaoke.file.process.domain.model.VisCompRes;
import com.fxiaoke.file.process.service.CommonOptions;
import com.fxiaoke.file.process.service.FileService;
import com.fxiaoke.file.process.utils.ImageBaseUtil;
import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ImageOptions implements CommonOptions {

  private static final String MODULE = "ImageOptions";

  private final ArkClient arkClient;
  private final FileService fileService;

  public ImageOptions(ArkClient arkClient, FileService fileService) {
    this.arkClient = arkClient;
    this.fileService = fileService;
  }

  @Override
  public ToMdResult toMarkdown(ToMdParams params) throws BaseException {
    try {
      // 调用视觉模型识别图片中的文本
      log.info("IMAGE TO MARKDOWN Start, EA: {},Filename:{}", params.getEa(), params.getFileName());
      Path filePath = params.getFilePath();
      String objectKey = fileService.uploadLocalFileToS3(Constants.S3_BUCKET_ROOT_DIR_VL_OCR,
          params.getFilePath());
      log.info("IMAGE TO MARKDOWN Process, ObjectKey: {}", objectKey);
      String processStyle = generateProcessStyle(filePath);
      String s3PreSignUrl = fileService.getS3PreSignUrl(objectKey, processStyle);
      VisCompReq<String> visCompReq = toVisCompReq(params, s3PreSignUrl);
      VisCompRes<String> visCompRes = arkClient.visComp(visCompReq);
      Path mdFilePath = params.generatMdFilePath();
      String content = visCompRes.getContent();
      try (BufferedWriter writer = Files.newBufferedWriter(mdFilePath, StandardCharsets.UTF_8)) {
        writer.write(content);
      }
      Usages usages = visCompRes.getUsages();
      log.info("IMAGE TO MARKDOWN End,EA: {},Md File:{},Usages:{}", params.getEa(), mdFilePath, usages);
      return new ToMdResult(1, params.getFileName(), mdFilePath, usages);
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".toImageMarkdown", params);
    }
  }

  private VisCompReq<String> toVisCompReq(ToMdParams params,String s3PreSignUrl){
    VisCompConfig visCompConfig = params.getVisCompConfig();
    VisCompReq<String> visCompReq = new VisCompReq<>();
    visCompReq.setModel(visCompConfig.getModel());
    visCompReq.setDetail(VisCompDetail.HIGH);
    visCompReq.setImageUrl(s3PreSignUrl);
    visCompReq.setSystemPrompt(visCompConfig.getSystemPrompt());
    visCompReq.setUserPrompt(visCompConfig.getUserPrompt());
    visCompReq.setData(params.getEa());
    return  visCompReq;
  }

  private String generateProcessStyle(Path filePath) throws IOException {
    ImageDimension imageResolution = ImageBaseUtil.getImageResolution(filePath);
    if (imageResolution.getResolution() < Constants.DCI_2K) {
      return "";
    }
    ImageDimension compressingWH = ImageBaseUtil.getCompressingWH(imageResolution.getWidth(),
        imageResolution.getHeight(), Constants.DCI_2K);
    return String.format("image/resize,w_%d,h_%d", compressingWH.getWidth(),
        compressingWH.getHeight());
  }
}
