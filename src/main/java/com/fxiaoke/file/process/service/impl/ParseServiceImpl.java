package com.fxiaoke.file.process.service.impl;

import com.fxiaoke.file.process.dao.AsyncPollingTaskDao;
import com.fxiaoke.file.process.dao.DocParseJobDao;
import com.fxiaoke.file.process.domain.api.request.BatchDocToMdArg;
import com.fxiaoke.file.process.domain.api.response.model.MarkdownInfo;
import com.fxiaoke.file.process.domain.constants.FileType;
import com.fxiaoke.file.process.domain.constants.JobStatus;
import com.fxiaoke.file.process.domain.entity.DocParseJob;
import com.fxiaoke.file.process.domain.entity.model.DocBillingRecord;
import com.fxiaoke.file.process.domain.entity.model.JobParseArg;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.model.FileDownloadResult;
import com.fxiaoke.file.process.domain.model.FileUploadParams;
import com.fxiaoke.file.process.domain.model.FileUploadResult;
import com.fxiaoke.file.process.domain.model.ToMdParams;
import com.fxiaoke.file.process.domain.model.ToMdResult;
import com.fxiaoke.file.process.domain.model.Usages;
import com.fxiaoke.file.process.domain.mq.DocParseCompletedMsg;
import com.fxiaoke.file.process.manager.ModelPriceManager;
import com.fxiaoke.file.process.mq.JobProducer;
import com.fxiaoke.file.process.service.ParseService;
import com.fxiaoke.file.process.utils.StrUtils;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;

@Slf4j
public abstract class ParseServiceImpl implements ParseService {

  protected static final String MODULE = "ParseServiceImpl";

  protected final JobProducer jobProducer;
  protected final DocParseJobDao docParseJobDao;
  protected final AsyncPollingTaskDao asyncPollingTaskDao;
  protected final ModelPriceManager modelPriceManager;

  protected ParseServiceImpl(JobProducer jobProducer, DocParseJobDao docParseJobDao,
      AsyncPollingTaskDao asyncPollingTaskDao,
      ModelPriceManager modelPriceManager) {
    this.jobProducer = jobProducer;
    this.docParseJobDao = docParseJobDao;
    this.asyncPollingTaskDao = asyncPollingTaskDao;
    this.modelPriceManager = modelPriceManager;
  }

  protected DocBillingRecord convertToDocBillingRecord(ToMdResult toMdResult, long fileSize) {
    DocBillingRecord docBillingRecord = new DocBillingRecord();

    // 基础信息
    docBillingRecord.setPageCount(toMdResult.getPageCount());
    docBillingRecord.setFileSize(fileSize);

    Usages usages = toMdResult.getUsages();
    if (usages != null && usages.getCount() >= 0) {
      // 调用视觉模型 Token消耗
      String modelName = usages.getModel();
      int inputTokens = usages.getTotalInputTokens();
      int outputTokens = usages.getTotalOutputTokens();
      int totalTokens = usages.getTotalTokens();
      docBillingRecord.setImageCount(usages.getCount());
      // 获取记录用模型名称屏蔽端点别名
      String recordAlias = modelPriceManager.getRecordAlias(modelName);
      docBillingRecord.setModelName(recordAlias);
      docBillingRecord.setTotalInputTokens(inputTokens);
      docBillingRecord.setTotalOutputTokens(outputTokens);
      docBillingRecord.setTotalTokens(totalTokens);
      // 换算为算粒价格
      double pricingTokens = modelPriceManager.getGranularCalculation(modelName, inputTokens,
          outputTokens);
      docBillingRecord.setPricingTokens(pricingTokens);
    }
    return docBillingRecord;
  }

  protected List<ToMdParams> toToMdParams(List<FileDownloadResult> downloadResults,
      BatchDocToMdArg arg) {
    List<ToMdParams> toMdParams = new ArrayList<>();
    for (FileDownloadResult downloadResult : downloadResults) {
      toMdParams.add(toToMdParams(downloadResult,arg));
    }
    return toMdParams;
  }

  protected ToMdParams toToMdParams(FileDownloadResult downloadResult,BatchDocToMdArg arg) {
    ToMdParams toMdParams = ToMdParams.of(downloadResult.filename(), downloadResult.filePath());
    toMdParams.setEa(arg.getEa());
    toMdParams.setEmployeeId(arg.getEmployeeId());
    return toMdParams;
  }

  protected ToMdParams toToMdParams(String jobId,FileDownloadResult downloadResult, JobParseArg arg) {
    ToMdParams toMdParams = ToMdParams.of(downloadResult.filename(), downloadResult.filePath(), arg);
    toMdParams.setJobId(jobId);
    toMdParams.setEa(arg.getEa());
    toMdParams.setEmployeeId(arg.getEmployeeId());
    return toMdParams;
  }

  protected List<FileUploadParams> toFileUploadParams(String ea, Integer employeeId,
      List<ToMdResult> results) {
    return results.stream().map(
        result -> new FileUploadParams(ea, employeeId, result.getFilePath(), ",",
            result.getFileName(), FileType.MARKDOWN.getFileType())).toList();
  }

  protected List<MarkdownInfo> toMarkdownInfos(List<FileUploadResult> fileUploadResults) {
    return fileUploadResults.stream().map(
            result -> new MarkdownInfo(result.path(), result.filename(), result.ext(), result.size()))
        .toList();
  }

  protected void sendDelayNotificationMsg(DocParseJob docParseJob){
    JobParseArg jobParseArg = docParseJob.getJobParseArg();
    String asyncCallbackMqTag = jobParseArg.getAsyncCallbackMqTag();
    if (StrUtils.isBlank(asyncCallbackMqTag)){
      return;
    }
    DocParseCompletedMsg msg = toDocParseCompletedMsg(docParseJob);
    jobProducer.sendCompletedDelayNotificationMsg(msg, asyncCallbackMqTag);
  }

  protected DocParseCompletedMsg toDocParseCompletedMsg(DocParseJob docParseJob) {
    JobStatus jobStatus = JobStatus.of(docParseJob.getJobStatus());
    JobParseArg jobParseArg = docParseJob.getJobParseArg();
    ObjectId jobId = docParseJob.get_id();
    return switch (jobStatus) {
      case COMPLETED -> DocParseCompletedMsg.ok(jobParseArg.getEa(),
          jobId.toString());
      case FAILED -> DocParseCompletedMsg.fail(jobParseArg.getEa(),
          jobId.toString(),
          docParseJob.getJobRecords().getJobFailCode(),
          docParseJob.getJobRecords().getJobFailReason());
      default -> null;
    };
  }

  protected void sendCompletedNotification(String ea, String jobId, String asyncCallbackMqTag) {
    if (StrUtils.notBlank(asyncCallbackMqTag)) {
      DocParseCompletedMsg msg = DocParseCompletedMsg.ok(ea, jobId);
      jobProducer.sendCompletedNotificationMsg(msg, asyncCallbackMqTag);
    }
  }

  protected void failJob(DocParseJob docParseJob, Exception exception) {
    // 保存错误信息到数据库
    String jobId = docParseJob.get_id().toString();

    int jobFailCode;
    String jobFailReason;
    if (exception instanceof BaseException e) {
      jobFailCode = e.getCode();
      jobFailReason = e.getReason();
    } else {
      jobFailCode = 500;
      jobFailReason = exception.getMessage();
    }

    log.error("Job processing failed start, jobId:{}, error:{}", jobId, jobFailReason);
    docParseJobDao.fail(jobId, jobFailCode, jobFailReason);
    // 发送失败通知MQ
    JobParseArg jobParseArg = docParseJob.getJobParseArg();
    if (StrUtils.notBlank(jobParseArg.getAsyncCallbackMqTag())) {
      DocParseCompletedMsg msg = DocParseCompletedMsg.fail(jobParseArg.getEa(), jobId, jobFailCode,
          jobFailReason);
      jobProducer.sendCompletedNotificationMsg(msg, jobParseArg.getAsyncCallbackMqTag());
    }
    log.error("Job processing failed end, jobId:{}, error:{}", jobId, jobFailReason);
  }
}
