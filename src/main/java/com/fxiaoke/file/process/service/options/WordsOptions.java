package com.fxiaoke.file.process.service.options;

import com.aspose.words.Dml3DEffectsRenderingMode;
import com.aspose.words.DmlRenderingMode;
import com.aspose.words.Document;
import com.aspose.words.IImageSavingCallback;
import com.aspose.words.ImageSavingArgs;
import com.aspose.words.ImlRenderingMode;
import com.aspose.words.LoadOptions;
import com.aspose.words.MarkdownExportAsHtml;
import com.aspose.words.MarkdownLinkExportMode;
import com.aspose.words.MarkdownListExportMode;
import com.aspose.words.MarkdownOfficeMathExportMode;
import com.aspose.words.MarkdownSaveOptions;
import com.aspose.words.SaveFormat;
import com.aspose.words.TableContentAlignment;
import com.aspose.words.TxtExportHeadersFootersMode;
import com.fxiaoke.file.process.client.ArkClient;
import com.fxiaoke.file.process.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.domain.constants.FileType;
import com.fxiaoke.file.process.domain.constants.LineSeparator;
import com.fxiaoke.file.process.domain.constants.VisCompDetail;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.model.MarkdownImgInfo;
import com.fxiaoke.file.process.domain.model.VisCompConfig;
import com.fxiaoke.file.process.domain.model.ToMdParams;
import com.fxiaoke.file.process.domain.model.ToMdResult;
import com.fxiaoke.file.process.domain.model.Usages;
import com.fxiaoke.file.process.domain.model.VisCompRes;
import com.fxiaoke.file.process.service.FileService;
import com.fxiaoke.file.process.utils.MarkDownUtils;
import java.io.FileOutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.text.MessageFormat;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class WordsOptions extends DocOptions {

  private static final String MODULE = "WordsOptions";

  public WordsOptions(ArkClient arkClient, FileService fileService,
      CmsPropertiesConfig cmsPropertiesConfig) {
    super(arkClient, fileService, cmsPropertiesConfig);
  }

  private LoadOptions loadOptions() {
    LoadOptions loadOptions = new LoadOptions();
    // 设置允许将wmf和emf图像转换为PNG以节省内存
    loadOptions.setConvertMetafilesToPng(true);
    // 设置忽略OLE数据
    loadOptions.setIgnoreOleData(true);
    return loadOptions;
  }

  private Document init(Path filePath) {
    try {
      return new Document(filePath.toString(), loadOptions());
    } catch (Exception e) {
      throw new BaseException(e, 400,
          MODULE + ".init", "Word file encrypted or corrupted or version compatibility", filePath);
    }
  }

  private MarkdownSaveOptions saveOptions(Path saveImgResourceDir, boolean ocr) {
    MarkdownSaveOptions saveOptions = saveOptions();
    if (ocr) {
      // 设置导出图像分辨率DPI
      saveOptions.setImageResolution(300);
    }
    // 图像不以Base64格式导出
    saveOptions.setExportImagesAsBase64(false);
    saveOptions.setImageSavingCallback(new ImageSavingCallback(saveImgResourceDir));
    return saveOptions;
  }

  private MarkdownSaveOptions saveOptions() {
    MarkdownSaveOptions saveOptions = new MarkdownSaveOptions();
    // 设置保存格式为Markdown
    saveOptions.setSaveFormat(SaveFormat.MARKDOWN);
    // 导出文本的编码格式
    saveOptions.setEncoding(StandardCharsets.UTF_8);
    // 不导出Aspose版本信息
    saveOptions.setExportGeneratorName(false);

    // 不导出分页符(否则会变成特殊乱码)
    saveOptions.setForcePageBreaks(false);

    // 忽略 DrawingML 的回退形状并呈现 DrawingML 本身
    saveOptions.setDmlRenderingMode(DmlRenderingMode.DRAWING_ML);
    // 轻量级稳定的渲染(忽略照明、材质等其他高级效果)
    saveOptions.setDml3DEffectsRenderingMode(Dml3DEffectsRenderingMode.BASIC);
    // 忽略墨水(InkML)对象的回退形状并呈现 InkML 本身
    saveOptions.setImlRenderingMode(ImlRenderingMode.INK_ML);

    // 作为原始HTML导出到markdown的元素
    saveOptions.setExportAsHtml(MarkdownExportAsHtml.NONE);
    // 不导出页眉页脚
    saveOptions.setExportHeadersFootersMode(TxtExportHeadersFootersMode.NONE);
    // 导出列表项为与Markdown语法兼容的格式
    saveOptions.setLinkExportMode(MarkdownLinkExportMode.INLINE);
    // 导出数学公式为纯文本
    saveOptions.setOfficeMathExportMode(MarkdownOfficeMathExportMode.MATH_ML);
    // 设置表格内文本对齐方式-自动
    saveOptions.setTableContentAlignment(TableContentAlignment.AUTO);
    // 设置列表导出模式
    saveOptions.setListExportMode(MarkdownListExportMode.MARKDOWN_SYNTAX);
    // 设置保存为md文档时的段落分隔符
    saveOptions.setParagraphBreak(LineSeparator.UNIX.getLine());

    // 设置空段落导出模式（NONE: 不导出空段落）
    // saveOptions.setEmptyParagraphExportMode(MarkdownEmptyParagraphExportMode.NONE);

    // 输出文档内容格式不启用格式(仅支持 HTML、MHTML、EPUB、WordML、RTF、DOCX 和 ODT)
    saveOptions.setPrettyFormat(false);
    // 是否使用高质量渲染
    saveOptions.setUseHighQualityRendering(true);

    return saveOptions;
  }

  private static class ImageSavingCallback implements IImageSavingCallback {

    // 用于计数生成的图像数量
    private int mCount;
    // 保存的文件名
    private final Path saveResourceDir;

    // 构造函数，接收输出文件名并存储
    public ImageSavingCallback(Path saveResourceDir) {
      this.saveResourceDir = saveResourceDir;
    }

    // 图像保存方法，会在图像保存时被调用
    public void imageSaving(ImageSavingArgs args) throws Exception {
      // 生成新的图像文件名，格式为 "输出文件名 shape 计数, of type 图像类型.扩展名"
      String imageFileName = MessageFormat.format("{0}.{1}", ++mCount,
          FilenameUtils.getExtension(args.getImageFileName()));
      // 设置图像的文件名和输出流，使图像能够保存到指定的目录中
      args.setImageFileName(imageFileName);
      args.setImageStream(new FileOutputStream(saveResourceDir.resolve(imageFileName).toFile()));
    }
  }

  private record wordsInfo(int pageCount,Path mdFilePath) {
  }

  private wordsInfo commonToMarkdown(Document doc,ToMdParams params) throws Exception{
    int pageCount = doc.getPageCount();
    log.info("Words To Markdown Start, Page Count: {}", pageCount);

    Path mdFilePath = params.generatMdFilePath();
    VisCompConfig visCompConfig = params.getVisCompConfig();
    MarkdownSaveOptions saveOptions = saveOptions(params.getFilePath().getParent(),
        visCompConfig.isOcr());
    doc.save(mdFilePath.toString(), saveOptions);
    log.info("Words to Markdown Process, FilePath: {}", mdFilePath);

    mdFilePath = MarkDownUtils.mdSTD(mdFilePath, visCompConfig.isOcr());
    log.info("Words To Markdown Process,STD FilePath {}", mdFilePath);

    return new wordsInfo(pageCount, mdFilePath);
  }

  protected ToMdResult toTextMarkdown(ToMdParams params) {
    Document doc = init(params.getFilePath());
    try {
      wordsInfo wordsInfo = commonToMarkdown(doc, params);
      return new ToMdResult(wordsInfo.pageCount(), params.getFileName(), wordsInfo.mdFilePath());
    } catch (BaseException e) {
      throw e;
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".toTextMarkdown", params);
    }
  }

  protected ToMdResult toImageMarkdown(ToMdParams params) {
    Document doc = init(params.getFilePath());
    try {
      wordsInfo wordsInfo = commonToMarkdown(doc, params);

      VisCompConfig visCompConfig = params.getVisCompConfig();
      visCompConfig.setDetail(VisCompDetail.LOW);
      // 提取Markdown中所有图片信息
      List<MarkdownImgInfo> markdownImgInfos = MarkDownUtils.extractImages(wordsInfo.mdFilePath());
      log.info("Words To Markdown Process, Total Image: {}", markdownImgInfos.size());

      // 调用视觉模型识别图片中的文本
      List<VisCompRes<MarkdownImgInfo>> visCompRes = visCompRes(visCompConfig,
          markdownImgInfos);
      log.info("Words To Markdown Process, Total Vl: {}", visCompRes.size());

      // 统计识别结果的Token用量
      Usages usages = accumulateUsage(visCompConfig,visCompRes);
      log.info("Words To Markdown Process, Usages: {}", usages);

      // 将识别结果写入Markdown文件并清洗无用标签、内容
      Path finalMdFilePath = MarkDownUtils.replaceImageTags(wordsInfo.mdFilePath(), visCompRes, FileType.DOCX);
      log.info("Words To Markdown End, Final Md File: {}", finalMdFilePath);

      return new ToMdResult(wordsInfo.pageCount(), params.getFileName(), finalMdFilePath, usages);
    } catch (BaseException e) {
      throw e;
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".toImageMarkdown", params);
    }
  }

}
