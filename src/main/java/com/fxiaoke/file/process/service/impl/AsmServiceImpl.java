package com.fxiaoke.file.process.service.impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.converter.exception.EIEANotFoundException;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.service.AsmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j(topic = "asmService")
public class AsmServiceImpl implements AsmService {

  private static final String MODULE = "AsmServiceImpl";

  private final EIEAConverter eieaConverter;

  public AsmServiceImpl(EIEAConverter eieaConverter) {
    this.eieaConverter = eieaConverter;
  }

  @Override
  public int getEid(String ea) {
    try {
      return eieaConverter.enterpriseAccountToId(ea);
    } catch (EIEANotFoundException e) {
      log.error("{} getEid error, ea: {}, error: {}", MODULE, ea, e.getMessage());
      throw new BaseException(400, e.getMessage(), MODULE + ".getEid", ea);
    }
  }

  @Override
  public String getEa(int eid) {
    return eieaConverter.enterpriseIdToAccount(eid);
  }
}
