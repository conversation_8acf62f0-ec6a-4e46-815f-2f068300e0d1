package com.fxiaoke.file.process.service;

import java.io.InputStream;
import java.nio.file.Path;

public interface S3Service {

  /**
   * 上传文件到S3
   *
   * @param bucketRootDir S3存储桶的根目录(一级目录)
   * @param filePath 要上传的文件路径
   * @return objectKey
   */
  String uploadToS3(String bucketRootDir, Path filePath);

  /**
   * 生成预先签名的URL，用于从S3下载文件
   *
   * @param objectKey 存储桶中的存储路径
   * @param process   处理参数
   * @return 预先签名的URL
   */
  String getPreSignUrl(String objectKey,String process);

  long getBigFileSize(String ea,String path);

  InputStream downloadBigFile(String ea,String path);
}
