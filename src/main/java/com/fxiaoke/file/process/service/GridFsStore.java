package com.fxiaoke.file.process.service;

import java.io.InputStream;
import java.nio.file.Path;
import java.util.Date;

public interface GridFsStore {

  String saveFile(Path fileLocalPath, String fileName, String extensions);

  String saveFile(String content, String fileName, String extensions);

  String saveFile(InputStream inputStream, String fileName, String extensions);

  InputStream getFileById(String fileId);

  int deleteFilesBeforeDate(Date date);
}
