package com.fxiaoke.file.process.service.options;

import com.aspose.cells.Cells;
import com.aspose.cells.Encoding;
import com.aspose.cells.LoadOptions;
import com.aspose.cells.MarkdownSaveOptions;
import com.aspose.cells.MemorySetting;
import com.aspose.cells.SaveFormat;
import com.aspose.cells.Workbook;
import com.aspose.cells.Worksheet;
import com.aspose.cells.WorksheetCollection;
import com.fxiaoke.file.process.client.ArkClient;
import com.fxiaoke.file.process.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.domain.constants.FileType;
import com.fxiaoke.file.process.domain.constants.LineSeparator;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.model.ToMdParams;
import com.fxiaoke.file.process.domain.model.ToMdResult;
import com.fxiaoke.file.process.service.FileService;
import com.fxiaoke.file.process.utils.FilenameUtil;
import com.fxiaoke.file.process.utils.MarkdownFormatUtil;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j(topic = "CellsOptions")
public class CellsOptions extends DocOptions {

  private static final String MODULE = "CellsOptions";

  private static final String UTF8_BOM = "\uFEFF";
  private static final String UTF16LE_BOM = "\uFFFE";
  private static final String UTF16BE_BOM = "\uFEFF";

  public CellsOptions(ArkClient arkClient, FileService fileService,
      CmsPropertiesConfig cmsPropertiesConfig) {
    super(arkClient, fileService, cmsPropertiesConfig);
  }

  private record SheetInfo(Path mdPath, String name,boolean ending) {

  }

  /**
   * 配置文件加载选项 - 设置内存使用偏好,优化内存使用
   */
  private LoadOptions loadOptions() {
    LoadOptions options = new LoadOptions();
    options.setMemorySetting(MemorySetting.MEMORY_PREFERENCE);
    return options;
  }

  /**
   * 配置Markdown保存选项 <br>
   * - 设置UTF8编码 <br>
   * - 启用合并区域 <br>
   * - 设置保存格式为Markdown <br>
   * - 禁用图表缓存刷新 <br> -
   * 启用自动创建目录 <br>
   * - 禁用文档属性加密 <br>
   * - 使用Unix换行符 <br>
   */
  private MarkdownSaveOptions saveOptions() {
    MarkdownSaveOptions options = new MarkdownSaveOptions();
    // 设置保存文本的格式
    options.setEncoding(Encoding.getUTF8());
    // 设置合并区域
    options.setMergeAreas(true);
    // 设置保存的格式
    options.setFormatStrategy(SaveFormat.MARKDOWN);
    // 不刷新图表缓存数据
    options.setRefreshChartCache(false);
    // 在保存文件前，如果目录不存在，则会自动创建目录
    options.setCreateDirectory(true);
    // 不加密文档属性
    options.setEncryptDocumentProperties(false);
    // 默认使用Unix换行符
    options.setLineSeparator(LineSeparator.UNIX.getLine());
    // 设置缓存的文件夹,用于存储一些大型数据
    options.setCachedFileFolder(Constants.CELLS_CACHE_FOLDER);
    // 不导出图片为Base64格式
    options.setExportImagesAsBase64(false);
    return options;
  }

  /**
   * 初始化工作簿 - 使用配置的加载选项打开Excel文件 - 如果文件加密会抛出异常
   */
  private Workbook init(Path filePath) {
    try {
      return new Workbook(filePath.toString(), loadOptions());
    } catch (Exception e) {
      throw new BaseException(e, 400, MODULE + ".init",
          "Excel file encrypted or corrupted or version compatibility", filePath);
    }
  }

  /**
   * 读取Excel文件的内容并输出到流中
   *
   * @param filePath  Excel文件路径
   * @param isRowMode true表示按行读取，false表示按列读取
   * @param writer    输出流, 用于输出解析结果
   */
  public void ranksStream(Path filePath, boolean isRowMode, BufferedWriter writer)
      throws BaseException {
    Workbook workbook = init(filePath);
    try {
      WorksheetCollection sheets = workbook.getWorksheets();
      for (int i = 0; i < sheets.getCount(); i++) {
        Worksheet sheet = sheets.get(i);
        log.info("Processing sheet start: {}", sheet.getName());
        processSheet(sheet, isRowMode, writer);
        log.info("Processing sheet end: {}", sheet.getName());
        // 刷新缓冲区，确保数据及时写入
        writer.flush();
      }
    } catch (Exception e) {
      throw new BaseException(e, 400, MODULE + ".ranksStream", filePath);
    }
  }

  private void processSheet(Worksheet sheet, boolean isRowMode, BufferedWriter writer)
      throws IOException {
    // 写入sheet标识
    writer.write("<!---" + sheet.getName() + "--->\n");

    Cells cells = sheet.getCells();
    // 注意：maxDataColumn和maxDataRow返回的是索引值，需要+1才是实际数量
    int maxCol = cells.getMaxDataColumn() + 1;
    int maxRow = cells.getMaxDataRow() + 1;

    // 根据模式选择处理方式
    if (isRowMode) {
      processData(cells, maxRow, maxCol, false, writer);
    } else {
      processData(cells, maxCol, maxRow, true, writer);
    }
  }

  private void processData(Cells cells, int outer, int inner, boolean isColumnMode,
      BufferedWriter writer) throws IOException {
    StringBuilder sb = new StringBuilder();

    for (int i = 0; i < outer; i++) {
      sb.setLength(0); // 重用StringBuilder

      // 处理每行/列的数据
      for (int j = 0; j < inner; j++) {
        String value =
            isColumnMode ? cells.get(j, i).getStringValue() : cells.get(i, j).getStringValue();

        appendCell(sb, value, j == inner - 1);
      }

      writer.write(sb.toString());
      writer.write('\n');
      writer.flush();
    }
  }

  private void appendCell(StringBuilder sb, String value, boolean isLast) {
    sb.append('"').append(escapeCell(value)).append('"');

    if (!isLast) {
      sb.append(',');
    }
  }

  private String escapeCell(String content) {
    if (content == null || content.isEmpty()) {
      return "";
    }

    // 优化：预估转义后的大致容量
    StringBuilder sb = new StringBuilder(content.length() + 16);

    for (int i = 0; i < content.length(); i++) {
      char c = content.charAt(i);
      switch (c) {
        case '"':
          sb.append("\"\"");
          break;
        case '\r':
          sb.append("\\r");
          break;
        case '\n':
          sb.append("\\n");
          break;
        case '\t':
          sb.append("\\t");
          break;
        default:
          sb.append(c);
      }
    }
    return sb.toString();
  }

  @Override
  public ToMdResult toImageMarkdown(ToMdParams params) {
    return toCommonMarkdown(params);
  }

  @Override
  public ToMdResult toTextMarkdown(ToMdParams params) {
    return toCommonMarkdown(params);
  }

  private ToMdResult toCommonMarkdown(ToMdParams params) {
    Workbook workbook = init(params.getFilePath());
    try {
      List<SheetInfo> sheetInfos = saveIndividualSheets(workbook, params.getFilePath());
      int sheetCount = sheetInfos.size();
      log.info("Excel To Markdown Start, Worksheets: {}", sheetCount);
      // 合并所有md文件
      Path mdFilePath = params.generatMdFilePath();
      try (BufferedWriter writer = Files.newBufferedWriter(mdFilePath)) {
        for (int i = 0; i < sheetCount; i++) {
          log.info("Excel To Markdown Process, Worksheet: {} ", i + 1);
          writeSheetContent(writer, sheetInfos.get(i));
          // 刷新缓冲区，确保数据及时写入
          writer.flush();
        }
      }
      log.info("Excel To Markdown End, Total Worksheets: {}", sheetCount);
      return new ToMdResult(sheetCount, params.getFileName(), mdFilePath);
    } catch (Exception e) {
      throw new BaseException(e, 400, MODULE + "-toMarkdown", params);
    } finally {
      workbook.dispose();
    }
  }

  /**
   * 保存Excel工作表为Markdown文件 - 保存工作表为单独的md文件 - 保存工作表名称和md文件路径
   */
  private List<SheetInfo> saveIndividualSheets(Workbook workbook, Path mdFilePath)
      throws Exception {
    List<SheetInfo> sheetInfos = new ArrayList<>();
    WorksheetCollection worksheets = workbook.getWorksheets();
    int sheetCount = worksheets.getCount();
    for (int i = 0; i < sheetCount; i++) {
      worksheets.setActiveSheetIndex(i);
      String sheetMdFile = FilenameUtil.resolveSibling(mdFilePath, String.valueOf(i),
          FileType.MARKDOWN.getFileType());
      workbook.save(sheetMdFile, saveOptions());
      boolean ending = sheetCount != 1 && sheetCount != (i + 1);
      // 保存sheet的名称、md文件路径
      sheetInfos.add(new SheetInfo(Path.of(sheetMdFile), worksheets.get(i).getName(),ending));
    }
    return sheetInfos;
  }

  /**
   * 逐个写入工作表内容合并为一个Excel的Markdown文件
   */
  private void writeSheetContent(BufferedWriter writer, SheetInfo sheetInfo)
      throws IOException {
    // 写入工作表内容(会自动写入标题)
    try (BufferedReader reader = Files.newBufferedReader(sheetInfo.mdPath,
        StandardCharsets.UTF_8)) {
      String line;
      boolean isFirstLine = true;
      while ((line = reader.readLine()) != null) {
        if (isFirstLine) {
          line = removeBOM(line);
          isFirstLine = false;
        }

        // 去除表格行中的图片标签
        line = removeImageTags(line);

        writer.write(line);
        writer.newLine();
      }
    }
    if (sheetInfo.ending) {
      // 添加分隔线
      writer.write(MarkdownFormatUtil.horizontalRule());
    }
    writer.newLine();
  }

  /**
   * IMAGE_PATTERN 匹配 ![图片描述](url) <br>
   * 支持带title的格式：![图片描述](url "title") <br>
   * 标准格式 ![图片描述]<br>
   * 没有URL的情况 ![图片描述] <br>
   * 其他内容、图片标签后跟其他内容
   */
  private static final Pattern IMAGE_PATTERN = Pattern.compile("!\\[][^\\s|]*");

  /**
   * 移除BOM头
   */
  private String removeBOM(String line) {
    if (line == null) {
      return null;
    }

    if (line.startsWith(UTF8_BOM)) {
      return line.substring(1);
    }
    if (line.startsWith(UTF16LE_BOM)) {
      return line.substring(1);
    }
    if (line.startsWith(UTF16BE_BOM)) {
      return line.substring(1);
    }
    return line;
  }

  /**
   * 去除文本中的图片标签
   * @param text 原始文本
   * @return 去除图片标签后的文本
   */
  private String removeImageTags(String text) {
    if (text == null || text.trim().isEmpty()) {
      return text;
    }

    return IMAGE_PATTERN.matcher(text).replaceAll("");
  }

}
