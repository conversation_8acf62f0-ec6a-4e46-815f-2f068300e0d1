package com.fxiaoke.file.process.service.impl;

import com.fxiaoke.file.process.dao.DocParseJobDao;
import com.fxiaoke.file.process.domain.api.request.TencentCallbackReq;
import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.domain.entity.DocParseJob;
import com.fxiaoke.file.process.domain.entity.model.DocBillingRecord;
import com.fxiaoke.file.process.domain.entity.model.JobParseArg;
import com.fxiaoke.file.process.domain.mq.DocParseCompletedMsg;
import com.fxiaoke.file.process.manager.ModelPriceManager;
import com.fxiaoke.file.process.mq.JobProducer;
import com.fxiaoke.file.process.service.CallBackService;
import com.fxiaoke.file.process.service.FileService;
import com.fxiaoke.file.process.utils.StrUtils;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j(topic = "CallBackServiceImpl")
public class CallBackServiceImpl implements CallBackService {

  private final FileService fileService;
  protected final JobProducer jobProducer;
  private final DocParseJobDao docParseJobDao;
  private final ModelPriceManager modelPriceManager;

  public CallBackServiceImpl(FileService fileService, JobProducer jobProducer,
      DocParseJobDao docParseJobDao, ModelPriceManager modelPriceManager) {
    this.fileService = fileService;
    this.jobProducer = jobProducer;
    this.docParseJobDao = docParseJobDao;
    this.modelPriceManager = modelPriceManager;
  }

  @Override
  public void tencentAsrCallback(TencentCallbackReq callbackReq) {
    log.info("Tencent Asr Callback Start, jobId: {}, code: {}, requestId: {}",
        callbackReq.getJobId(), callbackReq.getCode(), callbackReq.getRequestId());

    String jobId = callbackReq.getJobId();
    Optional<DocParseJob> docParseJobOpt = docParseJobDao.queryJob(jobId);
    if (docParseJobOpt.isEmpty()) {
      log.warn("Tencent Asr Callback, Job not found for ID: {}", jobId);
      return;
    }
    DocParseJob docParseJob = docParseJobOpt.get();
    JobParseArg job = docParseJob.getJobParseArg();

    // 检查任务状态
    int code = callbackReq.getCode();
    if (code != 0) {
      String failMsg = String.format("code: %d, message: %s", code, callbackReq.getMessage());
      failJob(jobId, job, 400, failMsg);
      return;
    }

    try {
      // 保存文件到GridFS
      String content = callbackReq.getText();
      String fileId = fileService.saveToGridFs(content, job.getFileName(), job.getTargetType());

      // 计算账单信息
      DocBillingRecord docBillingRecord = convertToDocBillingRecord(callbackReq.getAudioTime(),
          content.length());
      docParseJobDao.complete(jobId, fileId, docBillingRecord);

      // 发送完成消息(如果tag不空,同时上报账单信息-利用MQ重试确保可靠性)
      sendCompletedNotification(job.getEa(), jobId, job.getAsyncCallbackMqTag());

      log.info("Tencent Asr Callback End, jobId: {}, audioTime: {}", jobId,
          callbackReq.getAudioTime());
    } catch (Exception e) {
      failJob(jobId, job, 500, e.getMessage());
    }
  }

  private void failJob(String jobId, JobParseArg job, int code, String message) {
    docParseJobDao.fail(jobId, code, message);
    // 发送失败通知MQ
    if (StrUtils.notBlank(job.getAsyncCallbackMqTag())) {
      DocParseCompletedMsg msg = DocParseCompletedMsg.fail(job.getEa(), jobId, 400, message);
      jobProducer.sendCompletedNotificationMsg(msg, job.getAsyncCallbackMqTag());
    }
    log.error("Job processing failed end, jobId:{}, error:{}", jobId, message);
  }

  private void sendCompletedNotification(String ea, String jobId, String asyncCallbackMqTag) {
    if (StrUtils.notBlank(asyncCallbackMqTag)) {
      DocParseCompletedMsg msg = DocParseCompletedMsg.ok(ea, jobId);
      jobProducer.sendCompletedNotificationMsg(msg, asyncCallbackMqTag);
    }
  }

  private DocBillingRecord convertToDocBillingRecord(double audioTime, int contentLength) {
    DocBillingRecord docBillingRecord = new DocBillingRecord();

    // 设置页码（音频文件没有页码）
    docBillingRecord.setPageCount(0);
    // 设置音频时长
    docBillingRecord.setDuration(audioTime);
    // 设置文件大小
    docBillingRecord.setFileSize(contentLength);
    // 设置图片数量（音频文件没有图片）
    docBillingRecord.setImageCount(0);

    // 将音频时长根据规则换算为Token，1秒钟等于1KToken,1个字符等于10Token(输出Token不计费）
    docBillingRecord.setModelName(Constants.TENCENT_ASR_ENGINE);
    double inputToken = ModelPriceManager.tencentAsrDurationToInputTokens(audioTime);
    docBillingRecord.setTotalInputTokens((int) inputToken);
    double outputToken = ModelPriceManager.tencentAsrContentToOutputTokens(contentLength);
    docBillingRecord.setTotalOutputTokens((int) outputToken);

    docBillingRecord.setTotalTokens((int) (inputToken + outputToken));

    // 换算为算粒价格
    double pricingTokens = modelPriceManager.getGranularCalculation(Constants.TENCENT_ASR_ENGINE,
        inputToken, outputToken);
    docBillingRecord.setPricingTokens(pricingTokens);

    return docBillingRecord;
  }

}
