package com.fxiaoke.file.process.service;

import com.fxiaoke.file.process.domain.api.request.BatchDocToMdArg;
import com.fxiaoke.file.process.domain.api.request.DocRanksStreamArg;
import com.fxiaoke.file.process.domain.api.request.JobGetContent;
import com.fxiaoke.file.process.domain.api.response.BatchDocToMdResult;
import com.fxiaoke.file.process.domain.entity.DocParseJob;
import com.fxiaoke.file.process.domain.entity.model.JobParseArg;
import java.io.BufferedWriter;
import java.io.InputStream;
import java.util.Optional;

public interface ParseService {

  /**
   * 按行或列的方式读取Excel文件的内容并输出到流中
   *
   * @param arg 解析参数 {@link DocRanksStreamArg}
   *            arg.rowRead true表示按行读取，false表示按列读取
   * @param writer 输出流, 用于输出解析结果
   */
  void ranksStream(DocRanksStreamArg arg, BufferedWriter writer);

  BatchDocToMdResult batchDocToMd(BatchDocToMdArg arg);

  String submitJob(JobParseArg jobParseArg);

  void processJob(DocParseJob docParseJob);

  boolean subTask(DocParseJob docParseJob);

  Optional<DocParseJob> queryJob(String jobId);

  InputStream getContent(JobGetContent arg);

  boolean reportTokenUsage(String jobId);

  boolean wakeupTask(String jobId);


}
