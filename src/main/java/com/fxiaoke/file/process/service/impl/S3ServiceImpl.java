package com.fxiaoke.file.process.service.impl;

import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.file.process.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.domain.api.R;
import com.fxiaoke.file.process.domain.api.remote.bigfile.FileMetaEntity;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.service.S3Service;
import com.fxiaoke.file.process.utils.CallUtil;
import com.fxiaoke.file.process.utils.FileTypeUtil;
import com.fxiaoke.file.process.utils.FilenameUtil;
import com.fxiaoke.file.process.utils.SampleUUID;
import com.fxiaoke.s3.S3Client;
import com.fxiaoke.s3.domain.model.Download;
import com.fxiaoke.s3.domain.model.GeneratePresignedDownloadUrl;
import com.fxiaoke.s3.domain.model.Upload;
import com.fxiaoke.s3.impl.AliOSSClient;
import com.google.common.base.Strings;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j(topic = "S3ServiceImpl")
public class S3ServiceImpl implements S3Service {

  private static final String MODULE = "S3ServiceImpl";

  private final S3Client s3Client;
  private final OkHttpSupport okHttpSupport;
  private final CmsPropertiesConfig config;

  public S3ServiceImpl(OkHttpSupport httpClientSupport, CmsPropertiesConfig cmsPropertiesConfig) {
    this.okHttpSupport = httpClientSupport;
    this.s3Client = new AliOSSClient();
    this.config = cmsPropertiesConfig;
  }

  @Override
  public String uploadToS3(String bucketRootDir, Path filePath) {
    Upload.Arg arg = new Upload.Arg();
    arg.setS3User(config.getS3InsideUser());
    try (InputStream stream = Files.newInputStream(filePath)) {
      long contentLength = Files.size(filePath);
      arg.setContentLength(contentLength);
      String extension = FilenameUtil.getFileExtension(filePath);
      arg.setObjectKey(generateObjectKey(bucketRootDir, extension));
      arg.setContentType(FileTypeUtil.getMime(extension));
      log.debug("uploadToS3 arg:{}", arg);
      arg.setContent(stream);
      return s3Client.upload(arg).getObjectKey();
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".uploadToS3", filePath);
    }
  }

  @Override
  public String getPreSignUrl(String objectKey,String process) {
    GeneratePresignedDownloadUrl.Arg arg = new GeneratePresignedDownloadUrl.Arg();
    arg.setS3User(config.getS3User());
    arg.setObjectKey(objectKey);
    arg.setExpires(config.getS3PreSignUrlExpires());
    arg.setProcess(process);
    try {
      log.debug("getPreSignUrl arg:{}", arg);
      return s3Client.generatePresignedDownloadUrl(arg);
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".getPreSignUrl", objectKey);
    }
  }

  @Override
  public long getBigFileSize(String ea, String path) {
    FileMetaEntity fileMeta = getBigFileMeta(ea, path);
    return fileMeta.getSize();
  }

  @Override
  public InputStream downloadBigFile(String ea, String path) {
    FileMetaEntity fileMeta = getBigFileMeta(ea, path);
    String objectKey = fileMeta.getObjectKey();
    Download.Arg arg = new Download.Arg();
    arg.setS3User(config.getBigFileS3User());
    arg.setObjectKey(objectKey);
    return s3Client.download(arg);
  }

  private FileMetaEntity getBigFileMeta(String ea, String path) {
    log.info("getBigFileMeta ea:{}, path:{}", ea, path);
    String requestUrl = String.format(config.getFindBigFileMetaUrl(), ea, path);
    String resultStr = CallUtil.get(okHttpSupport, requestUrl);
    R<FileMetaEntity> fileMetaEntityR = CallUtil.formJson(resultStr, FileMetaEntity.class);
    if (!fileMetaEntityR.isSuccess() || fileMetaEntityR.getData() == null) {
      throw new BaseException(400, "File not found",
          MODULE + ".getBigFileMeta", ea, path);
    }
    log.info("getBigFileMeta success, ea:{}, path:{}, meta:{}", ea, path,
        fileMetaEntityR.getData());
    return fileMetaEntityR.getData();
  }

  private static String generateObjectKey(String rootDir, String extension) {
    StringBuilder objectKey = new StringBuilder(64);

    LocalDateTime now = LocalDateTime.now();
    String year = ofPatternToStr(now, "yyyy");
    String month = ofPatternToStr(now, "MM");
    String day = ofPatternToStr(now, "dd");
    String hours = ofPatternToStr(now, "HH");

    objectKey.append(rootDir).append('/')
        .append(year).append('/')
        .append(month).append('/')
        .append(day).append('/')
        .append(hours).append('/')
        .append(SampleUUID.getUUID());

    if (!Strings.isNullOrEmpty(extension)) {
      objectKey.append('.').append(extension);
    }

    return objectKey.toString();
  }

  private static String ofPatternToStr(LocalDateTime now, String pattern) {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
    return now.format(formatter);
  }
}
