package com.fxiaoke.file.process.service.options;

import com.fxiaoke.file.process.config.AsrConfig;
import com.fxiaoke.file.process.domain.constants.AsyncTaskType;
import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.model.AsyncTaskReq;
import com.fxiaoke.file.process.domain.model.AsyncTaskRes;
import com.fxiaoke.file.process.service.FileService;
import com.fxiaoke.file.process.utils.SecureCryptUtils;
import com.fxiaoke.file.process.utils.SignatureUtil;
import com.tencentcloudapi.asr.v20190614.AsrClient;
import com.tencentcloudapi.asr.v20190614.models.CreateRecTaskRequest;
import com.tencentcloudapi.asr.v20190614.models.CreateRecTaskResponse;
import com.tencentcloudapi.asr.v20190614.models.Task;
import java.nio.file.Path;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RefreshScope
public class AsrOptions {

  private static final String MODULE = "AsrOptions";

  private final AsrClient asrClient;
  private final AsrConfig asrConfig;
  private final FileService fileService;

  public AsrOptions(AsrClient asrClient, AsrConfig asrConfig, FileService fileService) {
    this.asrClient = asrClient;
    this.asrConfig = asrConfig;
    this.fileService = fileService;
  }

  /**
   * 异步任务处理方法，处理语音转文本的请求
   *
   * @param req 异步任务请求
   * @return 异步任务响应
   * @throws BaseException 如果处理过程中发生错误
   */
  public AsyncTaskRes subTask(AsyncTaskReq req) throws BaseException {
    try {
      // 调用视觉模型识别图片中的文本
      log.info("ASR TO MARKDOWN Start, EA: {},Filename:{}", req.getEa(), req.getFileName());
      Path filePath = req.getFilePath();
      String objectKey = fileService.uploadLocalFileToS3(Constants.S3_BUCKET_ROOT_DIR_VL_OCR, filePath);
      log.info("ASR TO MARKDOWN Process, ObjectKey: {}", objectKey);
      String s3PreSignUrl = fileService.getS3PreSignUrl(objectKey,null);

      CreateRecTaskRequest recReq = toRecTaskReq(req, s3PreSignUrl);
      CreateRecTaskResponse recRsp = asrClient.CreateRecTask(recReq);

      String requestId = recRsp.getRequestId();
      Task recRspData = recRsp.getData();
      Long taskId = recRspData.getTaskId();
      AsyncTaskRes recTaskRes = toRecTaskRes(req, taskId, requestId);
      log.info("ASR TO MARKDOWN End, EA: {}, Filename: {}, TaskId: {}, RequestId: {}",
          req.getEa(), req.getFileName(), taskId, requestId);
      return recTaskRes;
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".toImageMarkdown", req);
    }
  }

  private AsyncTaskRes toRecTaskRes(AsyncTaskReq req, Long taskId, String requestId) {
    AsyncTaskRes recRes = new AsyncTaskRes();
    recRes.setJobId(req.getJobId());

    recRes.setEa(req.getEa());
    recRes.setEmployeeId(recRes.getEmployeeId());

    recRes.setRequestId(requestId);
    recRes.setTaskId(String.valueOf(taskId));
    recRes.setTaskType(AsyncTaskType.ASR_TENCENT);
    return recRes;
  }

  private CreateRecTaskRequest toRecTaskReq(AsyncTaskReq req, String s3PreSignUrl) {
    CreateRecTaskRequest recReq = new CreateRecTaskRequest();
    recReq.setSourceType(Constants.SOURCE_TYPE_URL);
    recReq.setUrl(s3PreSignUrl);
    recReq.setEngineModelType(Constants.DEFAULT_REC_ENGINE);
    recReq.setChannelNum(Constants.CHANNEL_NUM_ONE);
    // 双声道时这几个只能用默认值，否则腾讯会报400错误
    // 是否开启说话人分离 0：不开启；
    recReq.setSpeakerDiarization(Constants.SpeakerDiarization);
    // 说话人分离人数, 需配合开启说话人分离使用，不开启无效，取值范围：0-10，0：自动分离（最多分离出20个人）
    recReq.setSpeakerNumber(Constants.SpeakerNumber);
    // 识别结果返回样式 0：基础识别结果（仅包含有效人声时间戳，无词粒度的详细识别结果）
    recReq.setResTextFormat(Constants.ResTextFormat);
    String callBackUrl = generateCallBackUrl(req);
    log.info("ASR TO MARKDOWN Process, CallBackUrl: {}", callBackUrl);
    recReq.setCallbackUrl(callBackUrl);
    return recReq;
  }

  private String generateCallBackUrl(AsyncTaskReq req) {
    String jobId = req.getJobId();
    String sign = SignatureUtil.getSignatureWithHmacSha1(jobId);
    return String.format(asrConfig.getCallBackUrlTemplate(),
        jobId, sign);
  }

}
