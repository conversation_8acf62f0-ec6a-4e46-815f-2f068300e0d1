package com.fxiaoke.file.process.service.options;

import com.fxiaoke.file.process.domain.constants.LineSeparator;
import com.fxiaoke.file.process.service.FileService;
import com.fxiaoke.file.process.utils.FilenameUtil;
import com.fxiaoke.file.process.utils.GuardUtil;
import com.fxiaoke.file.process.utils.UrlStrUtil;
import com.google.common.base.Splitter;
import com.vladsch.flexmark.html2md.converter.FlexmarkHtmlConverter;
import com.vladsch.flexmark.html2md.converter.HtmlMarkdownWriter;
import com.vladsch.flexmark.html2md.converter.HtmlNodeConverterContext;
import com.vladsch.flexmark.html2md.converter.HtmlNodeRenderer;
import com.vladsch.flexmark.html2md.converter.HtmlNodeRendererHandler;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.nodes.Element;

/**
 * 自定义HTML节点渲染器，用于flexmark HTML到Markdown转换
 * 提取HTML中的图片信息并下载到本地写入到Markdown文件中
 */
@Slf4j
public class HtmlOptionsNodeRenderer implements HtmlNodeRenderer {

  private final String ea;
  private final int employeeId;
  private final String saveDir;
  private final FileService fileService;

  public HtmlOptionsNodeRenderer(String ea, int employeeId, String saveDir,
      FileService fileService) {
    this.ea = ea;
    this.employeeId = employeeId;
    this.saveDir = saveDir;
    this.fileService = fileService;
  }

  @Override
  public Set<HtmlNodeRendererHandler<?>> getHtmlNodeRendererHandlers() {

    Set<HtmlNodeRendererHandler<?>> handlers = new HashSet<>();

    handlers.add(new HtmlNodeRendererHandler<>(FlexmarkHtmlConverter.IMG_NODE, Element.class,
        this::imgNodeRenderer));
    
    // 添加BR标签的处理器，将<br>标签转换为换行符
    handlers.add(new HtmlNodeRendererHandler<>("br", Element.class,
        this::brNodeRenderer));

    return handlers;
  }

  private void imgNodeRenderer(Element element, HtmlNodeConverterContext context,
      HtmlMarkdownWriter markdownWriter) {

    String src = element.attr("src");
    String alt = element.hasAttr("alt") ? element.attr("alt") : "";
    String title = element.hasAttr("title") ? element.attr("title") : "";

    log.debug("HTML Node Renderer - Image: src={}, alt={}, title={}", src, alt, title);
    handlePreserveMode(src, alt, title, markdownWriter);
  }

  private void handlePreserveMode(String src, String alt, String title,
      HtmlMarkdownWriter markdownWriter) {
    // 如果src为空或只包含空格，直接返回
    if (src == null || src.trim().isEmpty()) {
      return;
    }
    if (src.startsWith("http://") || src.startsWith("https://")) {
      Optional<String> imageRelativePath = downloadImage(src);
      imageRelativePath.ifPresent(s -> syntheticMarkdownImageTag(markdownWriter, alt, title, s));
    }
  }

  private void syntheticMarkdownImageTag(HtmlMarkdownWriter markdownWriter, String alt,
      String title, String src) {
    markdownWriter.append(LineSeparator.UNIX.getLine());

    StringBuilder imgTag = new StringBuilder();
    imgTag.append("![");
    if (!alt.isEmpty()) {
      imgTag.append(alt.replace("[", "\\[").replace("]", "\\]"));
    }
    imgTag.append("](").append(src);
    if (!title.isEmpty()) {
      imgTag.append(" \"").append(title.replace("\"", "\\\"")).append("\"");
    }
    imgTag.append(")");

    String imgTagStr = imgTag.toString();
    markdownWriter.append(imgTagStr);
    markdownWriter.append(LineSeparator.UNIX.getLine());
    log.debug("HTML Node Renderer - Synthetic Image Tag: {}", imgTagStr);
  }

  private Optional<String> downloadImage(String src) {

    // 从URL中解析文件Path
    Optional<String> pathOpt = UrlStrUtil.parseUrl(src);
    if (pathOpt.isEmpty()) {
      return Optional.empty();
    }

    String pathOrToken = pathOpt.get();
    String currentEa = ea;
    int currentEmployeeId = employeeId;

    log.debug("HTML Node Renderer - Parse Url, EA: {}, PathOrToken: {}", currentEa, pathOpt.get());

    // 判断文件Path是否是加密Token
    if (!pathOrToken.startsWith("N_")) {

      Optional<String> decryTextOpt = GuardUtil.decry(pathOrToken);
      if (decryTextOpt.isEmpty()) {
        return Optional.empty();
      }

      String decryText = decryTextOpt.get();
      List<String> authCompositionList = Splitter.on("$").splitToList(decryText);
      if (authCompositionList.isEmpty() || authCompositionList.size() < 5) {
        return Optional.empty();
      }

      currentEa = authCompositionList.getFirst();
      currentEmployeeId = Integer.parseInt(authCompositionList.get(1));
      pathOrToken = authCompositionList.get(4);

      log.info("HTML Node Renderer - Decrypted Token, EA: {},Path: {}", currentEa,pathOrToken);
      // 判断解密后的Token是否是N文件类型
      if (!pathOrToken.startsWith("N_")) {
        return Optional.empty();
      }

    }

    // 下载到本地目录
    Optional<String> cacheFilePathOpt = fileService.downloadNFile(currentEa, currentEmployeeId,
        pathOrToken, saveDir);
    cacheFilePathOpt.ifPresent(
        FilePath -> log.debug("HTML Node Renderer - Download File, FilePath: {}",FilePath)
    );
    return cacheFilePathOpt.map(FilenameUtil::getName);
  }

  /**
   * 处理BR标签，将其转换为换行符
   */
  private void brNodeRenderer(Element element, HtmlNodeConverterContext context,
      HtmlMarkdownWriter markdownWriter) {
    // 添加换行符
    markdownWriter.append(LineSeparator.UNIX.getLine());
  }

}
