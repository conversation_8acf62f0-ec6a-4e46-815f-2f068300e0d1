package com.fxiaoke.file.process.service.options;

import com.fxiaoke.file.process.client.ArkClient;
import com.fxiaoke.file.process.client.MistralClient;
import com.fxiaoke.file.process.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.domain.api.remote.mistral.MistralOcrRequest;
import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.domain.constants.FileType;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.model.ImageDimension;
import com.fxiaoke.file.process.domain.model.MarkdownImgInfo;
import com.fxiaoke.file.process.domain.model.VisCompConfig;
import com.fxiaoke.file.process.domain.model.ToMdParams;
import com.fxiaoke.file.process.domain.model.ToMdResult;
import com.fxiaoke.file.process.domain.model.Usages;
import com.fxiaoke.file.process.domain.model.VisCompRes;
import com.fxiaoke.file.process.service.FileService;
import com.fxiaoke.file.process.utils.FilenameUtil;
import com.fxiaoke.file.process.utils.ImageBaseUtil;
import com.fxiaoke.file.process.utils.MarkDownUtils;
import com.fxiaoke.file.process.utils.MarkdownFormatUtil;
import java.awt.image.BufferedImage;
import java.io.BufferedWriter;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import javax.imageio.ImageIO;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.io.RandomAccessReadBufferedFile;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RefreshScope
public class PDFOptions extends DocOptions {

  private static final String MODULE = "PDFOptions";

  private final MistralClient mistralClient;

  public PDFOptions(ArkClient arkClient,
      FileService fileService,
      CmsPropertiesConfig cmsPropertiesConfig,
      MistralClient mistralClient) {
    super(arkClient, fileService, cmsPropertiesConfig);
    this.mistralClient = mistralClient;
  }

  public int getPageCount(Path filePath) {
    try (RandomAccessReadBufferedFile randomAccessReadBufferedFile = new RandomAccessReadBufferedFile(
        filePath.toFile()); PDDocument document = Loader.loadPDF(randomAccessReadBufferedFile)) {
      return document.getNumberOfPages();
    } catch (Exception e) {
      throw new BaseException(e, 400, MODULE + ".getPageCount",
          "PDF file encrypted or corrupted or version compatibility" + filePath);
    }
  }

  /**
   * 将PDF转换为Markdown文本格式
   * @param params 转换参数，包括文件路径、文件名等
   * @return 转换结果，包含页数、文件名和Markdown文件路径
   */
  protected ToMdResult toTextMarkdown(ToMdParams params) {
    try {
      // 获取页码
      int pageCount = getPageCount(params.getFilePath());
      log.info("PDF To Markdown Start, Page Count: {}", pageCount);
      // 上传文件到对象存储
      String downloadUrl = fileService.getPreSignUrl(Constants.S3_BUCKET_ROOT_DIR_MISTRAL_OCR,
          params.getFilePath());
      log.info("PDF To Markdown Process, Sign URL: {}", downloadUrl);
      // 根据源文件存储路径生成markdown文件路径
      Path mdFilePath = params.generatMdFilePath();
      // 调用Mistral OCR接口进行文件转换
      VisCompConfig visCompConfig = params.getVisCompConfig();
      MistralOcrRequest mistralOcrRequest = toMistralOcrRequest(downloadUrl, mdFilePath,
          visCompConfig.isOcr(), visCompConfig.getIgnoreMinImagePixel());
      mistralClient.performMistralOcr(mistralOcrRequest);
      Usages usages = Usages.ofMistralOcr(pageCount);
      log.info("PDF To Markdown End, Mistral-OCR Process Total Page: {}, Usages:{}", pageCount,
          usages);
      return new ToMdResult(pageCount, params.getFileName(), mdFilePath, usages);
    } catch (BaseException e) {
      throw e;
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".toTextMarkdown", params);
    }
  }

  protected ToMdResult toImageMarkdown(ToMdParams params) {
    try {

      // 将PDF转换为图片并生成Markdown文件
      Path mdFilePath = params.generatMdFilePath();
      List<MarkdownImgInfo> markdownImgInfos = processPDFToMarkdown(params.getFilePath(), mdFilePath);
      log.info("PDF To Markdown Start, To Images: {}", mdFilePath);

      // 调用视觉模型识别图片中的文本
      VisCompConfig visCompConfig = params.getVisCompConfig();
      List<VisCompRes<MarkdownImgInfo>> visCompRes =
          params.isHighPerformance() ? visCompResHp(visCompConfig, markdownImgInfos)
              : visCompRes(visCompConfig, markdownImgInfos);
      log.info("PDF To Markdown Process, Total Vl: {}",visCompRes.size());

      // 统计识别结果的Token用量
      Usages usages = accumulateUsage(visCompConfig,visCompRes);
      log.info("PDF To Markdown Process, Total Usages: {}", usages);

      // 将识别结果写入Markdown文件并清洗无用标签、内容
      Path finalMdFilePath = MarkDownUtils.replaceImageTags(mdFilePath, visCompRes, FileType.PDF);
      log.info("PDF To Markdown End, Final Md File: {}", finalMdFilePath);

      return new ToMdResult(markdownImgInfos.size(), params.getFileName(), finalMdFilePath, usages);
    } catch (BaseException e) {
      throw e;
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".toImageMarkdown", params);
    }
  }

  public List<MarkdownImgInfo> processPDFToMarkdown(Path filePath, Path mdFilePath) {

    try (RandomAccessReadBufferedFile randomAccessReadBufferedFile = new RandomAccessReadBufferedFile(filePath);
        PDDocument document = Loader.loadPDF(randomAccessReadBufferedFile)) {

      int pageCount = document.getNumberOfPages();
      PDFRenderer pdfRenderer = new PDFRenderer(document);
      pdfRenderer.setSubsamplingAllowed(true);
      List<MarkdownImgInfo> recognizeInfos = new ArrayList<>();

      try (BufferedWriter writer = Files.newBufferedWriter(mdFilePath, StandardCharsets.UTF_8)) {
        int currentLineNumber = 0;

        for (int pdfIndex = 0; pdfIndex < pageCount; pdfIndex++) {
          // 生成图片路径
          String imagePath = FilenameUtil.resolveSibling(filePath, String.valueOf(pdfIndex + 1),
              FileType.PNG.getFileType());

          // 计算目标DPI
          PDPage page = document.getPage(pdfIndex);
          PDRectangle cropBox = page.getCropBox();
          float width = cropBox.getWidth();
          float height = cropBox.getHeight();
          ImageDimension targetDim = ImageBaseUtil.getScopeWH(width, height, Constants.DCI_2K);

          // 计算所需DPI以达到目标尺寸
          float dpi = Math.min((targetDim.getWidth() / width) * 72,
              (targetDim.getHeight() / height) * 72);

          // 直接用计算好的DPI渲染图片
          BufferedImage image = pdfRenderer.renderImageWithDPI(pdfIndex, dpi);
          ImageIO.write(image, "PNG", new File(imagePath));

          // 写入Markdown图片标记
          String imageName = FilenameUtil.getName(imagePath);
          String imageTag = String.format("![第%d页](%s)", pdfIndex + 1, imageName);
          writer.write(imageTag);
          writer.newLine();
          currentLineNumber++;

          // 创建图片识别信息
          MarkdownImgInfo imgInfo = new MarkdownImgInfo();
          imgInfo.setIndex(pdfIndex + 1);
          imgInfo.setImagePath(Path.of(imagePath));
          imgInfo.setOriginalTag(imageTag);
          imgInfo.setLineNumber(currentLineNumber);
          imgInfo.setStartIndex(0);
          imgInfo.setEndIndex(imageTag.length());
          recognizeInfos.add(imgInfo);

          if (pdfIndex < pageCount - 1) {
            writer.write(MarkdownFormatUtil.horizontalRule());
            writer.newLine();
            currentLineNumber++; // 添加这行
          }
        }
      }
      return recognizeInfos;
    } catch (Exception e) {
      throw new BaseException(e, 400, MODULE + ".processPDFToMarkdown", filePath);
    }
  }

  private MistralOcrRequest toMistralOcrRequest(String downloadUrl, Path mdFilePath, boolean ocr,
      Integer ignoreMinImagePixel) {
    MistralOcrRequest mistralOcrRequest = new MistralOcrRequest();
    mistralOcrRequest.setModel(Constants.MISTRAL_OCR_MODEL);
    MistralOcrRequest.Document document = new MistralOcrRequest.Document();
    document.setType(Constants.MISTRAL_OCR_DOCUMENT_SOURCE_TYPE);
    document.setDocumentUrl(downloadUrl);
    mistralOcrRequest.setDocument(document);
    if (ocr) {
      mistralOcrRequest.setIncludeImageBase64(true);
      mistralOcrRequest.setImageMinSize((int) Math.sqrt(ignoreMinImagePixel));
    } else {
      mistralOcrRequest.setIncludeImageBase64(false);
      mistralOcrRequest.setImageMinSize(Integer.MAX_VALUE);
    }
    mistralOcrRequest.setMdFilePath(mdFilePath);
    return mistralOcrRequest;
  }
}
