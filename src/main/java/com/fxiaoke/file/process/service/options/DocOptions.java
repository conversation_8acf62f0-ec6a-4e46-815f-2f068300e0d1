package com.fxiaoke.file.process.service.options;

import com.fxiaoke.file.process.client.ArkClient;
import com.fxiaoke.file.process.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.domain.model.MarkdownImgInfo;
import com.fxiaoke.file.process.domain.model.VisCompConfig;
import com.fxiaoke.file.process.domain.model.ToMdParams;
import com.fxiaoke.file.process.domain.model.ToMdResult;
import com.fxiaoke.file.process.domain.model.Usages;
import com.fxiaoke.file.process.domain.model.VisCompReq;
import com.fxiaoke.file.process.domain.model.VisCompRes;
import com.fxiaoke.file.process.service.CommonOptions;
import com.fxiaoke.file.process.service.FileService;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class DocOptions implements CommonOptions {

  protected static final String MODULE = "DocOptions";

  protected final ArkClient arkClient;
  protected final FileService fileService;
  protected final CmsPropertiesConfig config;

  public DocOptions(ArkClient arkClient,FileService fileService,
      CmsPropertiesConfig cmsPropertiesConfig) {
    this.arkClient = arkClient;
    this.fileService = fileService;
    this.config = cmsPropertiesConfig;
  }

  @Override
  public ToMdResult toMarkdown(ToMdParams params) {
    VisCompConfig visCompConfig = params.getVisCompConfig();
    if (visCompConfig.isOcr()) {
      return toImageMarkdown(params);
    }
    return toTextMarkdown(params);
  }

  abstract ToMdResult toImageMarkdown(ToMdParams params);

  abstract ToMdResult toTextMarkdown(ToMdParams params);

  protected List<VisCompRes<MarkdownImgInfo>> visCompRes(VisCompConfig visCompConfig, List<MarkdownImgInfo> markdownImgInfos) {

    log.info("VisCompRes Start, Image Count: {}", markdownImgInfos.size());
    List<VisCompReq<MarkdownImgInfo>> visCompReqs = new ArrayList<>(markdownImgInfos.size());

    for (MarkdownImgInfo info : markdownImgInfos) {
      VisCompReq<MarkdownImgInfo> visCompReq = VisCompReq.of(visCompConfig, info);
      Optional<VisCompReq<MarkdownImgInfo>> compReqOpt = fileService.uploadAndGeneratePreSignUrl(
          visCompReq);
      compReqOpt.ifPresent(visCompReqs::add);
    }
    log.info("VisCompReqs Process, S3 Process Count: {}", visCompReqs.size());
    List<VisCompRes<MarkdownImgInfo>> visCompRes = new ArrayList<>(visCompReqs.size());
    for (VisCompReq<MarkdownImgInfo> visCompReq : visCompReqs) {
      try {
        VisCompRes<MarkdownImgInfo> res = arkClient.visComp(visCompReq);
        visCompRes.add(res);
      } catch (Exception e) {
        log.warn("Vl recognize error, visCompReq: {}", visCompReq, e);
      }
    }
    log.info("VisCompRes End, visCompRes Count: {}", visCompRes.size());
    return  visCompRes;
  }

  protected List<VisCompRes<MarkdownImgInfo>> visCompResHp(VisCompConfig visCompConfig, List<MarkdownImgInfo> markdownImgInfos) {

    List<VisCompReq<MarkdownImgInfo>> visCompReqs = new  ArrayList<>(markdownImgInfos.size());
    for (MarkdownImgInfo info : markdownImgInfos) {
      VisCompReq<MarkdownImgInfo> visCompReq = VisCompReq.of(visCompConfig,info);
      visCompReqs.add(visCompReq);
    }

    // 并发的将本地 Markdown 文件中的图片上传到 S3 并生成预签名 URL
    visCompReqs = fileService.localMdFileToS3PreSignUrl(visCompReqs);

    // 并发的图片识别
    return arkClient.visComps(visCompReqs);
  }

  protected Usages accumulateUsage(VisCompConfig visCompConfig,
      List<VisCompRes<MarkdownImgInfo>> visCompRes) {
    Usages usages = new Usages(visCompConfig.getModel());
    // 累计计算 visCompRes 中 inputToken、outputToken、totalToken 的总值
    for (VisCompRes<MarkdownImgInfo> res : visCompRes) {
      usages.addUsage(res.getUsages());
    }
    return usages;
  }
}
