package com.fxiaoke.file.process.service.options;

import com.aspose.slides.BlobManagementOptions;
import com.aspose.slides.Flavor;
import com.aspose.slides.ICell;
import com.aspose.slides.IImage;
import com.aspose.slides.IShape;
import com.aspose.slides.IShapeCollection;
import com.aspose.slides.ISlide;
import com.aspose.slides.ISlideCollection;
import com.aspose.slides.ISlideSize;
import com.aspose.slides.ITable;
import com.aspose.slides.ITextFrame;
import com.aspose.slides.ImageFormat;
import com.aspose.slides.LoadFormat;
import com.aspose.slides.LoadOptions;
import com.aspose.slides.MarkdownExportType;
import com.aspose.slides.MarkdownSaveOptions;
import com.aspose.slides.NewLineType;
import com.aspose.slides.Presentation;
import com.aspose.slides.PresentationLockingBehavior;
import com.aspose.slides.SaveFormat;
import com.fxiaoke.file.process.client.ArkClient;
import com.fxiaoke.file.process.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.domain.constants.FileType;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.model.ImageDimension;
import com.fxiaoke.file.process.domain.model.MarkdownImgInfo;
import com.fxiaoke.file.process.domain.model.VisCompConfig;
import com.fxiaoke.file.process.domain.model.TableData;
import com.fxiaoke.file.process.domain.model.ToMdParams;
import com.fxiaoke.file.process.domain.model.ToMdResult;
import com.fxiaoke.file.process.domain.model.Usages;
import com.fxiaoke.file.process.domain.model.VisCompRes;
import com.fxiaoke.file.process.service.FileService;
import com.fxiaoke.file.process.utils.FilenameUtil;
import com.fxiaoke.file.process.utils.ImageBaseUtil;
import com.fxiaoke.file.process.utils.MarkDownUtils;
import com.fxiaoke.file.process.utils.MarkdownFormatUtil;
import java.awt.Dimension;
import java.awt.geom.Dimension2D;
import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.IntStream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SlidesOptions extends DocOptions {

  private static final String MODULE = "SlidesOptions";
  private static final String SLIDE_IMAGE_TAG_FORMAT = "![Slide %d](%s)";

  public SlidesOptions(ArkClient arkClient, FileService fileService,
      CmsPropertiesConfig cmsPropertiesConfig) {
    super(arkClient, fileService, cmsPropertiesConfig);
  }

  private BlobManagementOptions blobManagementOptions() {
    BlobManagementOptions options = new BlobManagementOptions();
    // 设置允许保存在内存中的二进制数据的最大大小(Aspose 默认600M,设置默认100M),超出部分将保存到临时文件中
    options.setMaxBlobsBytesInMemory(104857600L);
    // 允许在保存期间生成临时文件
    options.setTemporaryFilesAllowed(true);
    // 设置保存临时文件的根文件夹
    // options.setTempFilesRootPath(Constants.SLIDES_CACHE_FOLDER);
    // 设置在保存期间锁定文件的行为
    options.setPresentationLockingBehavior(PresentationLockingBehavior.KeepLocked);
    return options;
  }

  private LoadOptions loadOptions() {
    LoadOptions loadOptions = new LoadOptions(LoadFormat.Auto);
    loadOptions.setDefaultRegularFont("Times New Roman");
    loadOptions.setDefaultSymbolFont("Symbol");
    loadOptions.setDefaultAsianFont("SimSun");
    return loadOptions;
  }

  private MarkdownSaveOptions saveOptions() {
    MarkdownSaveOptions options = new MarkdownSaveOptions();
    // 指定 markdown 导出类型(Sequential-> 单独渲染所有项目、TextOnly->仅渲染文本、Visual-> 渲染所有项目_分组项目整体渲染)。
    options.setExportType(MarkdownExportType.TextOnly);
    // 指定 markdown 规范以转换表示形式。(支持多种风格样式如GitHub、CommonMark、ScholarlyMarkdown)
    options.setFlavor(Flavor.Github);
    // 设置换行符类型。
    options.setNewLineType(NewLineType.Mac);
    // 设置是否导出评论、注释。
    options.setShowComments(true);
    // 设置是否导出隐藏的幻灯片。
    options.setShowHiddenSlides(false);
    // 设置是否导出幻灯片编号。
    options.setShowSlideNumber(false);
    return options;
  }

  private Presentation init(Path filePath) {
    try {
      return new Presentation(filePath.toString(), loadOptions());
    } catch (Exception e) {
      throw new BaseException(e, 400,
          MODULE + ".init", "PPT file encrypted or corrupted or version compatibility", filePath);
    }
  }

  protected ToMdResult toTextMarkdown(ToMdParams params) {
    Presentation presentation = init(params.getFilePath());
    try {
      int pageCount = presentation.getSlides().size();
      log.info("Slides To Markdown Start, Page Count: {}", pageCount);
      Path mdFilePath = params.generatMdFilePath();
      presentation.save(mdFilePath.toString(), SaveFormat.Md, saveOptions());
      log.info("Slides To Markdown End, Page Count: {}", pageCount);

      Path finalMdFilePath = MarkDownUtils.clearNbsp(mdFilePath);

      return new ToMdResult(pageCount, params.getFileName(), finalMdFilePath, null);
    } catch (BaseException e) {
      throw e;
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".toTextMarkdown", params);
    } finally {
      presentation.dispose();
    }
  }

  protected ToMdResult toImageMarkdown(ToMdParams params) {
    Presentation presentation = init(params.getFilePath());
    try {
      // 获取幻灯片信息（页数、尺寸）
      int pageCount = presentation.getSlides().size();
      ISlideCollection slides = presentation.getSlides();
      Dimension dimension = calculateImageDimension(presentation);
      log.info("Slides To Image Markdown Start, Page Count: {}, Dimension: {}", pageCount, dimension);

      // 将幻灯片转换为图片、提取表格并生成Markdown文件
      Path mdFilePath = params.generatMdFilePath();
      List<MarkdownImgInfo> markdownImgInfos = processSlidesToMarkdown(slides,
          params.getFilePath(), mdFilePath, dimension, pageCount);
      log.info("Slides To Image Markdown Process, Total Images: {}", markdownImgInfos.size());

      // 调用视觉模型识别图片中的文本
      VisCompConfig visCompConfig = params.getVisCompConfig();
      List<VisCompRes<MarkdownImgInfo>> visCompRes = visCompRes(visCompConfig,
          markdownImgInfos);
      log.info("Slides To Image Markdown Process, Total Vl: {}", visCompRes.size());

      // 统计识别结果的Token用量
      Usages usages = accumulateUsage(visCompConfig,visCompRes);
      log.info("Slides To Image Markdown Process, Total Usages: {}", usages);

      // 将识别结果写入Markdown文件并清洗无用标签、内容
      Path finalMdFilePath = MarkDownUtils.replaceImageTags(mdFilePath, visCompRes, FileType.PPTX);
      log.info("Slides To Image Markdown Process, Final Md File: {}", finalMdFilePath);

      return new ToMdResult(markdownImgInfos.size(), params.getFileName(), finalMdFilePath, usages);
    } catch (BaseException e) {
      throw e;
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".toImageMarkdown", params);
    } finally {
      presentation.dispose();
    }
  }

  /**
   * 计算图片压缩后的尺寸
   *
   * @param presentation 演示文稿对象
   * @return 压缩后的图片尺寸
   */
  private Dimension calculateImageDimension(Presentation presentation) {
    ISlideSize slideSize = presentation.getSlideSize();
    Dimension2D size = slideSize.getSize();
    double height = size.getHeight();
    double width = size.getWidth();

    // 计算压缩后的尺寸，确保不超过最大分辨率限制
    ImageDimension compressingWH = ImageBaseUtil.getScopeWH(width, height, Constants.DCI_2K);
    return new Dimension(compressingWH.getWidth(), compressingWH.getHeight());
  }

  /**
   * 处理幻灯片并生成Markdown文件
   *
   * @param slides        幻灯片集合
   * @param sourceDocPath 源文档路径
   * @param mdFilePath    Markdown文件路径
   * @param dimension     图片尺寸
   * @param pageCount     幻灯片总数
   * @throws IOException 如果写入文件时发生错误
   */
  private List<MarkdownImgInfo> processSlidesToMarkdown(ISlideCollection slides,
      Path sourceDocPath, Path mdFilePath,
      Dimension dimension, int pageCount) throws IOException {
    List<MarkdownImgInfo> recognizeInfos = new ArrayList<>();
    try (BufferedWriter writer = Files.newBufferedWriter(mdFilePath, StandardCharsets.UTF_8)) {
      int currentLineNumber = 0; // 跟踪当前行号

      for (int slideIndex = 0; slideIndex < pageCount; slideIndex++) {

        ISlide slide = slides.get_Item(slideIndex);

        // 提取表格（至少2列）
        List<TableData> tableData = extractTables(slideIndex, slide);

        // 保存幻灯片图片并获取路径
        String imagePath = FilenameUtil.resolveSibling(sourceDocPath,
            String.valueOf(slideIndex + 1), FileType.PNG.getFileType());
        IImage image = slide.getImage(dimension);
        try {
          image.save(imagePath, ImageFormat.Png);

          // 写入图片链接并创建识别信息
          String imageName = FilenameUtil.getName(imagePath);
          String originalTag = String.format(SLIDE_IMAGE_TAG_FORMAT, slideIndex + 1, imageName);
          writer.write(originalTag);
          writer.newLine();
          currentLineNumber++; // 图片标签行

          writer.newLine(); // 添加空行
          currentLineNumber++; // 空行

          // 创建并添加识别信息
          MarkdownImgInfo imgInfo = new MarkdownImgInfo();
          imgInfo.setIndex(slideIndex + 1);
          imgInfo.setImagePath(Path.of(imagePath));
          imgInfo.setOriginalTag(originalTag);
          imgInfo.setLineNumber(currentLineNumber - 1); // 图片标签所在行号
          imgInfo.setStartIndex(0);
          imgInfo.setEndIndex(originalTag.length());
          recognizeInfos.add(imgInfo);

          // 写入表格数据，并获取写入的行数
          int tableLines = writeTableData(writer, tableData);
          currentLineNumber += tableLines; // 更新当前行号

        } finally {
          image.dispose();
        }

        // 添加页面分隔符（除最后一页外）
        if (slideIndex < pageCount - 1) {
          writer.write(MarkdownFormatUtil.horizontalRule());
          writer.newLine();
          currentLineNumber++; // 分隔符行
        }
      }
    }
    return recognizeInfos;
  }


  /**
   * 提取并写入表格数据
   *
   * @param writer Markdown写入器
   * @return 写入的行数
   * @throws IOException 如果写入文件时发生错误
   */
  private int writeTableData(BufferedWriter writer, List<TableData> tableData) throws IOException {
    int linesWritten = 0;

    // 写入表格数据
    if (!tableData.isEmpty()) {
      for (TableData table : tableData) {
        // 使用 TableData 的 toMarkdown 方法生成表格内容
        String markdown = table.toMarkdown();
        writer.write(markdown);
        // 计算表格内容的行数 - 直接从TableData对象计算而不是使用split
        linesWritten += table.getRows() + 1; // 表头行 + 分隔行 + 数据行

        writer.newLine();
        linesWritten++;
      }
    }

    return linesWritten;
  }

  /**
   * 提取幻灯片中的表格并从幻灯片中删除它们
   *
   * @param slide      幻灯片对象
   * @param slideIndex 幻灯片索引
   * @return 提取的表格数据列表
   */
  private List<TableData> extractTables(int slideIndex, ISlide slide) {
    // 1. 参数校验
    if (slide == null) {
      return Collections.emptyList();
    }

    List<TableData> tables = new ArrayList<>();
    IShapeCollection shapes = slide.getShapes();
    for (int shapeIndex = 0; shapeIndex < shapes.size(); shapeIndex++) {
      IShape shape = shapes.get_Item(shapeIndex);
      if (shape instanceof ITable table) {
        // 检查表格列数是否满足最小列数要求
        if (table.getColumns().size() < 2) {
          continue;
        }
        TableData tableData = processTable(table, slideIndex, tables.size());
        tables.add(tableData);
        shapes.remove(shape);
        // 因为删除了一个元素，所以索引需要回退
        shapeIndex--;
      }
    }
    return tables;
  }

  /**
   * 处理单个表格并转换为TableData
   */
  private TableData processTable(ITable table, int slideIndex, int tableIndex) {
    if (table.getColumns().size() < 2) {
      return null;
    }
    TableData tableData = new TableData(slideIndex + 1, tableIndex + 1);
    IntStream.range(0, table.getRows().size()).forEach(row -> {
      List<String> rowData = IntStream.range(0, table.getColumns().size())
          .mapToObj(col -> extractCellText(table, col, row)).toList();
      tableData.addRow(rowData);
    });

    return tableData;
  }

  /**
   * 提取单元格文本内容
   */
  private String extractCellText(ITable table, int col, int row) {
    try {
      return Optional.ofNullable(table.get_Item(col, row)).map(ICell::getTextFrame)
          .map(ITextFrame::getText).orElse("");
    } catch (Exception e) {
      return "";
    }
  }

}
