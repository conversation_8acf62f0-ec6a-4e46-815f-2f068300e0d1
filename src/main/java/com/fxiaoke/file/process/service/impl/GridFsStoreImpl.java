package com.fxiaoke.file.process.service.impl;

import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.service.GridFsStore;
import com.fxiaoke.file.process.utils.FileTypeUtil;
import com.fxiaoke.file.process.utils.FilenameUtil;
import com.github.mongo.support.DatastoreExt;
import com.mongodb.BasicDBObject;
import com.mongodb.gridfs.GridFS;
import com.mongodb.gridfs.GridFSDBFile;
import com.mongodb.gridfs.GridFSInputFile;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Slf4j(topic = "GridFsStoreImpl")
public class GridFsStoreImpl implements GridFsStore {

  private static final String MODULE = "GridFsStoreImpl";

  private final GridFS gridFS;

  private GridFS getGridFs() {
    return gridFS;
  }

  public GridFsStoreImpl(@Qualifier("dpsDataStore") DatastoreExt dpsDataStore) {
    this.gridFS = new GridFS(dpsDataStore.getDB(), Constants.GRID_FS_DEFAULT_BUCKET);
    log.info("GridFsStoreImpl initialized with bucket: {}", Constants.GRID_FS_DEFAULT_BUCKET);
  }

  @Override
  public String saveFile(Path fileLocalPath, String fileName, String extensions) {
    try (InputStream stream = Files.newInputStream(fileLocalPath)) {
      return saveFile(stream, fileName, extensions);
    } catch (IOException e) {
      throw new BaseException(e, 500, MODULE + ".saveFile", fileLocalPath);
    }
  }

  @Override
  public String saveFile(String content, String fileName, String extensions){
    try (InputStream inputStream = new ByteArrayInputStream(content.getBytes(StandardCharsets.UTF_8))) {
      return saveFile(inputStream, fileName, extensions);
    } catch (IOException e) {
      throw new BaseException(e, 500, MODULE + ".saveFile", content,fileName,extensions);
    }
  }

  @Override
  public String saveFile(InputStream inputStream, String fileName, String extensions) {
    try {
      GridFSInputFile localFile = getGridFs().createFile(inputStream);
      String mimeType = FileTypeUtil.getMime(extensions);
      localFile.setFilename(FilenameUtil.getName(fileName));
      localFile.setContentType(mimeType);
      localFile.save();
      Object id = localFile.getId();
      log.info("save GridFS file success, id:{},filename:{}", id, fileName);
      return id.toString();
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".saveFile", fileName);
    }
  }

  @Override
  public InputStream getFileById(String fileId) {
    ObjectId objectId = new ObjectId(fileId);
    GridFSDBFile gridFSDBFile = getGridFs().findOne(objectId);
    if (gridFSDBFile != null) {
      return gridFSDBFile.getInputStream();
    }
    throw new BaseException(400, "File not found", MODULE + ".getFile", fileId);
  }

  public InputStream getFileByName(String fileName) {
    GridFSDBFile gridFSDBFile = getGridFs().findOne(fileName);
    if (gridFSDBFile != null) {
      return gridFSDBFile.getInputStream();
    }
    throw new BaseException(400, "File not found", MODULE + ".getFile", fileName);
  }

  // 根据文件名判断文件是否存在
  public boolean isFileExist(String fileName) {
    GridFSDBFile gridFSDBFile = getGridFs().findOne(fileName);
    return gridFSDBFile != null;
  }

  /**
   * 删除指定日期之前的所有文件
   *
   * @param date 指定日期
   * @return 删除的文件数量
   */
  @Override
  public int deleteFilesBeforeDate(Date date) {
    int deletedCount = 0;
    try {
      // 创建查询条件
      BasicDBObject query = new BasicDBObject();
      query.put("uploadDate", new BasicDBObject("$lt", date));

      // 查找符合条件的文件
      List<GridFSDBFile> files = getGridFs().find(query);

      log.info("Found {} files to delete", files.size());

      // 记录开始时间
      long startTime = System.currentTimeMillis();

      // 删除文件
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      for (GridFSDBFile file : files) {
        String filename = file.getFilename();
        Date uploadDate = file.getUploadDate();
        long fileSize = file.getLength();

        // 删除文件
        getGridFs().remove(filename);
        deletedCount++;

        log.info("Deleted file: {}, upload time: {}, size: {} bytes", filename,
            sdf.format(uploadDate), fileSize);
      }

      // 计算总耗时
      long endTime = System.currentTimeMillis();
      log.info("Deletion completed. Deleted {} files in {} ms", deletedCount, (endTime - startTime));
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".deleteFilesBeforeDate", date);
    }
    return deletedCount;
  }

}
