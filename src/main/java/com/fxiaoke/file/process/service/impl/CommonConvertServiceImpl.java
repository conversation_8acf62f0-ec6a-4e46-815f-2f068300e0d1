package com.fxiaoke.file.process.service.impl;

import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.domain.constants.FileType;
import com.fxiaoke.file.process.domain.constants.VisCompDetail;
import com.fxiaoke.file.process.domain.constants.VisCompPrompt;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.model.AsyncTaskReq;
import com.fxiaoke.file.process.domain.model.AsyncTaskRes;
import com.fxiaoke.file.process.domain.model.ToMdParams;
import com.fxiaoke.file.process.domain.model.ToMdResult;
import com.fxiaoke.file.process.domain.model.VisCompConfig;
import com.fxiaoke.file.process.service.CommonConvertService;
import com.fxiaoke.file.process.service.CommonOptions;
import com.fxiaoke.file.process.service.options.AsrOptions;
import com.fxiaoke.file.process.service.options.CellsOptions;
import com.fxiaoke.file.process.service.options.HtmlOptions;
import com.fxiaoke.file.process.service.options.ImageOptions;
import com.fxiaoke.file.process.service.options.PDFOptions;
import com.fxiaoke.file.process.service.options.SlidesOptions;
import com.fxiaoke.file.process.service.options.WordsOptions;
import com.fxiaoke.file.process.utils.FilenameUtil;
import java.io.BufferedWriter;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class CommonConvertServiceImpl implements CommonConvertService {

  private final String MODULE = "CommonConvertServiceImpl";

  private final CellsOptions cellsOptions;
  private final PDFOptions pdfOptions;
  private final SlidesOptions slidesOptions;
  private final WordsOptions wordsOptions;
  private final HtmlOptions htmlOptions;
  private final ImageOptions imageOptions;
  private final AsrOptions asrOptions;

  private final VisCompConfig visCompConfig = new VisCompConfig(true,Constants.VL_DEFAULT_MODEL, VisCompDetail.HIGH,
      VisCompPrompt.COMMON_IMAGE_SYSTEM_PROMPT, VisCompPrompt.FULL_PAGE_IMAGE_USER_PROMPT,
      Constants.IGNORE_MIN_IMAGE_PIXEL);

  public CommonConvertServiceImpl(CellsOptions cellsOptions, PDFOptions pdfOptions,
      SlidesOptions slidesOptions, WordsOptions wordsOptions, HtmlOptions htmlOptions,
      ImageOptions imageOptions, AsrOptions asrOptions) {
    this.cellsOptions = cellsOptions;
    this.pdfOptions = pdfOptions;
    this.slidesOptions = slidesOptions;
    this.wordsOptions = wordsOptions;
    this.htmlOptions = htmlOptions;
    this.imageOptions = imageOptions;
    this.asrOptions = asrOptions;
  }

  private CommonOptions getOptions(FileType fileType) {
    return switch (fileType) {
      case XLS, XLSX -> cellsOptions;
      case PDF -> pdfOptions;
      case PPT, PPTX -> slidesOptions;
      case DOC, DOCX -> wordsOptions;
      case HTML -> htmlOptions;
      case JPG, JPEG, PNG, GIF, WEBP, BMP -> imageOptions;
      default -> throw new BaseException(400, "Unsupported file type", MODULE + ".getDocOptions",
          fileType);
    };
  }

  @Override
  public void ranksStream(Path filePath, boolean isRowMode, BufferedWriter writer)
      throws BaseException {
    cellsOptions.ranksStream(filePath, isRowMode, writer);
  }

  @Override
  public ToMdResult docToMd(ToMdParams params) throws BaseException {
    FileType fileType = FileType.of(FilenameUtil.getFileExtension(params.getFilePath()));
    CommonOptions commonOptions = getOptions(fileType);
    return commonOptions.toMarkdown(params);
  }

  @Override
  public List<ToMdResult> batchDocToMd(List<ToMdParams> params) throws BaseException {
    List<ToMdResult> toMdResults = new ArrayList<>();
    for (ToMdParams param : params) {
      FileType fileType = FileType.of(FilenameUtil.getFileExtension(param.getFilePath()));
      if (fileType==FileType.PDF){
        param.setVisCompConfig(visCompConfig);
        param.setHighPerformance(true);
      }
      CommonOptions commonOptions = getOptions(fileType);
      ToMdResult toMdResult = commonOptions.toMarkdown(param);
      toMdResults.add(toMdResult);
    }
    return toMdResults;
  }

  @Override
  public AsyncTaskRes subTask(AsyncTaskReq asyncTaskReq) throws BaseException {
    return asrOptions.subTask(asyncTaskReq);
  }
}
