package com.fxiaoke.file.process.service.impl;

import com.facishare.ai.api.client.TokenCountingResourceClient;
import com.facishare.ai.api.dto.BaseArgument;
import com.facishare.ai.api.dto.TokenCountingResourceQuery.Result;
import com.facishare.ai.api.dto.TokenCountingResourceReport;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.model.ToKenReport;
import com.fxiaoke.file.process.service.AsmService;
import com.fxiaoke.file.process.service.TokenUsagesService;
import org.springframework.stereotype.Component;

@Component
public class TokenUsagesServiceImpl implements TokenUsagesService {

  private static final String MODULE = "AsmServiceImpl";

  private final AsmService asmService;
  private final TokenCountingResourceClient client;

  public TokenUsagesServiceImpl(AsmService asmService,
      TokenCountingResourceClient tokenCountingResourceClient) {
    this.asmService = asmService;
    this.client = tokenCountingResourceClient;
  }

  @Override
  public boolean allowUsing(String ea, Integer employeeId) {
    int tenantId = asmService.getEid(ea);
    BaseArgument baseArgument = new BaseArgument();
    baseArgument.setTenantId(String.valueOf(tenantId));
    baseArgument.setUserId(String.valueOf(employeeId));
    try {
      Result result = client.queryTokenCounting(baseArgument);
      if (result != null && result.getSuccess()) {
        return result.getAllowUsing();
      }
      return false;
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".allowUsing", tenantId, employeeId);
    }
  }

  @Override
  public boolean report(ToKenReport toKenReport) {
    BaseArgument baseArgument = new BaseArgument();
    int tenantId = asmService.getEid(toKenReport.getEa());
    baseArgument.setTenantId(String.valueOf(tenantId));
    baseArgument.setUserId(String.valueOf(toKenReport.getEmployeeId()));

    TokenCountingResourceReport.Arg arg = new TokenCountingResourceReport.Arg();
    arg.setBusiness(toKenReport.getBusiness());
    arg.setApiName(toKenReport.getApiName());
    arg.setDisplayName(toKenReport.getDisplayName());

    arg.setModel(toKenReport.getModel());
    arg.setPromptTokens(toKenReport.getPromptTokens());
    arg.setCompletionTokens(toKenReport.getCompletionTokens());
    arg.setTotalTokens(toKenReport.getTotalTokens());
    arg.setPricingTokens(toKenReport.getPricingTokens());

    try {
      TokenCountingResourceReport.Result result = client.reportTokenCounting(baseArgument, arg);
      return result != null && result.getSuccess();
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".report", toKenReport);
    }
  }
}
