package com.fxiaoke.file.process.service.options;

import com.fxiaoke.file.process.client.ArkClient;
import com.fxiaoke.file.process.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.domain.constants.FileType;
import com.fxiaoke.file.process.domain.constants.VisCompDetail;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.model.VisCompConfig;
import com.fxiaoke.file.process.domain.model.ToMdParams;
import com.fxiaoke.file.process.domain.model.ToMdResult;
import com.fxiaoke.file.process.domain.model.MarkdownImgInfo;
import com.fxiaoke.file.process.domain.model.Usages;
import com.fxiaoke.file.process.domain.model.VisCompRes;
import com.fxiaoke.file.process.service.FileService;
import com.fxiaoke.file.process.service.VlService;
import com.fxiaoke.file.process.utils.MarkDownUtils;
import com.vladsch.flexmark.html2md.converter.FlexmarkHtmlConverter;
import com.vladsch.flexmark.html2md.converter.HtmlNodeRendererFactory;
import com.vladsch.flexmark.html2md.converter.LinkConversion;
import com.vladsch.flexmark.util.data.MutableDataSet;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;

@Component
@Slf4j(topic = "HtmlOptions")
public class HtmlOptions extends DocOptions {

  private static final String MODULE = "HtmlOptions";

  public HtmlOptions(ArkClient arkClient, FileService fileService,
      CmsPropertiesConfig cmsPropertiesConfig) {
    super(arkClient, fileService, cmsPropertiesConfig);
  }

  private MutableDataSet mutableDataSet(boolean ignoreImage) {
    MutableDataSet options = new MutableDataSet();
    // 设置使用 Markdown AST风格
    options.set(FlexmarkHtmlConverter.SETEXT_HEADINGS, false);
    // 不输出未知标签
    options.set(FlexmarkHtmlConverter.OUTPUT_UNKNOWN_TAGS, false);
    // 将 <br> 标签转换为换行符
    options.set(FlexmarkHtmlConverter.BR_AS_PARA_BREAKS, true);

    if (ignoreImage){
      // 设置忽略所有的 img 标签
      options.set(FlexmarkHtmlConverter.EXT_INLINE_IMAGE, LinkConversion.NONE);
    }

    return options;
  }

  private String convertHtmlToMarkdown(String htmlContent, MutableDataSet options) {
    return convertHtmlToMarkdown(htmlContent, options, null);
  }

  private String convertHtmlToMarkdown(String htmlContent, MutableDataSet options,HtmlNodeRendererFactory rendererFactory) {
    FlexmarkHtmlConverter converter;
    if (rendererFactory == null) {
      // 创建HTML转换器
      converter = FlexmarkHtmlConverter.builder(options).build();

    } else {
      // 创建HTML转换器并设置自定义的HTML节点渲染器工厂
      converter = FlexmarkHtmlConverter
          .builder(options)
          .htmlNodeRendererFactory(rendererFactory)
          .build();
    }
    // 将HTML转换为Markdown
    return converter.convert(htmlContent);
  }

  private HtmlNodeRendererFactory createHtmlNodeRendererFactory(ToMdParams params) {
    return (dataHolder) -> new HtmlOptionsNodeRenderer(params.getEa(), params.getEmployeeId(),
        params.getParentDir(), fileService);
  }

  @Override
  public ToMdResult toImageMarkdown(ToMdParams params) {

    try {
      String htmlContent = Files.readString(params.getFilePath(), StandardCharsets.UTF_8);
      // 设置Markdown转换选项
      MutableDataSet options = mutableDataSet(false);
      // 自定义HTML节点渲染器
      HtmlNodeRendererFactory rendererFactory = createHtmlNodeRendererFactory(params);
      // 将HTML转换为Markdown
      String markdown = convertHtmlToMarkdown(htmlContent, options, rendererFactory);

      Path mdFilePath = params.generatMdFilePath();
      try (BufferedWriter writer = Files.newBufferedWriter(mdFilePath, StandardCharsets.UTF_8)) {
        writer.write(markdown);
      }
      log.info("HTML to Markdown Start, FilePath: {}", mdFilePath);

      // 提取Markdown中所有图片信息
      List<MarkdownImgInfo> markdownImgInfos = MarkDownUtils.extractImages(mdFilePath);
      log.info("HTML to Markdown Process, ToTal Image: {}", markdownImgInfos.size());

      VisCompConfig visCompConfig = params.getVisCompConfig();
      visCompConfig.setDetail(VisCompDetail.LOW);
      // 调用视觉模型识别图片中的文本
      List<VisCompRes<MarkdownImgInfo>> visCompRes = visCompRes(visCompConfig,
          markdownImgInfos);
      log.info("HTML to Markdown Process, ToTal Vl: {}",visCompRes.size());

      // 累计识别结果的总Token用量
      Usages usages = accumulateUsage(visCompConfig,visCompRes);
      log.info("HTML to Markdown Process, Usages: {}", usages);

      // 将识别结果写入Markdown文件并清洗无用标签、内容
      Path finalMdFilePath = MarkDownUtils.replaceImageTags(mdFilePath, visCompRes, FileType.HTML);
      log.info("HTML to Markdown End, Final Md File: {}", finalMdFilePath);

      return new ToMdResult(1, params.getFileName(), finalMdFilePath, usages);
    } catch (IOException e) {
      throw new BaseException(e, 500, MODULE + ".toImageMarkdown", params);
    }
  }

  @Override
  public ToMdResult toTextMarkdown(ToMdParams params) {
    try {
      String htmlContent = Files.readString(params.getFilePath(), StandardCharsets.UTF_8);
      log.info("HTML to Markdown Start, Html Content Size: {}", htmlContent.length());
      MutableDataSet options = mutableDataSet(true);
      String markdown = convertHtmlToMarkdown(htmlContent, options);

      Path mdFilePath = params.generatMdFilePath();
      try (BufferedWriter writer = Files.newBufferedWriter(mdFilePath, StandardCharsets.UTF_8)) {
        writer.write(markdown);
      }
      log.info("HTML to Markdown End, Md File: {}", mdFilePath);

      Path finalMdFilePath = MarkDownUtils.clearBr(mdFilePath);

      return new ToMdResult(1, params.getFileName(), finalMdFilePath);
    } catch (IOException e) {
      throw new BaseException(e, 500, MODULE + ".toImageMarkdown", params);
    }
  }

  @Override
  public ToMdResult toMarkdown(ToMdParams params) throws BaseException {
    VisCompConfig visCompConfig = params.getVisCompConfig();
    if (visCompConfig.isOcr()) {
      return toImageMarkdown(params);
    }
    return toTextMarkdown(params);
  }
}
