package com.fxiaoke.file.process.manager;

import java.util.concurrent.Executor;

import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import org.springframework.beans.factory.DisposableBean;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;

@Component
@Slf4j(topic = "ThreadManager")
public class ThreadManager implements DisposableBean {

  private final ThreadPoolExecutor executor = createIOExecutor();
  private final AtomicBoolean isShutdown = new AtomicBoolean(false);

  public Executor getExecutor() {
    return executor;
  }

  /**
   * 创建一个高并发的IO线程池，适用于需要处理大量IO操作的场景。 线程数设置为CPU核心数的2倍，最大线程数设置为CPU核心数的8倍。 队列容量设置为4000，空闲线程存活时间为2分钟。
   */
  private ThreadPoolExecutor createIOExecutor() {
    int coreSize = Runtime.getRuntime().availableProcessors();
    // 对于高并发IO，可以设置更多线程
    int corePoolSize = coreSize * 2;
    // CPU核心数的8倍
    int maximumPoolSize = coreSize * 8;

    return new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 120L,
        // 2分钟空闲时间
        TimeUnit.SECONDS, new LinkedBlockingQueue<>(4000), // 更大的队列
        createIOThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());
  }

  /**
   * 创建一个自定义的线程工厂，用于创建IO密集型线程。 线程名称格式为 "TIW-1", "TIW-2", ...
   */
  private ThreadFactory createIOThreadFactory() {
    return new ThreadFactory() {
      private final AtomicInteger threadNumber = new AtomicInteger(1);

      @Override
      public Thread newThread(@NotNull Runnable runnable) {
        Thread thread = new Thread(runnable, "TIW-" + threadNumber.getAndIncrement());
        thread.setDaemon(false);
        return thread;
      }
    };
  }

  /**
   * Spring容器销毁时自动调用，实现优雅关机
   */
  @Override
  public void destroy() throws Exception {
    shutdownExecutor();
  }

  /**
   * 优雅关闭线程池
   */
  public void shutdownExecutor() {
    if (!isShutdown.compareAndSet(false, true)) {
      return; // 已经关闭过了，避免重复关闭
    }

    ThreadPoolExecutor executor = this.executor;
    if (!executor.isShutdown()) {
      log.info("开始关闭IO线程池，当前活跃线程数: {}, 队列任务数: {}", executor.getActiveCount(),
          executor.getQueue().size());

      try {
        // 第一步：停止接收新任务
        executor.shutdown();

        // 第二步：等待已提交任务完成，最多等待30秒
        if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
          log.info("等待超时，强制关闭IO线程池");
          executor.shutdownNow();

          // 第三步：等待强制关闭完成，再等待10秒
          if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
            log.info("IO线程池强制关闭失败，可能存在无法中断的任务");
          } else {
            log.info("IO线程池强制关闭完成");
          }
        } else {
          log.info("IO线程池优雅关闭完成");
        }

      } catch (InterruptedException e) {
        log.error("关闭IO线程池时被中断: {}", e.getMessage(), e);
        executor.shutdownNow();
        Thread.currentThread().interrupt();
      }
    }
  }

  /**
   * 获取线程池状态信息，用于监控
   */
  public String getThreadPoolStatus() {
    ;
    return String.format("线程池状态 - 核心线程数:%d, 活跃线程数:%d, 最大线程数:%d, "
            + "队列任务数:%d, 已完成任务数:%d, 总任务数:%d", executor.getCorePoolSize(),
        executor.getActiveCount(), executor.getMaximumPoolSize(), executor.getQueue().size(),
        executor.getCompletedTaskCount(), executor.getTaskCount());
  }

  /**
   * 检查线程池是否健康
   */
  public boolean isHealthy() {
    return !executor.isShutdown() && !executor.isTerminated();
  }
}
