package com.fxiaoke.file.process.manager;

import com.fxiaoke.file.process.config.ModelPricingPropertiesConfig;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.model.ModelPrice;
import org.springframework.stereotype.Component;

@Component
public class ModelPriceManager {

  private static final String MODULE = "ModelPriceManager";

  private final ModelPricingPropertiesConfig config;

  public ModelPriceManager(ModelPricingPropertiesConfig modelPricingPropertiesConfig) {
    this.config = modelPricingPropertiesConfig;
  }

  private static final int MAO_UNIT = 10; // 1元 = 10毛

  /**
   * 获取指定模型的别名
   *
   * @param modelName 模型名称
   * @return 模型别名
   * @throws IllegalArgumentException 当模型不存在时抛出异常
   */
  public String getRecordAlias(String modelName) {
    ModelPrice modelPrice = config.getModelPrice(modelName);
    if (modelPrice == null) {
      throw new BaseException(400, "model price not found: ", MODULE, modelName);
    }
    String alias = modelPrice.getAlias();
    if (alias == null || alias.isEmpty()) {
      return modelName;
    }
    return alias;
  }

  /**
   * 计算指定模型的token使用费用（元）
   *
   * @param modelName    模型名称
   * @param inputTokens  输入token数量
   * @param outputTokens 输出token数量
   * @return 总费用（元）
   * @throws IllegalArgumentException 当模型不存在时抛出异常
   */
  public double calculatePrice(String modelName, double inputTokens, double outputTokens) {
    ModelPrice modelPrice = config.getModelPrice(modelName);

    if (modelPrice == null) {
      throw new BaseException(400, "model price not found: ", MODULE, modelName);
    }

    double inputCost = (inputTokens / 1000.0) * modelPrice.getInputPrice();
    double outputCost = (outputTokens / 1000.0) * modelPrice.getOutputPrice();

    return inputCost + outputCost;
  }

  /**
   * 计算指定模型的token使用费用（1算粒≈1毛）
   *
   * @param modelName    模型名称
   * @param inputTokens  输入token数量
   * @param outputTokens 输出token数量
   * @return 总费用（毛）
   * @throws IllegalArgumentException 当模型不存在时抛出异常
   */
  public double getGranularCalculation(String modelName, long inputTokens, long outputTokens) {
    return calculatePrice(modelName, inputTokens, outputTokens) * MAO_UNIT;
  }

  public double getGranularCalculation(String modelName, double inputTokens, double outputTokens) {
    return calculatePrice(modelName, inputTokens, outputTokens) * MAO_UNIT;
  }

  /**
   * 将元转换为毛
   *
   * @param yuan 元为单位的金额
   * @return 毛为单位的金额
   */
  public static double yuanToMao(double yuan) {
    return yuan * MAO_UNIT;
  }

  /**
   * 将毛转换为元
   *
   * @param mao 毛为单位的金额
   * @return 元为单位的金额
   */
  public static double maoToYuan(double mao) {
    return mao / MAO_UNIT;
  }

  public static double tencentAsrDurationToInputTokens(double duration) {
    // 腾讯语音识别每秒消耗1000个token
    return duration * 1000;
  }

  public static double tencentAsrContentToOutputTokens(int contentLength) {
    // 腾讯语音识别每个字符消耗10个token
    return contentLength * 10;
  }

}
