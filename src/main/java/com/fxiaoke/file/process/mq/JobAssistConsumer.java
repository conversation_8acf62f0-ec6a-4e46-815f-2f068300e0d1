package com.fxiaoke.file.process.mq;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.file.process.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.domain.constants.JobStatus;
import com.fxiaoke.file.process.domain.entity.DocParseJob;
import com.fxiaoke.file.process.domain.mq.DocParseCompletedMsg;
import com.fxiaoke.file.process.service.ParseService;
import com.fxiaoke.file.process.utils.StrUtils;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import jakarta.annotation.PreDestroy;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

@Component
@Slf4j(topic = "JobConsumer")
public class JobAssistConsumer implements ApplicationListener<ContextRefreshedEvent> {

  private final AutoConfMQPushConsumer consumer;
  private final ParseService docParseService;

  public JobAssistConsumer(CmsPropertiesConfig cmsPropertiesConfig, ParseService docParseService) {
    String mqConfigName = cmsPropertiesConfig.getMqConfigName();
    String mqSection = cmsPropertiesConfig.getMqAssistSection();
    log.info("JobConsumer init start,configName={},mqSection={}", mqConfigName, mqSection);
    consumer = new AutoConfMQPushConsumer(mqConfigName, mqSection,
        (MessageListenerConcurrently) (msgs, context) -> processMessages(msgs));
    log.info("JobConsumer init end,configName={},mqSection={}", mqConfigName, mqSection);
    this.docParseService = docParseService;
  }

  private ConsumeConcurrentlyStatus processMessages(List<MessageExt> msgs) {

    for (MessageExt msg : msgs) {
      String tag = msg.getTags();
      if (StrUtils.isBlank(tag)) {
        log.warn("Skip message with empty tag");
        continue;
      }
      switch (tag) {
        case Constants.MQ_TAG_DOC_ASYNC_TASK -> processAsyncTaskMessage(msg);
        case Constants.MQ_TAG_DOC_TASK_WAKEUP ->  processTaskWakeupMessage(msg);
        case Constants.MQ_TAG_DOC_TOKEN_REPORT -> processTokenReportMessage(msg);
        default -> log.info("Non-service listen tag, skip. tag:{}", tag);
      }
    }
    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
  }

  private void processAsyncTaskMessage(MessageExt msg) {
    String jobId = JSON.parseObject(msg.getBody(), String.class);
    log.info("AsyncTask subTask Start, jobId:{}", jobId);
    Optional<DocParseJob> docParseJobOpt = docParseService.queryJob(jobId);
    if (docParseJobOpt.isEmpty()) {
      log.warn("Job not found, jobId:{}", jobId);
      return;
    }
    DocParseJob docParseJob = docParseJobOpt.get();
    log.info("Job found, jobId:{}, status:{}", jobId, docParseJob.getJobStatus());
    if (isJobProcessable(docParseJob)) {
      boolean asyncResult = docParseService.subTask(docParseJob);
      log.info("AsyncTask subTask End, jobId:{}, result:{}", jobId, asyncResult);
      return;
    }
    log.info("Job status not QUEUE/PREPROCESS, skip. jobId:{}, status:{}", jobId,
        docParseJob.getJobStatus());
  }

  private boolean isJobProcessable(DocParseJob job) {
    return job.getJobStatus() == JobStatus.QUEUE.getValue()
        || job.getJobStatus() == JobStatus.PREPROCESS.getValue();
  }

  private void processTaskWakeupMessage(MessageExt msg){
    DocParseCompletedMsg completedMsg = JSON.parseObject(msg.getBody(), DocParseCompletedMsg.class);
    log.info("Start task wakeup, message:{}", completedMsg);
    boolean wakeupResult = docParseService.wakeupTask(completedMsg.getJobId());
    log.info("Task wakeup completed, message:{}, result:{}", completedMsg, wakeupResult);
  }

  private void processTokenReportMessage(MessageExt msg) {
    DocParseCompletedMsg reportMsg = JSON.parseObject(msg.getBody(), DocParseCompletedMsg.class);
    log.info("Start token report, message:{}", reportMsg);
    boolean reportResult = docParseService.reportTokenUsage(reportMsg.getJobId());
    log.info("Token report completed, message:{}, result:{}", reportMsg, reportResult);
  }

  @PreDestroy
  public void close() {
    if (consumer != null) {
      consumer.close();
    }
  }

  @Override
  public void onApplicationEvent(@NotNull ContextRefreshedEvent event) {
    if (consumer != null && event.getApplicationContext().getParent() == null) {
      consumer.start();
    }
  }

}
