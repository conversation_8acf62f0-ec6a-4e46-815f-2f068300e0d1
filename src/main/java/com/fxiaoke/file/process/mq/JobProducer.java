package com.fxiaoke.file.process.mq;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.file.process.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.domain.constants.MessageDelayLevel;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.mq.DocParseCompletedMsg;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Component;

@Component
@Slf4j(topic = "JobProducer")
public class JobProducer {

  private final String MODULE = "JobProducer";

  private final String topic;
  private final AutoConfMQProducer producer;

  public JobProducer(CmsPropertiesConfig cmsPropertiesConfig) {
    String mqConfigName = cmsPropertiesConfig.getMqConfigName();
    String mqConvertSection = cmsPropertiesConfig.getMqConvertSection();
    log.info("JobProducer init start,configName={},mqSection={}", mqConfigName, mqConvertSection);
    producer = new AutoConfMQProducer(mqConfigName, mqConvertSection);
    topic = producer.getDefaultTopic();
    log.info("JobProducer init end,configName={},mqSection={}", mqConfigName, mqConvertSection);
  }

  public void sendJobMsg(String jobId, String tag) {
    try {
      log.info("send parse job message start, jobId:{}", jobId);
      producer.send(new Message(topic, tag, JSON.toJSONBytes(jobId)));
      log.info("send parse job message end, jobId:{}", jobId);
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE, jobId);
    }
  }

  /**
   * 发送原始 - 任务完成/失败通知消息，默认延迟级别为LEVEL_0
   * @param msg 完成消息
   * @param asyncCallbackMqTag 异步回调MQ标签
   */
  public void sendCompletedNotificationMsg(DocParseCompletedMsg msg, String asyncCallbackMqTag) {
    if (msg == null || asyncCallbackMqTag == null) {
      log.warn("Invalid message or tag: msg={}, tag={}", msg, asyncCallbackMqTag);
      return;
    }
    try {
      byte[] msgBytes = JSON.toJSONBytes(msg);

      // 发送完成/失败通知消息
      sendDelayMessage(topic, asyncCallbackMqTag, msgBytes, MessageDelayLevel.LEVEL_0);
      log.info("Sent completed notification message: msg={}, tag={}", msg, asyncCallbackMqTag);

      // 发送幂等任务唤醒消息
      sendDelayMessage(topic, Constants.MQ_TAG_DOC_TASK_WAKEUP, msgBytes, MessageDelayLevel.LEVEL_0);
      log.info("Sent task wakeup message: msg={}, tag={}", msg, Constants.MQ_TAG_DOC_TASK_WAKEUP);

      // 成功时发送Token上报消息
      if (msg.isSuccess()) {
        sendDelayMessage(topic, Constants.MQ_TAG_DOC_TOKEN_REPORT, msgBytes,
            MessageDelayLevel.LEVEL_0);
        log.info("Sent token report message: msg={}", msg);
      }
    } catch (Exception e) {
      log.error("Failed to send notification messages: msg={}, tag={}", msg, asyncCallbackMqTag, e);
      throw new BaseException(e, 500, MODULE + ".sendNotification", msg);
    }
  }

  public void sendIdempotentNotificationMsg(DocParseCompletedMsg msg, String asyncCallbackMqTag) {
    if (msg == null || asyncCallbackMqTag == null) {
      return;
    }
    try {
      byte[] msgBytes = JSON.toJSONBytes(msg);
      sendDelayMessage(topic, asyncCallbackMqTag, msgBytes, MessageDelayLevel.LEVEL_0);
      log.info("Sent idempotent notification message: msg={}, tag={}", msg, asyncCallbackMqTag);
    } catch (Exception e) {
      log.error("Failed to send idempotent notification message: msg={}, tag={}", msg, asyncCallbackMqTag, e);
      throw new BaseException(e, 500, MODULE + ".sendIdempotentNotification", msg);
    }
  }

  /**
   * 发送直接幂等 - 任务完成/失败通知消息，默认延迟级别为LEVEL_2
   * @param msg 完成消息
   * @param asyncCallbackMqTag 异步回调MQ标签
   */
  public void sendCompletedDelayNotificationMsg(DocParseCompletedMsg msg, String asyncCallbackMqTag){
    byte[] msgBytes = JSON.toJSONBytes(msg);
    sendDelayMessage(topic, asyncCallbackMqTag, msgBytes, MessageDelayLevel.LEVEL_2);
  }

  private void sendDelayMessage(String topic, String tag, byte[] body, MessageDelayLevel level) {
    Message message = new Message(topic, tag, body);
    if (level != MessageDelayLevel.LEVEL_0) {
      message.setDelayTimeLevel(level.getLevel());
    }
    producer.send(message);
  }

}
