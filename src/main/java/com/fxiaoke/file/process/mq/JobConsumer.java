package com.fxiaoke.file.process.mq;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.file.process.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.domain.constants.JobStatus;
import com.fxiaoke.file.process.domain.entity.DocParseJob;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.mq.DocParseCompletedMsg;
import com.fxiaoke.file.process.service.ParseService;
import com.fxiaoke.file.process.utils.StrUtils;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import jakarta.annotation.PreDestroy;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

@Component
@Slf4j(topic = "JobConsumer")
public class JobConsumer implements ApplicationListener<ContextRefreshedEvent> {

  private final AutoConfMQPushConsumer consumer;
  private final ParseService docParseService;

  public JobConsumer(CmsPropertiesConfig cmsPropertiesConfig, ParseService docParseService) {
    String mqConfigName = cmsPropertiesConfig.getMqConfigName();
    String mqConvertSection = cmsPropertiesConfig.getMqConvertSection();
    log.info("JobConsumer init start,configName={},mqSection={}", mqConfigName, mqConvertSection);
    consumer = new AutoConfMQPushConsumer(mqConfigName, mqConvertSection,
        (MessageListenerConcurrently) (msgs, context) -> processMessages(msgs));
    log.info("JobConsumer init end,configName={},mqSection={}", mqConfigName, mqConvertSection);
    this.docParseService = docParseService;
  }


  private ConsumeConcurrentlyStatus processMessages(List<MessageExt> msgs) {
    log.info("Start processing messages, count:{}", msgs.size());
    try {
      for (MessageExt msg : msgs) {
        String tag = msg.getTags();
        if (StrUtils.isBlank(tag)) {
          log.warn("Skip message with empty tag");
          continue;
        }
        if (tag.equals(Constants.MQ_TAG_DOC_PARSE_JOB_SUBMIT)) {
          processDocParseMessage(msg);
        } else {
          log.info("Non-service listen tag, skip. tag:{}", tag);
        }
      }
    } catch (BaseException e) {
      log.error("process job fail ,reason:{}", e.getReason(), e);
    } catch (Exception e) {
      log.error("Failed to process messages", e);
    }
    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
  }

  private void processDocParseMessage(MessageExt msg) {
    String jobId = JSON.parseObject(msg.getBody(), String.class);

    // 验证消费次数
    if (!isValidForProcessing(msg, jobId)) {
      return;
    }

    log.info("Start processing job submit message, jobId:{}", jobId);
    Optional<DocParseJob> docParseJobOpt = docParseService.queryJob(jobId);
    if (docParseJobOpt.isEmpty()) {
      log.warn("Job not found, jobId:{}", jobId);
      return;
    }

    DocParseJob docParseJob = docParseJobOpt.get();
    log.info("Job found, jobId:{}, status:{}", jobId, docParseJob.getJobStatus());
    if (isJobProcessable(docParseJob)) {
      docParseService.processJob(docParseJob);
    } else {
      log.info("Job status not QUEUE/PREPROCESS, skip. jobId:{}, status:{}", jobId,
          docParseJob.getJobStatus());
    }
  }

  private boolean isValidForProcessing(MessageExt msg, String jobId) {
    if (msg.getReconsumeTimes() >= 2) {
      log.warn("JobConsumer jobId:{} reconsume times[{}] exceed limit, skip", jobId,
          msg.getReconsumeTimes());
      return false;
    }
    return true;
  }

  private boolean isJobProcessable(DocParseJob job) {
    return job.getJobStatus() == JobStatus.QUEUE.getValue()
        || job.getJobStatus() == JobStatus.PREPROCESS.getValue();
  }

  @PreDestroy
  public void close() {
    if (consumer != null) {
      consumer.close();
    }
  }

  @Override
  public void onApplicationEvent(@NotNull ContextRefreshedEvent event) {
    if (consumer != null && event.getApplicationContext().getParent() == null) {
      consumer.start();
    }
  }

}
