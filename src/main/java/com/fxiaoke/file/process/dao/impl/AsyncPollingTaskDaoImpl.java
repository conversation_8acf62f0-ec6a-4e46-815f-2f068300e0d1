package com.fxiaoke.file.process.dao.impl;

import com.fxiaoke.file.process.dao.AsyncPollingTaskDao;
import com.fxiaoke.file.process.domain.constants.JobStatus;
import com.fxiaoke.file.process.domain.entity.AsyncPollingTask;
import com.fxiaoke.file.process.domain.entity.fieids.AsyncPollingTaskFieIds;
import com.fxiaoke.file.process.domain.model.AsyncTaskRes;
import com.github.mongo.support.DatastoreExt;
import java.util.Date;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Key;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Repository;

@Repository
public class AsyncPollingTaskDaoImpl implements AsyncPollingTaskDao {

  private final DatastoreExt dpsDataStore;

  public AsyncPollingTaskDaoImpl(DatastoreExt dpsDataStore) {
    this.dpsDataStore = dpsDataStore;
  }

  private DatastoreExt getDatastore() {
    return dpsDataStore;
  }

  private Query<AsyncPollingTask> getQuery() {
    return getDatastore().createQuery(AsyncPollingTask.class);
  }

  private Query<AsyncPollingTask> getQuery(String jobId) {
    return getQuery().field(AsyncPollingTaskFieIds.jobId)
        .equal(new ObjectId(jobId));
  }

  @Override
  public String create(AsyncTaskRes asyncTaskRes) {
    AsyncPollingTask asyncPollingTask = new AsyncPollingTask();
    asyncPollingTask.setJobId(asyncTaskRes.getJobId());
    asyncPollingTask.setEa(asyncTaskRes.getEa());
    asyncPollingTask.setEmployeeId(asyncTaskRes.getEmployeeId());
    asyncPollingTask.setTaskType(asyncTaskRes.getTaskType().getType());
    asyncPollingTask.setRequestId(asyncTaskRes.getRequestId());
    asyncPollingTask.setTaskId(asyncTaskRes.getTaskId());
    asyncPollingTask.setJobStatus(JobStatus.QUEUE.getValue());

    Date date = new Date();
    asyncPollingTask.setCreateTime(date);
    asyncPollingTask.setUpdateTime(date);
    Key<AsyncPollingTask> saveResult = getDatastore().save(asyncPollingTask);
    return saveResult.getId().toString();
  }

}
