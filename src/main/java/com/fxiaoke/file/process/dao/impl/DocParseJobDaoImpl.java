package com.fxiaoke.file.process.dao.impl;

import com.fxiaoke.file.process.dao.DocParseJobDao;
import com.fxiaoke.file.process.domain.constants.JobStatus;
import com.fxiaoke.file.process.domain.entity.DocParseJob;
import com.fxiaoke.file.process.domain.entity.fieids.DocParseJobFieIds;
import com.fxiaoke.file.process.domain.entity.model.DocBillingRecord;
import com.fxiaoke.file.process.domain.entity.model.JobParseArg;
import com.fxiaoke.file.process.domain.entity.model.JobRecords;
import com.fxiaoke.file.process.utils.StrUtils;
import com.github.mongo.support.DatastoreExt;
import com.mongodb.WriteResult;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Key;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.stereotype.Repository;

@Repository
public class DocParseJobDaoImpl implements DocParseJobDao {

  private final DatastoreExt dpsDataStore;

  public DocParseJobDaoImpl(DatastoreExt dpsDataStore) {
    this.dpsDataStore = dpsDataStore;
  }

  private DatastoreExt getDatastore() {
    return dpsDataStore;
  }

  private Query<DocParseJob> getQuery() {
    return getDatastore().createQuery(DocParseJob.class);
  }

  private Query<DocParseJob> getQuery(String jobId) {
    return getQuery().field(DocParseJobFieIds.jobId)
        .equal(new ObjectId(jobId));
  }

  private Query<DocParseJob> getCompleteJobQueryById(String jobId) {
    Query<DocParseJob> query = getQuery(jobId);
    query.criteria(DocParseJobFieIds.jobStatus)
         .equal(JobStatus.COMPLETED.getValue());
    return query;
  }

  private UpdateOperations<DocParseJob> getUpdate() {
    return getDatastore().createUpdateOperations(DocParseJob.class);
  }

  private String mergeNestedField(String parentField, String nestedField) {
    return parentField + "." + nestedField;
  }

  private UpdateOperations<DocParseJob> getUpdateJobStatusOperation(JobStatus status,
      String nestedField) {
    return getUpdate()
        .set(DocParseJobFieIds.jobStatus, status.getValue())
        .set(mergeNestedField(DocParseJobFieIds.jobRecords, nestedField), new Date());
  }

  private DocParseJob query(String jobId) {
    return dpsDataStore.get(DocParseJob.class, new ObjectId(jobId));
  }

  @Override
  public Optional<DocParseJob> queryJob(String jobId) {
    return Optional.ofNullable(query(jobId));
  }

  @Override
  public Optional<List<DocParseJob>> queryJobByUniqueIds(String uniqueIds) {
    Query<DocParseJob> query = getQuery();
    query.field(DocParseJobFieIds.uniqueIds).equal(uniqueIds);
    query.and(
        query.criteria(DocParseJobFieIds.jobStatus).notEqual(JobStatus.COMPLETED.getValue()),
        query.criteria(DocParseJobFieIds.jobStatus).notEqual(JobStatus.FAILED.getValue())
    );
    // 优先将创建时间早的任务排在前面
    query.order(DocParseJobFieIds.jobId);
    List<DocParseJob> docParseJobs = query.asList();
    if (docParseJobs != null && !docParseJobs.isEmpty()) {
      return Optional.of(docParseJobs);
    }
    return Optional.empty();
  }

  @Override
  public Optional<DocParseJob> idempotentJob(String uniqueIds,String asyncCallbackMqTag) {
    Optional<DocParseJob> docParseJobOptional = queryOriginalJob(uniqueIds);
    if (docParseJobOptional.isPresent()) {
      DocParseJob docParseJob = docParseJobOptional.get();
      DocParseJob newDocParseJob = copyAndCreate(docParseJob,asyncCallbackMqTag);
      return Optional.of(newDocParseJob);
    }
    return Optional.empty();
  }

  @Override
  public Optional<DocParseJob> queryCompleteJob(String jobId) {
    Query<DocParseJob> query = getCompleteJobQueryById(jobId);
    return Optional.ofNullable(query.get());
  }

  @Override
  public int deleteJob(String jobId) {
    Query<DocParseJob> query = getQuery();
    query.or(
        query.criteria(DocParseJobFieIds.jobId).equal(new ObjectId(jobId)),
        query.criteria(DocParseJobFieIds.idempotentJobId).equal(jobId)
    );
    WriteResult deleteResult = getDatastore().delete(query);
    return deleteResult.getN();
  }

  @Override
  public String create(JobParseArg jobParseArg) {
    DocParseJob docParseJob = new DocParseJob();
    docParseJob.setJobStatus(JobStatus.QUEUE.getValue());
    // 设置任务状态与任务记录
    docParseJob.setJobRecords(JobRecords.jobInit());
    // 设置任务参数
    docParseJob.setJobParseArg(jobParseArg);
    docParseJob.setUniqueIds(jobParseArg.getUniqueIds());

    Key<DocParseJob> saveResult = getDatastore().save(docParseJob);
    return saveResult.getId().toString();
  }

  @Override
  public void process(String jobId) {
    Query<DocParseJob> query = getQuery(jobId);
    UpdateOperations<DocParseJob> ops = getUpdateJobStatusOperation(JobStatus.PREPROCESS,
        DocParseJobFieIds.jobProcessStartTime);
    getDatastore().update(query, ops);
  }

  @Override
  public void complete(String jobId, String fileId, DocBillingRecord docBillingRecord) {
    Query<DocParseJob> query = getQuery(jobId);
    UpdateOperations<DocParseJob> ops = getUpdateJobStatusOperation(JobStatus.COMPLETED,
        DocParseJobFieIds.jobCompletionTime);
    ops.set(DocParseJobFieIds.fileID, fileId);
    ops.set(DocParseJobFieIds.docBillingRecord, docBillingRecord);
    getDatastore().update(query, ops);
  }

  @Override
  public void fail(String jobId, int jobFailCode, String jobFailReason) {
    Query<DocParseJob> query = getQuery(jobId);

    UpdateOperations<DocParseJob> ops = getUpdateJobStatusOperation(JobStatus.FAILED,
        DocParseJobFieIds.jobFailTime);

    if (StrUtils.notBlank(jobFailReason)) {
      ops.set(mergeNestedField(DocParseJobFieIds.jobRecords,
          DocParseJobFieIds.jobFailCode), jobFailCode);
      ops.set(mergeNestedField(DocParseJobFieIds.jobRecords,
          DocParseJobFieIds.jobFailReason), jobFailReason);
    }

    getDatastore().update(query, ops);
  }

  @Override
  public void billingReportDone(String jobId, boolean reportResult) {
    Query<DocParseJob> query = getQuery(jobId);
    UpdateOperations<DocParseJob> ops = getUpdate()
        .set(DocParseJobFieIds.billingReportDone, reportResult);
    getDatastore().update(query, ops);
  }

  private Optional<DocParseJob> queryOriginalJob(String uniqueIds) {
    Query<DocParseJob> query = getQuery();
    query.field(DocParseJobFieIds.uniqueIds).equal(uniqueIds);
    // 只查询非幂等任务
    query.field(DocParseJobFieIds.idempotent).equal(false);
    query.order(DocParseJobFieIds.jobId);
    return Optional.ofNullable(query.get());
  }

  private DocParseJob copyAndCreate(DocParseJob docParseJob,String asyncCallbackMqTag) {
    DocParseJob newDocParseJob = new DocParseJob();
    newDocParseJob.setUniqueIds(docParseJob.getUniqueIds());
    newDocParseJob.setJobStatus(docParseJob.getJobStatus());

    // 重置任务提交记录(不重置处理时间)
    JobRecords jobRecords = docParseJob.getJobRecords();
    jobRecords.setJobSubmitTime(new Date());
    newDocParseJob.setJobRecords(jobRecords);

    // 复制任务参数，并设置异步回调的MQ标签(防止tag冲突)
    JobParseArg jobParseArg = docParseJob.getJobParseArg();
    jobParseArg.setAsyncCallbackMqTag(asyncCallbackMqTag);
    newDocParseJob.setJobParseArg(jobParseArg);

    newDocParseJob.setFileID(docParseJob.getFileID());
    newDocParseJob.setDocBillingRecord(docParseJob.getDocBillingRecord());
    // 标识为幂等任务
    newDocParseJob.setIdempotent(true);
    newDocParseJob.setIdempotentJobId(docParseJob.get_id().toString());
    Key<DocParseJob> saveResult = getDatastore().save(newDocParseJob);
    String newJobId = saveResult.getId().toString();
    newDocParseJob.set_id(new ObjectId(newJobId));
    return newDocParseJob;
  }

  // 删除库内所有数据
  @Override
  public int clearAllJobs() {
    Query<DocParseJob> query = getQuery();
    WriteResult delete = getDatastore().delete(query);
    return delete.getN();
  }

}
