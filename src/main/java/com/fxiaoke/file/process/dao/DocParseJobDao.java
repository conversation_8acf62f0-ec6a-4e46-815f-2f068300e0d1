package com.fxiaoke.file.process.dao;

import com.fxiaoke.file.process.domain.entity.DocParseJob;
import com.fxiaoke.file.process.domain.entity.model.DocBillingRecord;
import com.fxiaoke.file.process.domain.entity.model.JobParseArg;
import java.util.List;
import java.util.Optional;

public interface DocParseJobDao {

  Optional<DocParseJob> queryJob(String jobId);

  Optional<List<DocParseJob>> queryJobByUniqueIds(String uniqueIds);

  Optional<DocParseJob> idempotentJob(String uniqueIds,String asyncCallbackMqTag);

  String create(JobParseArg jobParseArg);

  void process(String jobId);

  void complete(String jobId, String fileId, DocBillingRecord docBillingRecord);

  void fail(String jobId, int jobFailCode, String jobFailReason);

  void billingReportDone(String jobId, boolean reportResult);

  Optional<DocParseJob> queryCompleteJob(String jobId);

  int deleteJob(String jobId);

  // 删除库内所有数据
  int clearAllJobs();
}
