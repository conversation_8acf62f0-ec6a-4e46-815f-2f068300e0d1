package com.fxiaoke.file.process;

import com.fxiaoke.file.process.domain.constants.Constants;
import com.fxiaoke.file.process.domain.exception.BaseException;
import java.io.File;
import java.io.IOException;
import java.util.Locale;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

@Service
@Slf4j(topic = "Initialize")
public class AsposeInit implements ApplicationRunner {

  private static final String MODULE = "InitializeService";
  private static final String LIC = "Aspose.Total.Java.lic";

  private static final String[] asposeCache = {
      Constants.WORD_CACHE_FOLDER,
      Constants.CELLS_CACHE_FOLDER,
      Constants.SLIDES_CACHE_FOLDER,
      Constants.PDF_CACHE_FOLDER
  };

  @Override
  public void run(ApplicationArguments args) {
    init();
  }

  public static void init(){
    try {
      initLocaleLanguage();
      registerAsposeLicense();
      initAsposeCacheFolder();
    } catch (Exception e) {
      throw new BaseException(e,500,MODULE+"-AsposeInit","Aspose registerLicense fail"+LIC);
    }
  }

  /**
   * 初始化本地化语言
   */
  private static void initLocaleLanguage() {
    // 使用 Locale.Builder 构建 Locale 实例
    Locale locale = new Locale.Builder()
        .setLanguage("zh")
        .setRegion("CN").build();
    // 设置默认 Locale
    Locale.setDefault(locale);
  }

  /**
   * Aspose License authentication
   */
  private static void registerAsposeLicense() throws Exception {
    log.info("License authentication Start, LicenseName:{}", LIC);
    ClassPathResource resource = new ClassPathResource(LIC);

    com.aspose.slides.License pptLicense = new com.aspose.slides.License();
    pptLicense.setLicense(resource.getInputStream());
    com.aspose.pdf.License pdfLicense = new com.aspose.pdf.License();
    pdfLicense.setLicense(resource.getInputStream());
    com.aspose.cells.License xlsLicense = new com.aspose.cells.License();
    xlsLicense.setLicense(resource.getInputStream());
    com.aspose.words.License docLicense = new com.aspose.words.License();
    docLicense.setLicense(resource.getInputStream());
    com.aspose.html.License htmlLicense = new com.aspose.html.License();
    htmlLicense.setLicense(resource.getInputStream());

    resource.getInputStream().close();

    log.info("License authentication End, LicenseName:{}", LIC);
  }

  /**
   * Aspose cacheFolder init
   */
  private static void initAsposeCacheFolder() throws IOException {
    for (String cacheFolder : asposeCache) {
      File file = new File(cacheFolder);
      if (!file.exists()) {
        FileUtils.forceMkdir(file);
        log.info("cache Folder not exist, create cache Folder:{}", file.getPath());
      } else {
        FileUtils.cleanDirectory(file);
        log.info("cache Folder exist, clean cache Folder:{}", file.getPath());
      }
    }
  }
}
